import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/post_model.dart';
import 'supabase_service.dart';

class PostsService {
  static PostsService? _instance;
  static PostsService get instance => _instance ??= PostsService._();
  
  PostsService._();
  
  SupabaseClient get _client => SupabaseService.instance.client;
  
  /// Fetch posts with user information
  Future<List<PostModel>> getPosts({
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      // Query posts with user information using a join
      final response = await _client
          .from('posts')
          .select('''
            id,
            user_id,
            channel_id,
            content,
            media_url,
            media_type,
            like_count,
            comment_count,
            is_active,
            created_at,
            updated_at,
            users!inner(
              username,
              display_name,
              avatar_url
            )
          ''')
          .eq('is_active', true)
          .order('created_at', ascending: false)
          .range(offset, offset + limit - 1);

      final List<dynamic> data = response as List<dynamic>;
      
      return data.map((json) {
        // Flatten the user data
        final userInfo = json['users'] as Map<String, dynamic>?;
        final flattenedJson = Map<String, dynamic>.from(json);
        
        if (userInfo != null) {
          flattenedJson['username'] = userInfo['username'];
          flattenedJson['display_name'] = userInfo['display_name'];
          flattenedJson['avatar_url'] = userInfo['avatar_url'];
        }
        
        // Remove the nested users object
        flattenedJson.remove('users');
        
        return PostModel.fromJson(flattenedJson);
      }).toList();
    } catch (e) {
      throw Exception('Failed to fetch posts: $e');
    }
  }

  /// Fetch posts for a specific user
  Future<List<PostModel>> getUserPosts(String userId, {
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      final response = await _client
          .from('posts')
          .select('''
            id,
            user_id,
            channel_id,
            content,
            media_url,
            media_type,
            like_count,
            comment_count,
            is_active,
            created_at,
            updated_at,
            users!inner(
              username,
              display_name,
              avatar_url
            )
          ''')
          .eq('user_id', userId)
          .eq('is_active', true)
          .order('created_at', ascending: false)
          .range(offset, offset + limit - 1);

      final List<dynamic> data = response as List<dynamic>;
      
      return data.map((json) {
        final userInfo = json['users'] as Map<String, dynamic>?;
        final flattenedJson = Map<String, dynamic>.from(json);
        
        if (userInfo != null) {
          flattenedJson['username'] = userInfo['username'];
          flattenedJson['display_name'] = userInfo['display_name'];
          flattenedJson['avatar_url'] = userInfo['avatar_url'];
        }
        
        flattenedJson.remove('users');
        
        return PostModel.fromJson(flattenedJson);
      }).toList();
    } catch (e) {
      throw Exception('Failed to fetch user posts: $e');
    }
  }

  /// Like a post
  Future<bool> likePost(String postId) async {
    try {
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Check if already liked
      final existingLike = await _client
          .from('likes')
          .select('id')
          .eq('user_id', currentUser.id)
          .eq('post_id', postId)
          .maybeSingle();

      if (existingLike != null) {
        // Unlike the post
        await _client
            .from('likes')
            .delete()
            .eq('user_id', currentUser.id)
            .eq('post_id', postId);

        // Decrement like count
        await _client.rpc('decrement_post_likes', params: {'post_id': postId});
        
        return false; // Unliked
      } else {
        // Like the post
        await _client.from('likes').insert({
          'user_id': currentUser.id,
          'post_id': postId,
        });

        // Increment like count
        await _client.rpc('increment_post_likes', params: {'post_id': postId});
        
        return true; // Liked
      }
    } catch (e) {
      throw Exception('Failed to like/unlike post: $e');
    }
  }

  /// Check if current user has liked a post
  Future<bool> hasLikedPost(String postId) async {
    try {
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) return false;

      final like = await _client
          .from('likes')
          .select('id')
          .eq('user_id', currentUser.id)
          .eq('post_id', postId)
          .maybeSingle();

      return like != null;
    } catch (e) {
      return false;
    }
  }

  /// Create a new post
  Future<PostModel?> createPost({
    required String content,
    String? mediaUrl,
    String? mediaType,
    String? channelId,
  }) async {
    try {
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      final response = await _client
          .from('posts')
          .insert({
            'user_id': currentUser.id,
            'content': content,
            'media_url': mediaUrl,
            'media_type': mediaType,
            'channel_id': channelId,
          })
          .select('''
            id,
            user_id,
            channel_id,
            content,
            media_url,
            media_type,
            like_count,
            comment_count,
            is_active,
            created_at,
            updated_at,
            users!inner(
              username,
              display_name,
              avatar_url
            )
          ''')
          .single();

      final userInfo = response['users'] as Map<String, dynamic>?;
      final flattenedJson = Map<String, dynamic>.from(response);
      
      if (userInfo != null) {
        flattenedJson['username'] = userInfo['username'];
        flattenedJson['display_name'] = userInfo['display_name'];
        flattenedJson['avatar_url'] = userInfo['avatar_url'];
      }
      
      flattenedJson.remove('users');
      
      return PostModel.fromJson(flattenedJson);
    } catch (e) {
      throw Exception('Failed to create post: $e');
    }
  }

  /// Delete a post
  Future<bool> deletePost(String postId) async {
    try {
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      await _client
          .from('posts')
          .update({'is_active': false})
          .eq('id', postId)
          .eq('user_id', currentUser.id);

      return true;
    } catch (e) {
      throw Exception('Failed to delete post: $e');
    }
  }
}
