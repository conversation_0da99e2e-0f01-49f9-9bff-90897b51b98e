//
//  EmailViewController.swift
//  GameFlex
//
//  Created by <PERSON> on 8/23/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import FirebaseAuth

class EmailViewController: GFViewController {
    
    @IBOutlet weak var headerView: UIView!
    @IBOutlet weak var logoView: LoginHeaderView!
    @IBOutlet weak var logoLabel: UILabel!
    @IBOutlet weak var tableView: UITableView!
    @IBOutlet weak var legalTextView: UITextView!
    @IBOutlet weak var signUpButton: UIButton!
    
    var emailText = ""
    var emailConfirmText = ""
    var passwordText = ""
    var passwordConfirmText = ""
        
    var topHeaderErrorText = ""
    var bottomHeaderErrorText = ""
    
    let placeholders: [[String: [String]]] = [["Details": ["Email", "Confirm Email"]],
                                               ["Security": ["Password", "Confirm Password", ""]]]
    
    var textArray: [String: String] = [:]
    
    let headerLabelTag = 9998211
    let headerCheckmarkTag = 9998212
    
    static func storyboardInstance() -> EmailViewController {
        let sb = UIStoryboard(name: "Main", bundle: nil)
        return sb.instantiateViewController(withIdentifier: String(describing: EmailViewController.self)) as! EmailViewController
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setBackgroundGradient()

        navigationController?.navigationBar.tintColor = .black
        let textAttributes = [NSAttributedString.Key.foregroundColor: UIColor.black]
        navigationController?.navigationBar.titleTextAttributes = textAttributes
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(UINib(nibName: TextFieldTableViewCell.cellIdentifier, bundle: nil), forCellReuseIdentifier: TextFieldTableViewCell.cellIdentifier)
        tableView.backgroundColor = .clear
        legalTextView.text = "email.legal".localized
        let tap = UITapGestureRecognizer(target: self, action: #selector(didTapView))
        view.addGestureRecognizer(tap)
        textArray = ["Email": emailText, "Confirm Email": emailConfirmText, "Password": passwordText, "Confirm Password": passwordConfirmText]
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        title = "Sign up"
        // completes the logoView
        let logo = UIImageView(frame: CGRect(x: 0, y: -2, width: 24, height: 24))
        logo.image = UIImage(named: "AppIcon")
        logo.layer.cornerRadius = 12
        logo.clipsToBounds = true
        logoView.addSubview(logo)
        let im2 = UIImage(named: "gameFlex")
        let name = UIImageView(frame: CGRect(x: 30, y: 0, width: 128, height: 20))
        name.image = im2
        logoView.addSubview(name)
        logoView.backgroundColor = .clear
        logoLabel.text = "email.alreadyAccount".localized
        logoLabel.textColor = .black
        let attr = NSMutableAttributedString(string: "email.alreadyAccount".localized)
        if let subStringRange = "email.alreadyAccount".localized.range(of: "email.alreadyAccountBolded".localized) {
            let nsRange = NSRange(subStringRange, in: "email.alreadyAccount".localized)
            attr.addAttributes([NSAttributedString.Key.font : UIFont.boldSystemFont(ofSize: 17),
                                NSAttributedString.Key.foregroundColor: UIColor.gfGreen],
                               range: nsRange)
            logoLabel.attributedText = attr
        }
        let butt = UIButton(frame: CGRect(x: 0, y: 0, width: headerView.frame.width, height: headerView.frame.height + 25))
        butt.addTarget(self, action: #selector(didTapAlreadyHasAccount), for: .touchUpInside)
        butt.backgroundColor = .clear
        butt.isUserInteractionEnabled = true
        headerView.addSubview(butt)
        
        // setup the signup button
        signUpButton.layer.cornerRadius = 8.0
        signUpButton.alpha = 0.3
        signUpButton.isUserInteractionEnabled = false
        signUpButton.backgroundColor = .gfGreen
        signUpButton.tintColor = .black
        signUpButton.setTitle("email.signup".localized, for: .normal)
        
        // textView
        legalTextView.text = "email.legal".localized
        legalTextView.textColor = .white
        let bttr = NSMutableAttributedString(string: "email.legal".localized)
        bttr.addAttribute(NSAttributedString.Key.foregroundColor, value: UIColor.white, range: NSRange(location: 0, length: "email.legal".localized.count))
        if let subStringRange = "email.legal".localized.range(of: "Privacy Policy") {
            let nsRange = NSRange(subStringRange, in: "email.legal".localized)
            bttr.addAttributes([NSAttributedString.Key.font : UIFont.boldSystemFont(ofSize: 12),
                                NSAttributedString.Key.foregroundColor: UIColor.gfGreen],
                               range: nsRange)
            if let subbStringRange = "email.legal".localized.range(of: "Terms of Service") {
                let nsRange1 = NSRange(subbStringRange, in: "email.legal".localized)
                bttr.addAttributes([NSAttributedString.Key.font : UIFont.boldSystemFont(ofSize: 12),
                                    NSAttributedString.Key.foregroundColor: UIColor.gfGreen],
                                   range: nsRange1)
            }
            legalTextView.attributedText = bttr
            legalTextView.textAlignment = .center
        }

        legalTextView.backgroundColor = .clear
        
    }
    
    @objc func didTapView() {
        view.endEditing(true)
    }
    
    @objc func didTapAlreadyHasAccount() {
        User.shouldGoToLoginOnPop = true
        navigationController?.popViewController(animated: true)
    }
    
    private func enableSignUpButton() {
        if emailText == emailConfirmText,
            Utilities.isValidEmail(emailText),
            Utilities.isValidPassword(passwordText),
            passwordText == passwordConfirmText {
            signUpButton.alpha = 1.0
            signUpButton.isUserInteractionEnabled = true
        } else {
            signUpButton.alpha = 0.3
            signUpButton.isUserInteractionEnabled = false
        }
    }
    
    // MARK: - SignUp Tapped
    // this button is not tapable if email is invalid or password is invalid
    @IBAction func didTapSignUpButton() {
        Utilities.showSpinner()
        let email = emailText
        let password = passwordText
        User.shouldGoToLoginOnPop = true
        User.authenticationService = .email
        self.createFirebaseUser(email: email, password: password) { (success, error) in
            guard error == nil else {
                
                let alert = UIAlertController(title: "Error", message: "Something unexpected happened.", preferredStyle: .alert)
                let ok = UIAlertAction(title: "Try Again", style: .default) {_ in
                    self.emailText = ""
                    self.passwordText = ""
                    self.emailConfirmText = ""
                    self.passwordConfirmText = ""
                    self.tableView.reloadData()
                    self.tableView.scrollToRow(at: IndexPath(row: 0, section: 0), at: .top, animated: true)
                }
                let login = UIAlertAction(title: "Login", style: .default) { _ in
                    self.didTapAlreadyHasAccount()
                }
                alert.addAction(ok)
                alert.addAction(login)
                DispatchQueue.main.async {
                    Utilities.hideSpinner()
                    self.present(alert, animated: true, completion: nil)
                }
                return
            }
            // successful execution is handled in the parent view controller
        }
    }
}

// MARK: - UITableViewDelegate, UITableViewDataSource

extension EmailViewController: UITableViewDataSource, UITableViewDelegate {
    
    func numberOfSections(in tableView: UITableView) -> Int {
        return 2
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        if section == 0 {
            return 2
        }
        return 3
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: TextFieldTableViewCell.cellIdentifier, for: indexPath) as? TextFieldTableViewCell else { return TextFieldTableViewCell() }
        let str = decomposeThePlaceholders(index: indexPath.section)[indexPath.row]
        if indexPath.section == 1 {
            cell.textField.isSecureTextEntry = true
            cell.textField.tag = indexPath.row + 10
            if indexPath.row == tableView.numberOfRows(inSection: 1) - 1 {
                cell.grayBox.isHidden = true
                cell.isUserInteractionEnabled = false
            } else {
                cell.grayBox.isHidden = false
                cell.isUserInteractionEnabled = true
                cell.textField.autocorrectionType = .no
            }
            cell.configureCell(str, password: true)
            switch indexPath.row { // section 1
            case 0: cell.textField.text = passwordText
            case 1: cell.textField.text = passwordConfirmText
            default: break
            }

        } else {
            cell.grayBox.isHidden = false
            cell.configureCell(str, password: false)
            cell.textField.isSecureTextEntry = false
            cell.textField.tag = indexPath.row
            cell.isUserInteractionEnabled = true
            cell.textField.autocorrectionType = .no
            switch indexPath.row { // section 0
            case 0: cell.textField.text = emailText
            case 1: cell.textField.text = emailConfirmText
            default: break
            }
        }
        if textArray[str] != "" {
            cell.textField.text = textArray[str]
        }
        cell.textField.autocapitalizationType = .none
        cell.backgroundColor = .clear
        cell.selectionStyle = .none
        cell.textField.delegate = self
        
        return cell
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 55
    }
    
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {

        let header = UIView(frame: CGRect(x: 0, y: 0, width: view.frame.size.width, height: 35))
        let label = UILabel(frame: CGRect(x: 16, y: 5, width: 250.0, height: 25))
        label.text = decomposePlaceholderHeader(index: section)
        label.font = .boldSystemFont(ofSize: 20)
        label.textColor = .white
        header.addSubview(label)
        
        header.subviews.filter({ $0.tag == self.headerLabelTag }).forEach({ $0.removeFromSuperview() })
        header.subviews.filter({ $0.tag == self.headerCheckmarkTag }).forEach({ $0.removeFromSuperview() })

        switch section {
        case 0:
            if Utilities.isValidEmail(emailText), emailText.count > 0 {
                if emailText != emailConfirmText {
                    topHeaderErrorText = "email.notMatching".localized
                } else {
                    topHeaderErrorText = ""
                    // add checkmark
                    let im = UIImageView(frame: CGRect(x: 95.0, y: 8.0, width: 17, height: 17))
                    im.image = #imageLiteral(resourceName: "circleCheck")
                    im.tag = headerCheckmarkTag
                    header.addSubview(im)
                }
            } else if emailText.count > 0 {
                topHeaderErrorText = "email.inValidEmail".localized
            } else {
                topHeaderErrorText = ""
            }
            if topHeaderErrorText != "" {
            let topErrorLabel = UILabel(frame: CGRect(x: 130.0, y: 0.0, width: view.frame.width - 140.0, height: 45.0))
                topErrorLabel.text = topHeaderErrorText.uppercased()
                topErrorLabel.font = .boldSystemFont(ofSize: 10)
                topErrorLabel.textColor = .gfYellow_F2DE76
                topErrorLabel.textAlignment = .left
                topErrorLabel.numberOfLines = 0
                topErrorLabel.tag = 9998211
                header.addSubview(topErrorLabel)
                let im = UIImageView(frame: CGRect(x: 100, y: 8.0, width: 20, height: 20))
                im.image = #imageLiteral(resourceName: "alertTriangle")
                im.tag = headerLabelTag
                header.addSubview(im)
            }
        case 1:
            if Utilities.isValidPassword(passwordText), passwordText.count > 0 {
                if passwordText != passwordConfirmText {
                    bottomHeaderErrorText = "email.notMatching".localized
                } else {
                    bottomHeaderErrorText = ""
                    //add checkmark
                    let im = UIImageView(frame: CGRect(x: 95.0, y: 8.0, width: 17, height: 17))
                    im.image = #imageLiteral(resourceName: "circleCheck")
                    im.tag = headerCheckmarkTag
                    header.addSubview(im)
                }
            } else  if passwordText.count > 0 {
                bottomHeaderErrorText = "email.inValidPassword".localized
            } else {
                bottomHeaderErrorText = ""
            }
            if bottomHeaderErrorText != "" {
                let bottomErrorLabel = UILabel(frame: CGRect(x: 122.0, y: 0.0, width: view.frame.width - 140.0, height: 45.0))
                bottomErrorLabel.text = bottomHeaderErrorText.uppercased()
                bottomErrorLabel.font = .boldSystemFont(ofSize: 10)
                bottomErrorLabel.textColor = .gfYellow_F2DE76
                bottomErrorLabel.textAlignment = .left
                bottomErrorLabel.numberOfLines = 0
                bottomErrorLabel.tag = 9998211
                header.addSubview(bottomErrorLabel)
                let im = UIImageView(frame: CGRect(x: 100, y: 8.0, width: 20, height: 20))
                im.image = #imageLiteral(resourceName: "alertTriangle")
                im.tag = headerLabelTag
                header.addSubview(im)
            }
        default: break
        }
        
        return header
    }
    
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return 35
    }
    
    // MARK: - tableView helpers
    func decomposeThePlaceholders(index: Int) -> [String] {
        let values = placeholders[index].values
        var arr: [String] = []
        for val in values {
            arr.append(contentsOf: val)
        }
        return arr
    }
    
    func decomposePlaceholderHeader(index: Int) -> String {
        let keys = placeholders[index].keys
        
        return keys.first ?? ""
    }
}

extension EmailViewController: UITextFieldDelegate {
    
    func textFieldDidBeginEditing(_ textField: UITextField) {
        let section = textField.tag < 9 ? 0 : 1
        let row = textField.tag < 9 ? textField.tag : textField.tag - 10
        guard let cell = tableView.cellForRow(at: IndexPath(row: row, section: section)) as? TextFieldTableViewCell else { return }
        cell.hideOrShowTheClearButton(tag: textField.tag)
    }
    
    func textFieldDidEndEditing(_ textField: UITextField) {
        let section = textField.tag < 10 ? 0 : 1
        let row = textField.tag < 10 ? textField.tag : textField.tag - 10
        guard let cell = tableView.cellForRow(at: IndexPath(row: row, section: section)) as? TextFieldTableViewCell else { return }
        cell.hideOrShowTheClearButton(tag: textField.tag)
        let str = textField.text?.trim()
        textField.text = str
        enableSignUpButton()
        tableView.reloadData()
    }
    
    func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        guard string != "\n" else {
            return false
        }
        
        let result = (textField.text as NSString?)?.replacingCharacters(in: range, with: string) ?? string
        switch textField.tag {
        case 0:
            emailText = result
        case 1:
            emailConfirmText = result
        case 10: // security
            passwordText = result
        case 11:
            passwordConfirmText = result
        default: break
        }
        enableSignUpButton()
        return true
    }
    
    
}
