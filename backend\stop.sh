#!/bin/bash

# GameFlex Backend Stop Script
# This script stops the Supabase development environment

set -e

# Function to show help
show_help() {
    echo "GameFlex Backend Stop Script"
    echo "Usage: ./stop.sh [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --remove-data    Also remove all data volumes (WARNING: This will delete all data!)"
    echo "  --help          Show this help message"
    echo ""
    echo "Examples:"
    echo "  ./stop.sh                    # Stop services but keep data"
    echo "  ./stop.sh --remove-data      # Stop services and remove all data"
}

# Parse command line arguments
REMOVE_DATA=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --remove-data)
            REMOVE_DATA=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

echo "🛑 Stopping GameFlex Development Backend..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Services may already be stopped."
    exit 0
fi

# Check if docker-compose.yml exists
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ docker-compose.yml not found. Make sure you're in the backend directory."
    exit 1
fi

if [ "$REMOVE_DATA" = true ]; then
    echo "⚠️  WARNING: This will remove all data including database, storage, and logs!"
    read -p "Are you sure you want to continue? Type 'yes' to confirm: " confirmation

    if [ "$confirmation" = "yes" ]; then
        echo "🗑️  Stopping containers and removing volumes..."
        docker-compose down -v
        echo "✅ All services stopped and data removed!"
    else
        echo "❌ Operation cancelled."
        exit 0
    fi
else
    echo "🐳 Stopping Docker containers..."
    docker-compose down
    echo "✅ All services stopped!"
fi

echo ""
echo "🔧 Next Steps:"
echo "   To start again: ./start.sh"

if [ "$REMOVE_DATA" = false ]; then
    echo "   To remove all data: ./stop.sh --remove-data"
fi

echo "   To view logs: docker-compose logs"
echo ""
