//
//  FlareEmptyCollectionViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 7/18/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

class FlareEmptyCollectionViewCell: UICollectionViewCell {
    
    @IBOutlet weak var label: UILabel!
    
    static var cellIdentifier = String(describing: FlareEmptyCollectionViewCell.self)
    
    override func awakeFromNib() {
        super.awakeFromNib()
        label.textColor = .gfGrayText
    }
}
