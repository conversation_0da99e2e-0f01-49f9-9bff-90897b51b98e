//
//  CreateChannelPictureTableViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 11/14/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import Kingfisher

class CreateChannelPictureTableViewCell: UITableViewCell {
    
    @IBOutlet weak var imageSuperView: UIView!
    @IBOutlet weak var editButton: UIButton!
    @IBOutlet weak var profileImageView: UIImageView!
    @IBOutlet weak var changeColorButton: UIButton!
    @IBOutlet weak var editLabelOnlyButton: UIButton!
    
    weak var delegate: ProfileDelegate?
    
    var channel: Channel?
    
    static var cellIdentifier = String(describing: CreateChannelPictureTableViewCell.self)
    
    override func awakeFromNib() {
        super.awakeFromNib()
        selectionStyle = .none
        // images and font presentations
        imageSuperView.layer.cornerRadius = 35.0
        imageSuperView.clipsToBounds = true
        configureCell()
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        profileImageView.image = nil
        configureCell()
    }
    
    func configureCell() {
        let image = UIImage(named: "channelMask")
        
        // placeholder smilie
        profileImageView.image = image
        if let pic = channel?.channelImage, pic != "" {
            let url = URL(string: pic)
            let processor = DownsamplingImageProcessor(size: profileImageView.bounds.size)
            profileImageView.kf.indicatorType = .activity
            profileImageView.kf.setImage(
                with: url,
                placeholder: UIImage(named: "Layer 6"), //FlexManager.randomImagePlaceholder()),
                options: [
                    .processor(processor),
                    .scaleFactor(UIScreen.main.scale),
                    .transition(.fade(1)),
                    .cacheOriginalImage
                ], completionHandler:
                    {
                        result in
                        switch result {
                        case .success: break
                        case .failure(let error):
                            print("Job failed: \(error.localizedDescription)")
                            self.profileImageView.image = image
                        }
                    })
        }
        
        imageSuperView.layer.borderWidth = 1
        imageSuperView.layer.borderColor = UIColor.gfBlue_00D5FF.cgColor
        imageSuperView.clipsToBounds = true

    }
    
    @IBAction func didTap(_ sender: UIButton) {
        if sender == changeColorButton {
            delegate?.didTapForProfileAction(.changeBackgroundChannelColor, nil)
            return
        }
        
        // add a photo or take a pic
        delegate?.didTapForProfileAction(.photo, nil)
        
    }
}
