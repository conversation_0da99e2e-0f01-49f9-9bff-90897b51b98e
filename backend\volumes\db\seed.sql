-- GameFlex Development Seed Data
-- This script inserts initial development data

-- Set search path
SET search_path TO gameflex, public;

-- Insert development users
INSERT INTO gameflex.users (id, email, username, display_name, bio, is_verified, is_active) VALUES
(
    '********-0000-0000-0000-********0001',
    '<EMAIL>',
    'devuser',
    'Development User',
    'This is a development user account for testing purposes.',
    true,
    true
),
(
    '********-0000-0000-0000-********0002',
    '<EMAIL>',
    'admin',
    'Admin User',
    'Administrator account for GameFlex development.',
    true,
    true
),
(
    '********-0000-0000-0000-********0003',
    '<EMAIL>',
    'johndoe',
    '<PERSON>',
    'Gaming enthusiast and content creator.',
    true,
    true
),
(
    '********-0000-0000-0000-********0004',
    '<EMAIL>',
    'jane<PERSON>',
    '<PERSON>',
    'Professional gamer and streamer.',
    true,
    true
),
(
    '********-0000-0000-0000-********0005',
    '<EMAIL>',
    'mike<PERSON><PERSON>',
    '<PERSON>',
    '<PERSON>asual gamer who loves sharing gaming moments.',
    true,
    true
)
ON CONFLICT (email) DO NOTHING;

-- Insert user profiles
INSERT INTO gameflex.user_profiles (user_id, first_name, last_name, country, language, preferences) VALUES
(
    '********-0000-0000-0000-********0001',
    'Dev',
    'User',
    'United States',
    'en',
    '{"theme": "dark", "notifications": {"email": true, "push": true}}'
),
(
    '********-0000-0000-0000-********0002',
    'Admin',
    'User',
    'United States',
    'en',
    '{"theme": "dark", "notifications": {"email": true, "push": true}}'
),
(
    '********-0000-0000-0000-********0003',
    'John',
    'Doe',
    'United States',
    'en',
    '{"theme": "light", "notifications": {"email": true, "push": false}}'
),
(
    '********-0000-0000-0000-********0004',
    'Jane',
    'Smith',
    'Canada',
    'en',
    '{"theme": "dark", "notifications": {"email": false, "push": true}}'
),
(
    '********-0000-0000-0000-********0005',
    'Mike',
    'Wilson',
    'United Kingdom',
    'en',
    '{"theme": "auto", "notifications": {"email": true, "push": true}}'
)
ON CONFLICT (user_id) DO NOTHING;

-- Insert development channels
INSERT INTO gameflex.channels (id, name, description, owner_id, is_public, member_count) VALUES
(
    '10000000-0000-0000-0000-********0001',
    'General Gaming',
    'A place to discuss all things gaming',
    '********-0000-0000-0000-********0001',
    true,
    5
),
(
    '10000000-0000-0000-0000-********0002',
    'Mobile Gaming',
    'Share your favorite mobile gaming moments',
    '********-0000-0000-0000-********0003',
    true,
    3
),
(
    '10000000-0000-0000-0000-********0003',
    'Competitive Gaming',
    'For serious gamers and esports enthusiasts',
    '********-0000-0000-0000-********0004',
    true,
    2
),
(
    '10000000-0000-0000-0000-********0004',
    'Indie Games',
    'Discover and share amazing indie games',
    '********-0000-0000-0000-********0005',
    true,
    4
),
(
    '10000000-0000-0000-0000-********0005',
    'GameFlex Development',
    'Private channel for GameFlex development team',
    '********-0000-0000-0000-********0002',
    false,
    2
)
ON CONFLICT (id) DO NOTHING;

-- Insert channel memberships
INSERT INTO gameflex.channel_members (channel_id, user_id, role) VALUES
-- General Gaming channel
('10000000-0000-0000-0000-********0001', '********-0000-0000-0000-********0001', 'owner'),
('10000000-0000-0000-0000-********0001', '********-0000-0000-0000-********0002', 'admin'),
('10000000-0000-0000-0000-********0001', '********-0000-0000-0000-********0003', 'member'),
('10000000-0000-0000-0000-********0001', '********-0000-0000-0000-********0004', 'member'),
('10000000-0000-0000-0000-********0001', '********-0000-0000-0000-********0005', 'member'),

-- Mobile Gaming channel
('10000000-0000-0000-0000-********0002', '********-0000-0000-0000-********0003', 'owner'),
('10000000-0000-0000-0000-********0002', '********-0000-0000-0000-********0001', 'member'),
('10000000-0000-0000-0000-********0002', '********-0000-0000-0000-********0005', 'member'),

-- Competitive Gaming channel
('10000000-0000-0000-0000-********0003', '********-0000-0000-0000-********0004', 'owner'),
('10000000-0000-0000-0000-********0003', '********-0000-0000-0000-********0003', 'member'),

-- Indie Games channel
('10000000-0000-0000-0000-********0004', '********-0000-0000-0000-********0005', 'owner'),
('10000000-0000-0000-0000-********0004', '********-0000-0000-0000-********0001', 'member'),
('10000000-0000-0000-0000-********0004', '********-0000-0000-0000-********0003', 'member'),
('10000000-0000-0000-0000-********0004', '********-0000-0000-0000-********0004', 'member'),

-- GameFlex Development channel (private)
('10000000-0000-0000-0000-********0005', '********-0000-0000-0000-********0002', 'owner'),
('10000000-0000-0000-0000-********0005', '********-0000-0000-0000-********0001', 'admin')
ON CONFLICT (channel_id, user_id) DO NOTHING;

-- Insert sample posts
INSERT INTO gameflex.posts (id, user_id, channel_id, content, media_type, like_count, comment_count) VALUES
(
    '20000000-0000-0000-0000-********0001',
    '********-0000-0000-0000-********0003',
    '10000000-0000-0000-0000-********0001',
    'Just finished an amazing gaming session! What games are you all playing this weekend?',
    null,
    5,
    2
),
(
    '20000000-0000-0000-0000-********0002',
    '********-0000-0000-0000-********0004',
    '10000000-0000-0000-0000-********0003',
    'New tournament starting next week! Who''s ready to compete?',
    null,
    8,
    3
),
(
    '20000000-0000-0000-0000-********0003',
    '********-0000-0000-0000-********0005',
    '10000000-0000-0000-0000-********0004',
    'Found this incredible indie game that you all need to try!',
    null,
    12,
    4
),
(
    '20000000-0000-0000-0000-********0004',
    '********-0000-0000-0000-********0001',
    '10000000-0000-0000-0000-********0002',
    'Mobile gaming has come so far! Check out this gameplay.',
    'video',
    15,
    6
),
(
    '20000000-0000-0000-0000-********0005',
    '********-0000-0000-0000-********0002',
    '10000000-0000-0000-0000-********0005',
    'Working on some exciting new features for GameFlex!',
    null,
    3,
    1
),
(
    '20000000-0000-0000-0000-********0006',
    '********-0000-0000-0000-********0003',
    '10000000-0000-0000-0000-********0001',
    'Epic boss fight last night! Took us 3 hours but we finally got him down! 🎮⚔️',
    'image',
    23,
    8
),
(
    '20000000-0000-0000-0000-********0007',
    '********-0000-0000-0000-********0004',
    '10000000-0000-0000-0000-********0002',
    'Streaming live now! Come watch me attempt this impossible speedrun 🏃‍♂️💨',
    'video',
    45,
    12
),
(
    '20000000-0000-0000-0000-********0008',
    '********-0000-0000-0000-********0005',
    '10000000-0000-0000-0000-********0003',
    'Just discovered this hidden gem of a game. The art style is absolutely stunning! 🎨',
    'image',
    18,
    5
),
(
    '20000000-0000-0000-0000-********0009',
    '********-0000-0000-0000-********0001',
    '10000000-0000-0000-0000-********0004',
    'GameFlex community is growing so fast! Thank you all for being part of this journey 🚀',
    null,
    67,
    15
),
(
    '20000000-0000-0000-0000-********0010',
    '********-0000-0000-0000-********0002',
    '10000000-0000-0000-0000-********0005',
    'New patch notes are out! Lots of exciting changes coming to your favorite games 📝',
    null,
    34,
    9
),
(
    '20000000-0000-0000-0000-********0011',
    '********-0000-0000-0000-********0003',
    '10000000-0000-0000-0000-********0001',
    'Retro gaming night! Playing some classics from the 90s. What''s your favorite retro game? 🕹️',
    'image',
    29,
    11
),
(
    '20000000-0000-0000-0000-********0012',
    '********-0000-0000-0000-********0004',
    '10000000-0000-0000-0000-********0002',
    'VR gaming is mind-blowing! Just tried the new space exploration game 🚀🌌',
    'video',
    41,
    7
),
(
    '20000000-0000-0000-0000-********0013',
    '********-0000-0000-0000-********0005',
    '10000000-0000-0000-0000-********0003',
    'Cozy gaming session with some hot chocolate ☕ Perfect way to spend a rainy evening',
    'image',
    22,
    6
),
(
    '20000000-0000-0000-0000-********0014',
    '********-0000-0000-0000-********0001',
    '10000000-0000-0000-0000-********0004',
    'Beta testing some amazing new features! Can''t wait for you all to try them 🎯',
    null,
    38,
    13
),
(
    '20000000-0000-0000-0000-********0015',
    '********-0000-0000-0000-********0002',
    '10000000-0000-0000-0000-********0005',
    'Game development tip: Always playtest with real users! Their feedback is invaluable 💡',
    null,
    25,
    4
)
ON CONFLICT (id) DO NOTHING;

-- Insert sample comments
INSERT INTO gameflex.comments (post_id, user_id, content, like_count) VALUES
('20000000-0000-0000-0000-********0001', '********-0000-0000-0000-********0004', 'I''m playing the new RPG that just came out!', 2),
('20000000-0000-0000-0000-********0001', '********-0000-0000-0000-********0005', 'Same here! It''s incredible.', 1),
('20000000-0000-0000-0000-********0002', '********-0000-0000-0000-********0003', 'Count me in! When do registrations open?', 3),
('20000000-0000-0000-0000-********0002', '********-0000-0000-0000-********0005', 'I''ve been practicing for weeks!', 2),
('20000000-0000-0000-0000-********0002', '********-0000-0000-0000-********0001', 'This is going to be epic!', 1),
('20000000-0000-0000-0000-********0003', '********-0000-0000-0000-********0001', 'What''s the name of the game?', 4),
('20000000-0000-0000-0000-********0003', '********-0000-0000-0000-********0003', 'I love discovering new indie games!', 2),
('20000000-0000-0000-0000-********0003', '********-0000-0000-0000-********0004', 'Thanks for the recommendation!', 1),
('20000000-0000-0000-0000-********0003', '********-0000-0000-0000-********0002', 'Adding this to my wishlist!', 1),
('20000000-0000-0000-0000-********0004', '********-0000-0000-0000-********0003', 'Mobile gaming is the future!', 3),
('20000000-0000-0000-0000-********0004', '********-0000-0000-0000-********0004', 'The graphics look amazing!', 2),
('20000000-0000-0000-0000-********0004', '********-0000-0000-0000-********0005', 'Can''t wait to try this game!', 1),
('20000000-0000-0000-0000-********0004', '********-0000-0000-0000-********0002', 'Great content as always!', 2),
('20000000-0000-0000-0000-********0005', '********-0000-0000-0000-********0001', 'Excited to see what''s coming!', 1)
ON CONFLICT DO NOTHING;

-- Insert sample follows
INSERT INTO gameflex.follows (follower_id, following_id) VALUES
('********-0000-0000-0000-********0001', '********-0000-0000-0000-********0003'),
('********-0000-0000-0000-********0001', '********-0000-0000-0000-********0004'),
('********-0000-0000-0000-********0001', '********-0000-0000-0000-********0005'),
('********-0000-0000-0000-********0003', '********-0000-0000-0000-********0001'),
('********-0000-0000-0000-********0003', '********-0000-0000-0000-********0004'),
('********-0000-0000-0000-********0004', '********-0000-0000-0000-********0001'),
('********-0000-0000-0000-********0004', '********-0000-0000-0000-********0003'),
('********-0000-0000-0000-********0004', '********-0000-0000-0000-********0005'),
('********-0000-0000-0000-********0005', '********-0000-0000-0000-********0001'),
('********-0000-0000-0000-********0005', '********-0000-0000-0000-********0003'),
('********-0000-0000-0000-********0005', '********-0000-0000-0000-********0004')
ON CONFLICT (follower_id, following_id) DO NOTHING;
