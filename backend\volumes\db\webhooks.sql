-- Webhooks setup for Supabase
-- This script sets up the webhooks functionality

-- Create supabase_functions schema
CREATE SCHEMA IF NOT EXISTS supabase_functions;

-- Create http extension if not exists
CREATE EXTENSION IF NOT EXISTS http WITH SCHEMA extensions;

-- Create webhook function
CREATE OR REPLACE FUNCTION supabase_functions.http_request()
RETURNS trigger
LANGUAGE plpgsql
AS $$
DECLARE
  request_id bigint;
  payload jsonb;
  url text := TG_ARGV[0];
  method text := TG_ARGV[1];
  headers jsonb DEFAULT '{}'::jsonb;
  params jsonb DEFAULT '{}'::jsonb;
  timeout_ms integer DEFAULT 1000;
BEGIN
  IF url IS NULL OR url = 'null' THEN
    RAISE EXCEPTION 'url argument is missing';
  END IF;

  IF method IS NULL OR method = 'null' THEN
    method := 'POST';
  END IF;

  IF TG_ARGV[2] IS NOT NULL AND TG_ARGV[2] != 'null' THEN
    headers := TG_ARGV[2]::jsonb;
  END IF;

  IF TG_ARGV[3] IS NOT NULL AND TG_ARGV[3] != 'null' THEN
    params := TG_ARGV[3]::jsonb;
  END IF;

  IF TG_ARGV[4] IS NOT NULL AND TG_ARGV[4] != 'null' THEN
    timeout_ms := TG_ARGV[4]::integer;
  END IF;

  CASE
    WHEN TG_OP = 'INSERT' THEN
      payload := jsonb_build_object(
        'type', 'INSERT',
        'table', TG_TABLE_NAME,
        'schema', TG_TABLE_SCHEMA,
        'record', row_to_json(NEW),
        'old_record', null
      );
    WHEN TG_OP = 'UPDATE' THEN
      payload := jsonb_build_object(
        'type', 'UPDATE',
        'table', TG_TABLE_NAME,
        'schema', TG_TABLE_SCHEMA,
        'record', row_to_json(NEW),
        'old_record', row_to_json(OLD)
      );
    WHEN TG_OP = 'DELETE' THEN
      payload := jsonb_build_object(
        'type', 'DELETE',
        'table', TG_TABLE_NAME,
        'schema', TG_TABLE_SCHEMA,
        'record', null,
        'old_record', row_to_json(OLD)
      );
    ELSE
      RAISE EXCEPTION 'Unknown TG_OP: %', TG_OP;
  END CASE;

  -- Set default headers
  headers := jsonb_build_object(
    'Content-Type', 'application/json',
    'User-Agent', 'Supabase/Webhooks'
  ) || headers;

  SELECT INTO request_id extensions.http_post(
    url,
    payload::text,
    headers
  );

  INSERT INTO supabase_functions.hooks
    (hook_table_id, hook_name, request_id)
  VALUES
    (TG_RELID, TG_NAME, request_id);

  CASE
    WHEN TG_OP = 'INSERT' THEN
      RETURN NEW;
    WHEN TG_OP = 'UPDATE' THEN
      RETURN NEW;
    WHEN TG_OP = 'DELETE' THEN
      RETURN OLD;
    ELSE
      RETURN NULL;
  END CASE;
END;
$$;

-- Create hooks table
CREATE TABLE IF NOT EXISTS supabase_functions.hooks (
  id bigserial PRIMARY KEY,
  hook_table_id integer NOT NULL,
  hook_name text NOT NULL,
  request_id bigint,
  created_at timestamptz NOT NULL DEFAULT now()
);

-- Grant permissions
GRANT USAGE ON SCHEMA supabase_functions TO anon, authenticated, service_role;
GRANT ALL ON ALL TABLES IN SCHEMA supabase_functions TO anon, authenticated, service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA supabase_functions TO anon, authenticated, service_role;
