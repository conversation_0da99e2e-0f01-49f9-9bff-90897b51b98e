<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="16097.2" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="16087"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="LoginHeaderView" customModule="GameFlex" customModuleProvider="target"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="3tQ-oY-f8x" customClass="LoginHeaderView" customModule="GameFlex" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="162" height="24"/>
            <subviews>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="appstore" translatesAutoresizingMaskIntoConstraints="NO" id="MBP-rw-Vsn">
                    <rect key="frame" x="0.0" y="0.0" width="24" height="24"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="24" id="Ydf-PU-afI"/>
                        <constraint firstAttribute="height" constant="24" id="ZQj-qp-Mp1"/>
                    </constraints>
                </imageView>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="gameFlex" translatesAutoresizingMaskIntoConstraints="NO" id="7E1-f0-EeQ">
                    <rect key="frame" x="35" y="0.0" width="128" height="24"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="24" id="PYo-ks-mIe"/>
                        <constraint firstAttribute="width" constant="128" id="gAw-ed-bHa"/>
                    </constraints>
                </imageView>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="7E1-f0-EeQ" firstAttribute="leading" secondItem="MBP-rw-Vsn" secondAttribute="trailing" constant="11" id="7SU-MV-0SJ"/>
                <constraint firstItem="7E1-f0-EeQ" firstAttribute="top" secondItem="3tQ-oY-f8x" secondAttribute="top" id="85X-Su-u6t"/>
                <constraint firstItem="MBP-rw-Vsn" firstAttribute="top" secondItem="3tQ-oY-f8x" secondAttribute="top" id="BL5-i6-8vp"/>
                <constraint firstAttribute="height" constant="24" id="Py4-5W-Dp7"/>
                <constraint firstAttribute="bottom" secondItem="7E1-f0-EeQ" secondAttribute="bottom" id="R9c-PW-1i2"/>
                <constraint firstItem="MBP-rw-Vsn" firstAttribute="leading" secondItem="3tQ-oY-f8x" secondAttribute="leading" id="SU0-c7-UhX"/>
                <constraint firstItem="7E1-f0-EeQ" firstAttribute="leading" secondItem="MBP-rw-Vsn" secondAttribute="trailing" constant="11" id="V4D-w8-s6J"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <viewLayoutGuide key="safeArea" id="236-Bh-PTT"/>
            <connections>
                <outlet property="logoImageView" destination="MBP-rw-Vsn" id="pPe-rR-L2q"/>
                <outlet property="logoLabel" destination="7E1-f0-EeQ" id="5xf-Cr-VuJ"/>
            </connections>
            <point key="canvasLocation" x="-42.028985507246382" y="-309.375"/>
        </view>
    </objects>
    <resources>
        <image name="appstore" width="1024" height="1024"/>
        <image name="gameFlex" width="127" height="19"/>
    </resources>
</document>
