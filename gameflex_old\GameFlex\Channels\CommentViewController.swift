//
//  CommentViewController.swift
//  GameFlex
//
//  Created by <PERSON> on 10/26/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

class CommentViewController: GFViewController {
    
    @IBOutlet weak var tableView: UITableView!
    @IBOutlet weak var tableViewBottomConstraint: NSLayoutConstraint!
    
    var commentsArray: [Comment] = []
    var flexId: String?
    var newCommentText: String = ""
    var isFromReflex: Bool = false
    var keyboardHeight: CGFloat = 0.0
    
    weak var delegate: HomeDelegate?
    
    var count: Int?
    

    static func storyboardInstance() -> CommentViewController {
        let sb = UIStoryboard(name: "Main", bundle: nil)
        return sb.instantiateViewController(withIdentifier: String(describing: CommentViewController.self)) as! CommentViewController
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .gfDarkGrayMask
        tableView.delegate = self
        tableView.dataSource = self
        tableView.tableFooterView = UIView()
        tableView.register((UINib(nibName: CommentTableViewCell.cellIdentifier, bundle: nil)), forCellReuseIdentifier: CommentTableViewCell.cellIdentifier)
        tableView.register(UINib(nibName: EditProfileTextViewTableViewCell.cellIdentifier, bundle: nil), forCellReuseIdentifier: EditProfileTextViewTableViewCell.cellIdentifier)
        tableView.register(UINib(nibName: EditProfileButtonTableViewCell.cellIdentifier, bundle: nil), forCellReuseIdentifier: EditProfileButtonTableViewCell.cellIdentifier)
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(keyboardWillShow(_:)),
                                               name: UIResponder.keyboardWillShowNotification,
                                               object: nil)
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(keyboardDidShow(_:)),
                                               name: UIResponder.keyboardDidShowNotification,
                                               object: nil)
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(keyboardWillHide(_:)),
                                               name: UIResponder.keyboardWillHideNotification,
                                               object: nil)
        hideKeyboardWhenTappedAround()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        if let flexId = flexId, flexId != "" {
            retrieveTheComments(flexId) { (success, results, error) in
                DispatchQueue.main.async {
                    self.tableView.reloadData()
                }
            }
        }
        title = "Comments"
        addCancelToNavBar()
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        view.endEditing(true)
        if let count = count {
            var object = SideBarObject()
            object.buttonType = .comment
            object.buttonCount = count
            var object1 = SideBarObject()
            object1.buttonType = .reflex
            object1.restartTimers = true
            DispatchQueue.main.async {
                self.delegate?.updateSideBar(objects: [object, object1])
            }
        }
    }
    
    @objc func refreshContent() {
        tableView.refreshControl?.beginRefreshing()
        if let flexId = flexId, flexId != "" {
            if !commentsArray.isEmpty, let dated = commentsArray[0].timeStamp {
                retrieveTheComments(flexId, dated) { (success, array, error ) in
                    DispatchQueue.main.async {
                        self.tableView.refreshControl?.endRefreshing()
                    }
                    if success {
                        if self.commentsArray.isEmpty {
                            self.commentsArray = array
                        } else {
                            self.commentsArray.append(contentsOf: array)
                            self.commentsArray = Array(Set(self.commentsArray))
                            self.commentsArray.sort(by: { $0.date?.timeIntervalSinceReferenceDate ?? 0.0 < $1.date?.timeIntervalSinceReferenceDate ?? 0.0 })
                        }
                        DispatchQueue.main.async {
                            self.tableView.reloadData()
                        }
                    }
                }
            }
        }
    }
    
    func retrieveTheComments(_ flexId: String, _ dated: String? = nil, closure: @escaping (_ success: Bool, _ results: [Comment], _ error: Error?) -> Void) {
        DispatchQueue.main.async {
            Utilities.showSpinner()
        }
        GFNetworkServices.getComments(flexId, dated) { (success, array, error) in
            DispatchQueue.main.async {
                Utilities.hideSpinner()
                self.tableView.refreshControl?.endRefreshing()
            }
            if success, let array = array {
                if self.commentsArray.isEmpty {
                    self.commentsArray = array
                } else {
                    self.commentsArray.append(contentsOf: array)
                    self.commentsArray = Array(Set(self.commentsArray))
                }
                self.commentsArray.sort(by: { $0.date?.timeIntervalSinceReferenceDate ?? 0.0 < $1.date?.timeIntervalSinceReferenceDate ?? 0.0 })
                DispatchQueue.main.async {
                    self.tableView.reloadData()
                }
            }
        }
    }
    
    // MARK: - Keyboard Delegates
    
    @objc func keyboardWillShow(_ notification: NSNotification) {
        let userInfo = notification.userInfo!
        let keyboardSize = (userInfo[UIResponder.keyboardFrameEndUserInfoKey] as! NSValue).cgRectValue.size
        keyboardHeight = keyboardSize.height + 5
        tableView.contentInset = UIEdgeInsets(top: 0, left: 0, bottom: keyboardSize.height + 10, right: 0)
    }
    
    @objc func keyboardDidShow(_ notification: NSNotification) {
            let ip = IndexPath(row: self.tableView.numberOfRows(inSection: 0) - 1, section: 0)
            self.tableView.scrollToRow(at: ip, at: .bottom, animated: true)
    }

    @objc func keyboardWillHide(_ notification: NSNotification) {
        keyboardHeight = 0.0
        self.tableViewBottomConstraint.constant = 0
    }
}

extension CommentViewController: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return commentsArray.count + 2
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        if indexPath.row < tableView.numberOfRows(inSection: 0) - 2 {
            guard let cell = tableView.dequeueReusableCell(withIdentifier: CommentTableViewCell.cellIdentifier, for: indexPath) as? CommentTableViewCell else { return CommentTableViewCell() }
            let comment = commentsArray[indexPath.row]
            cell.commentTextView.text = comment.comment
            cell.flexterName.text = comment.commentorFlextorName
            cell.flexterName.textColor = .lightGray
            cell.commentTextView.textColor = .lightGray
            cell.statsLabel.text = ""
            cell.statsLabel.isHidden = true
            cell.dateTimeLabel.text = Utilities.relativeTime(date: comment.date)
            cell.dateTimeLabel.textColor = .lightGray
            cell.dateTimeLabel.isHidden = false
            cell.profileImageviewWidthConstraint.constant = 0
            return cell
        }
        if indexPath.row == tableView.numberOfRows(inSection: 0) - 1 {
            guard let cell = tableView.dequeueReusableCell(withIdentifier: EditProfileButtonTableViewCell.cellIdentifier, for: indexPath) as? EditProfileButtonTableViewCell else { return EditProfileButtonTableViewCell() }
            cell.delegate = self
            return cell
        }
        
        guard let cell = tableView.dequeueReusableCell(withIdentifier: EditProfileTextViewTableViewCell.cellIdentifier, for: indexPath) as? EditProfileTextViewTableViewCell else { return  EditProfileTextViewTableViewCell() }
        cell.delegate = self
        cell.titleLabel.text = "Add your comment..."
        cell.textView.addDoneButtonToKeyboard()
        cell.textView.tintColor = .gfGreen
        cell.editingProfile = false
        cell.configureCell()
        cell.textView.text = ""
        cell.characterCountLabel.text = "0"
        return cell
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        if indexPath.row == tableView.numberOfRows(inSection: 0) - 1 {
            return 84
        } else if indexPath.row == tableView.numberOfRows(inSection: 0) - 2 {
            return 130
        }
        return UITableView.automaticDimension
    }
    
    func tableView(_ tableView: UITableView, estimatedHeightForRowAt indexPath: IndexPath) -> CGFloat {
        return 200
    }
}

extension CommentViewController: ProfileDelegate {
    
    func didBeginEditingTextView() {
        let ip = IndexPath(row: tableView.numberOfRows(inSection: 0) - 1, section: 0)
        tableView.scrollToRow(at: ip, at: .top, animated: true)
    }
    
    func updateTextFor(_ type: ProfileUpdateDataType, text: String) {
        if type == .userDescription {
            newCommentText = text
        }
    }
    
    func didTapForProfileAction(_ type: ProfileUpdateDataType, _ object: Any?) {
        if type == .makeTheCall {
            if let flexId = flexId {
                GFNetworkServices.makeComment(newCommentText, flexId) { (success, count, error) in
                    if success {
                        self.count = count
                        if let flexId = self.flexId, flexId != "" {
                            self.retrieveTheComments(flexId) { (success, results, error) in
                                self.newCommentText = ""
                                self.tableView.reloadData()
                            }
                        }
                    }
                }
            }
        }
    }
}

