//
//  BackgroundTasks.swift
//  GameFlex
//
//  Created by <PERSON> on 1/12/21.
//  Copyright © 2021 GameFlex. All rights reserved.
//

import UIKit
import BackgroundTasks
import UserNotifications
import UserNotificationsUI

struct BackgroundTasks {
    
    static var isCancelled = false
    private static var arrayOfFeeds: [FeedType] = []

    static func handleGetFlexes(task: BGAppRefreshTask) {
        guard !isCancelled else { return } // fast fail
        task.expirationHandler = {
          isCancelled = true
        }
        DispatchQueue.main.async {
            let appDelegate = UIApplication.shared.delegate as? AppDelegate
            appDelegate?.scheduleBackgroundTasks()
        }
        task.expirationHandler = {
            BackgroundTasks.isCancelled = true
        }
        if !arrayOfFeeds.contains(.myFeed) {
            arrayOfFeeds.append(.myFeed)
        }
        FeedViewModel.getMyFeed(top: true) { (success, isEmpty, error) in
            self.processCallCompletion(type: .myFeed, task: task)
        }
        if !arrayOfFeeds.contains(.main) {
            arrayOfFeeds.append(.main)
        }
        FeedViewModel.getTheMain(top: true) { (success, error) in
            self.processCallCompletion(type: .main, task: task)
        }
        if !arrayOfFeeds.contains(.channels) {
            arrayOfFeeds.append(.channels)
        }
//        FeedViewModel.getChannels(top: true) { (success, error) in
//            self.processCallCompletion(type: .channels, task: task)
//        }

        
    }
    
    private static func processCallCompletion(type: FeedType, task: BGAppRefreshTask) {
        arrayOfFeeds = arrayOfFeeds.filter({ $0 != type})
        
        if arrayOfFeeds.isEmpty {
            UIApplication.shared.applicationIconBadgeNumber = 1
            task.setTaskCompleted(success: true)
        }
    }
}
