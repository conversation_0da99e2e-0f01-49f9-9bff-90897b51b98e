//
//  ColorCollectionViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 7/27/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

class ColorCollectionViewCell: UICollectionViewCell {
    
    @IBOutlet weak var colorView: UIView!
            
    static var cellIdentifier = String(describing: ColorCollectionViewCell.self)
    
    override func awakeFromNib() {
        super.awakeFromNib()
        colorView.layer.cornerRadius = colorView.frame.size.width/2
        
    }

    override func prepareForReuse() {
        super.prepareForReuse()
        colorView.layer.borderColor = UIColor.clear.cgColor
    }
    
    func configureColorView(_ isSelected: Bool = false) {
        
        if isSelected {
            colorView.layer.borderColor = UIColor.gfGreen.cgColor
            colorView.layer.borderWidth = 3.0
        } else {
            colorView.layer.borderColor = UIColor.clear.cgColor
        }
        
    }

}


