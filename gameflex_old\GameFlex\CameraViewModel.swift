//
//  CameraViewModel.swift
//  GameFlex
//
//  Created by <PERSON> on 7/13/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

enum CameraCellType: String {
    case none, restart
    case buttons, filters, edit, flare, text, slider, stickerInProgress, fonts, colors, draw, cancelText, erase
    case brightness, contrast, temperature, saturation, shadowRemover
    case flexFun, emojis, smellies, memes, bubbles, arrows, smack, favs
}

enum CameraActionType: String {
    case none
    case snapPicture, toggleFlash, runTimer, swapCamera
    case filter, edit, flare, text, draw, cancelText
    case brightness, contrast, temperature, saturation, shadowRemover
    case flexFun, emojis, smellies, memes, bubbles, arrows, smack, favs
    case fonts, colors
}

struct CameraViewModel {
    
    static var isReflex: Bool = false
    static var parentFlexId: String?
    static var isEraseEnabled: Bool = false
    
    static var activeSticker: StickerView?
    static var stickersInProgress: [StickerView] = []
    static var canCreateTextStickerView = false
    static var canCreateStickerStickerView = false
    static var textStickerBeingEdited = false

    static var state: CameraCellType = .buttons
    static var wasState: CameraCellType?
    static var selectedFlareCategoryIndexPath: IndexPath?
    static var selectedFontIndexPath: IndexPath = IndexPath(row: 3, section: 0)
    static var selectedColorIndexPath: IndexPath = IndexPath(row: 0, section: 0) // white
    static var previousSelectedColorIndexPath: IndexPath = IndexPath(row: 1, section: 0) //black
    static var colorText: Bool = true // false -> color the background
    static var textStates: [CameraCellType] = [.text, .fonts, .colors]
    static var filterStates: [CameraCellType] = [.filters, .edit, .brightness, .contrast, .temperature, .saturation, .shadowRemover]
    static var textStickerStartingHeight: CGFloat = 50.0
    
    static var drawings: [UIImageView] = []
    static let drawingsTag = 3229103
    static let viewerTag   = 3229104
    static let blackViewTag = 3229105
    static var opacitySetting: CGFloat = 1.0
    static var opacityUp = false

    static let filterArray: [Int: [String: FilterType]] = [0: ["None".localized: FilterType.normal],
                                                           1: ["Binary".localized: FilterType.binary],
                                                           2: ["Boomer".localized: FilterType.boomer],
                                                           3: ["Chromo".localized: FilterType.chromo],
                                                           4: ["Alpha".localized: FilterType.alpha],
                                                           5: ["Mono".localized: FilterType.mono],
                                                           6: ["Schmuag".localized: FilterType.schmuag],
                                                           7: ["Vibes".localized: FilterType.vibes],
                                                           8: ["Boost".localized: FilterType.boost],
                                                           9: ["Flat".localized: FilterType.flat]]
    
    // used in editCollectionViewCells
    static let editArray = [0: ["Brightness".localized: #imageLiteral(resourceName:"brightness")],
                            1: ["Contrast".localized: #imageLiteral(resourceName:"contrast")],
                            2: ["Temperature".localized: #imageLiteral(resourceName:"structure")],
                            3: ["Saturation".localized: #imageLiteral(resourceName:"brightness")],
                            4: ["Shadow".localized: #imageLiteral(resourceName:"brightness")]]
    
    static let editDictionary = ["Brightness".localized: SliderFilterType.brightness,
                                 "Contrast".localized: SliderFilterType.contrast,
                                 "Temperature".localized: SliderFilterType.temperature,
                                 "Saturation".localized: SliderFilterType.saturation,
                                 "Shadow".localized: SliderFilterType.shadowRemover]

    // used in selecting editCollectionViewCells
    static let editActionArray: [CameraActionType] = [.brightness, .contrast, .temperature, .saturation, .shadowRemover]
    static let editCellArray: [CameraCellType] = [.brightness, .contrast, .temperature, .saturation, .shadowRemover]
    
    static let flareCategories = [0: ["Flex Fun".localized: #imageLiteral(resourceName:"gradientStickers")],
                                  1: ["Emojis".localized: #imageLiteral(resourceName:"gradientEmojis")],
                                  2: ["Smellies".localized: #imageLiteral(resourceName:"gradientTrending")],
                                  3: ["Memes".localized: #imageLiteral(resourceName:"gradientLocations")],
                                  4: ["Bubbles".localized: #imageLiteral(resourceName:"gradientVR")],
                                  5: ["Arrows".localized: #imageLiteral(resourceName:"gradientFantasy")],
                                  6: ["Smack".localized: #imageLiteral(resourceName:"gradientSandbox")]]
    
    static let flareTypes: [CameraCellType] = [.flexFun, .emojis, .smellies, .memes, .bubbles, .arrows, .smack, .favs]
    static let flareCarousel: [CameraCellType] = [.flare, .favs, .flexFun, .emojis, .smellies, .memes, .bubbles, .arrows, .smack]
    static let flareDictionary: [CameraCellType: CameraActionType] = [.flexFun: .flexFun, .emojis: .emojis, .smellies: .smellies, .memes: .memes, .bubbles: .bubbles, .arrows: .arrows, .smack: .smack, .favs: .favs]
        
    static var heightOfCollectionView: [CameraCellType: CGFloat] = [.buttons: 109.0, .filters: 140.0, .edit: 140.0, .brightness: 178.0, .flare: 222.0,
                                                                    .favs: 222.0, .flexFun: 222.0, .emojis: 222.0, .smellies: 222.0, .memes: 222.0, .bubbles: 222.0, .arrows: 222.0, .smack: 222.0, .text: 55.0, .fonts: 55.0, .colors: 55.0, .none: 0.0, .draw: 55.0]
    static func sequenceOfCells(type: CameraCellType) -> [CameraCellType] {
        switch type {
            
        case .buttons: return [.buttons]
        case .filters: return [.filters]
        case .edit: return [.edit]
        default: return []
        }
        
    }
    
    static func configureCell(cell: UICollectionViewCell, type: CameraCellType) {
        
        switch type {
        case .buttons:
            break
        case .filters:
            break
        default: break
        }
    }
    
    // sticker inventory
    static let stickersSource: [CameraCellType: [String]] = [.flexFun: CameraViewModel.flexFun, .smellies: CameraViewModel.smelliesStickers, .memes: CameraViewModel.memesStickers, .bubbles: CameraViewModel.bubblesStickers, .arrows: CameraViewModel.arrowsStickers, .smack: CameraViewModel.smackStickers, .emojis: CameraViewModel.emojis]
    
    static var emojis: [String] {
        let emojiRanges = [
            0x1F601...0x1F64F,
            0x2702...0x27B0,
            0x1F680...0x1F6C0,
            0x1F170...0x1F251
        ]

        var emojiList: [String] = []
        for range in emojiRanges {
            var array: [String] = []
            for i in range {
                if let unicodeScalar = UnicodeScalar(i){
                    array.append(String(describing: unicodeScalar))
                }
            }

            emojiList.append(contentsOf: array)
        }
        return emojiList
    }

    static let flexFun: [String] = ["Ass Kicked.png", "Barrel Stuffed.png", "Bullet Hole 1.png", "Bullet Sponge.png", "Camper Text.png", "Camper.png", "Cheater.png", "Crosshairs.png", "Cry Baby.png", "Dead Inside.png", "Face Roll.png", "Fail Army.png", "Farming NPCs.png", "Feeding the Enemy.png", "Filthy Casual.png", "Finger Guns.png", "Fireball.png", "Five Stars.png", "Flex Arm.png", "Flex Man 1.png", "Flex Man 2.png", "Flex Smoothfly - Shouting.png", "Flex Smoothfly - Smiling.png", "Flexi Hand on mouth.png", "Flexi Hearts.png", "Flexi Laugh and Point.png", "Flexi Shocked.png", "Flexin Smoothfly.png", "Flexio Drinking.png", "Flexio Grumpy.png", "Flexio Running.png", "Flexio Smashing.png", "Flexio Stunned.png", "Fragged.png", "Friendly Fire.png", "Good Game.png", "Grinding.png", "Group 6.png", "Group-2.png", "Group-3.png", "Group.png", "HeFlex Would You look at that.png", "Headshot 2.png", "Headshot.png", "High Five.png", "Mind Blown.png", "Nerd Girl.png", "One Shot Kill.png", "Pirate Full Body.png", "Point and Laugh.png", "Rekt.png", "Sarcastic Soldier.png", "Screen Peeker.png", "Smurf.png", "Spanked.png", "Thumbs Down.png", "Vector-3.png", "Vector.png", "What were you thinking.png", "flexi What was that.png", "icons8-alien-50.png", "icons8-apple-arcade-50.png", "icons8-arcade-cabinet-50.png", "icons8-asian-character-50.png", "icons8-assassins-creed-50.png", "icons8-atari-2600-50.png", "icons8-bendy-50.png", "icons8-best-terraria-50.png", "icons8-body-armor-50.png", "icons8-crash-bandicoot-50.png", "icons8-danganronpa-50.png", "icons8-death-star-50.png", "icons8-eevee-50.png", "icons8-futurama-zapp-brannigan-50.png", "icons8-game-controller-50.png", "icons8-grand-theft-auto-v-50.png", "icons8-hyper-potion-50.png", "icons8-joystick-50.png", "icons8-mana-50.png", "icons8-mega-ball-50.png", "icons8-meowth-50.png", "icons8-millennium-rod-50.png", "icons8-minecraft-main-character-50.png", "icons8-minecraft-pickaxe-50.png", "icons8-minecraft-pug-50.png", "icons8-minecraft-skeleton-50.png", "icons8-minecraft-sword-50-2.png", "icons8-minecraft-sword-50.png", "icons8-minecraft-zombie-50.png", "icons8-naruto-50-2.png", "icons8-naruto-50.png", "icons8-nintendo-gamecube-50.png", "icons8-pacman-50-2.png", "icons8-pacman-50-3.png", "icons8-pacman-50.png", "icons8-pikachu-pokemon-50.png", "icons8-play-button-circled-50.png", "icons8-playstation-50.png", "icons8-pokeball-50-2.png", "icons8-pokeball-50.png", "icons8-pokecoin-50.png", "icons8-pubg-50-2.png", "icons8-pubg-50.png", "icons8-pubg-helmet-50.png", "icons8-qq-50.png", "icons8-rainbow-six-siege-50.png", "icons8-rockstar-games-50.png", "icons8-satellites-50.png", "icons8-skyrim-50.png", "icons8-sonic-the-hedgehog-50.png", "icons8-spyro-50.png", "icons8-stormtrooper-50.png", "icons8-super-mario-50-2.png", "icons8-super-mario-50.png", "icons8-visual-game-boy-50-2.png", "icons8-visual-game-boy-50-3.png", "icons8-visual-game-boy-50.png", "icons8-xbox-50.png", "mushblue.png", "mushgreen.png", "mushred.png", "mushyellow.png", "spaz kid.png", "tetris 1.png", "tetris 2.png", "tetris 3.png", "tetris 4.png", "tetris 5.png"]
    static let smackStickers: [String] = ["80s_Vibe.png", "Aesthetic.png", "Are_You_Ok.png", "Ass Kicked text.png", "Back_To_The_Past.png", "Barrel Stuffed Text.png", "Bullet Sponge Text.png", "Cheater Text.png", "Cherry_Bomb.png", "Classic.png", "Cool!.png", "Cry Baby Text.png", "Eye Roll text.png", "Face Palm text.png", "Face Roll Text.png", "Fail Army text copy.png", "Fail Army text.png", "Fail Text.png", "Fail.png", "Far_Out.png", "Farming NPCs text.png", "Feeding the Enemy Text.png", "Filthy Casual Text.png", "Flexed 1 Text.png", "Flexed 2 Text.png", "Game Over.png", "Game_Over.png", "God Mode Text.png", "Good Game Text.png", "Great Job, Idiot text.png", "Griefer Text.png", "Grinding Text.png", "Groovy.png", "Hahahaha Text.png", "Headshot 2 text.png", "Healthbar.png", "High Five Text.png", "Layer 5.png", "Let_s_Go.png", "Lightning 1.png", "Lit Text.png", "Logo.png", "Nerfed Text.png", "Oh no it_s retarded text.png", "Oh yeah text.png", "Old_School.png", "One Shot Kill Text.png", "Quick Scope Text.png", "Rage Quit text.png", "Red_is_Sus.png", "Rekt Text.png", "Retro.png", "Roasted Text.png", "SH_ Text.png", "Smurf Text.png", "Spanked Text.png", "Spoiler Text.png", "Spray and Pray text.png", "Teabagged 2 Text.png", "That_s Cap.png", "That_s_Sus.png", "Toxic Teammate Text.png", "Trollin_ Text.png", "Waaaaaaah text.png", "Would you look at that text.png", "You_Sus_Bro.png"]
    static let smelliesStickers: [String] = ["Layer 10.png", "Layer 11.png", "Layer 12.png", "Layer 13.png", "Layer 14.png", "Layer 15.png", "Layer 16.png", "Layer 17.png", "Layer 18.png", "Layer 19.png", "Layer 20.png", "Layer 21.png", "Layer 22.png", "Layer 23.png", "Layer 24.png", "Layer 25.png", "Layer 26.png", "Layer 27.png", "Layer 28.png", "Layer 29.png", "Layer 30.png", "Layer 31.png", "Layer 32.png", "Layer 33.png", "Layer 6.png", "Layer 7.png", "Layer 8.png", "Layer 9.png", "Smileys.png"]
    static let memesStickers: [String] = ["Cutout 1.png", "Cutout 2.png", "Cutout 5.png", "Cutout 6.png", "Cutout-10.png", "Cutout-11.png", "Cutout-12.png", "Cutout-13.png", "Cutout-14.png", "Cutout-15.png", "Cutout-16.png", "Cutout-17.png", "Cutout-18.png", "Cutout-3.png", "Cutout-4.png", "Cutout-7.png", "Cutout-8.png", "Future US Army.png", "Hey i wanna.png", "I don_t care.png", "I sure hope.png", "I was to busy.png", "It is Wednesday.png", "Okay.png", "That was legitness.png", "Think.png", "Thumbs up.png"]
    static let bubblesStickers: [String] = ["Angry Bubble 1.png", "Angry Bubble 2.png", "Angry Question Mark.png", "Shout 1.png", "Shout 2.png", "Shout 3.png", "Shout 4.png", "Shout 5.png", "Shout 6.png", "Speech Bubble 1.png", "Speech Bubble 10.png", "Speech Bubble 11.png", "Speech Bubble 13.png", "Speech Bubble 14.png", "Speech Bubble 15.png", "Speech Bubble 4.png", "Speech Bubble 5.png", "Speech Bubble 6.png", "Speech Bubble 7.png", "Speech Bubble 9.png", "Square Cloud 1.png", "Square Cloud 2.png", "Square Cloud 3.png", "Thought Cloud 3.png", "What was that text.png", "What were you thinking text.png", "Whisper Bubble 1.png"]
    static let arrowsStickers: [String] = ["Arrow 1.png", "Arrow 2.png", "Arrow 3.png", "Arrow 4.png", "Vector-2.png", "icons8-arrow-50.png"]

    
    static func clearTheFlex(isRestart: Bool = false) {
        activeSticker = nil
        canCreateStickerStickerView = false
        canCreateTextStickerView = false
        colorText = true // false -> color the background
        drawings = []
        FinalizeViewModel.channelId = nil
        isReflex = false
        opacitySetting = 1.0
        opacityUp = false
        parentFlexId = nil
        previousSelectedColorIndexPath = IndexPath(row: 1, section: 0) //black
        selectedColorIndexPath = IndexPath(row: 0, section: 0) // white
        selectedFlareCategoryIndexPath = nil
        selectedFontIndexPath = IndexPath(row: 3, section: 0)
        state = isRestart ? .restart : .buttons
        stickersInProgress = []
        textStickerStartingHeight = 50.0
        wasState = .buttons
        Utilities.hideSpinner()
    }
    
    static func configureFontCell(ip: IndexPath, cv: UICollectionView, isSelected: Bool) {
        guard let cell = cv.dequeueReusableCell(withReuseIdentifier: FontCollectionViewCell.cellIdentifier, for: ip) as? FontCollectionViewCell else { return }
        cell.configureLabel(isSelected)
        // apply the font to the activeSticker
        if let ts = CameraViewModel.activeSticker as? TextStickerView, ts.viewType == .text, let size = ts.textStickerView?.font?.pointSize, isSelected {
            let font = FontManager.flatFontsArray[ip.row]
            ts.textStickerView.font = UIFont(name: font, size: size)
            ts.setNeedsDisplay()
        }
    }
    
    static func configureColorCell(ip: IndexPath, cv: UICollectionView, isSelected: Bool) {
        guard let cell = cv.dequeueReusableCell(withReuseIdentifier: ColorCollectionViewCell.cellIdentifier, for: ip) as? ColorCollectionViewCell else { return }
        cell.configureColorView(isSelected)
    }

    static var colors: [UIColor] {
        return [
        /* white, black, violet, indigo, blue, green, yellow, orange, red */
            UIColor.white,
            .init(red: 1, green: 0, blue: 0, alpha: 1.0), // red
           .black,
            .init(red: 0, green: 1, blue: 0, alpha: 1.0), // green
            .init(red: 0, green: 0, blue: 1, alpha: 1.0), // blue
            .init(red: 1, green: 1, blue: 0, alpha: 1.0), // yellow
            .init(red: 1, green: 127/255, blue: 0, alpha: 1.0), // brown/orange
            .init(red: 148/255, green: 0, blue: 211/255, alpha: 1.0),
            .init(red: 75/255, green: 0, blue: 130/255, alpha: 1.0),

        ]
    }
    
}
