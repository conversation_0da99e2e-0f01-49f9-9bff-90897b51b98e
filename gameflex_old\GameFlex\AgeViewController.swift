//
//  AgeViewController.swift
//  GameFlex
//
//  Created by <PERSON> on 9/15/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

class AgeViewController: UIViewController {
    
    @IBOutlet weak var collectionView: UICollectionView!
        
    var month: IndexPath?
    var year: IndexPath?
    let kLabelTag = 4566902
    
    let months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
    var years: [Int] {
        var arr: [Int] = []
        for i in 1915...2015 {
            arr.append(i)
        }
        arr.sort { ($1 < $0 ) }
        return arr
    }
    
    static func storyboardInstance() -> AgeViewController {
        let sb = UIStoryboard(name: "Main", bundle: nil)
        return sb.instantiateViewController(withIdentifier: String(describing: AgeViewController.self)) as! AgeViewController
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(UINib(nibName: "MonthYearCollectionViewCell", bundle: nil), forCellWithReuseIdentifier: MonthYearCollectionViewCell.cellIdentifier)
        collectionView.register(UICollectionReusableView.self, forSupplementaryViewOfKind: UICollectionView.elementKindSectionHeader, withReuseIdentifier: "header")
        let backButton = UIBarButtonItem(title: "", style: .plain, target: navigationController, action: nil)
        navigationItem.leftBarButtonItem = backButton
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
    }
    
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
    }
    
    private func convertToDateOfBirth() {
        if let ipm = month, let ipy = year {
            let mo = months[ipm.row]
            let yr = years[ipy.row]
            let birth = "\(mo) 1, \(yr)"
            let df = DateFormatter()
            df.dateFormat = "MMM d, yyyy"
            let dated = df.date(from: birth)
            User.dateOfBirth = dated
        }
    }

}

extension AgeViewController: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    
    func numberOfSections(in collectionView: UICollectionView) -> Int {
        return 2
    }
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        switch section {
        case 0:
            return months.count
        default:
            return years.count
        }
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: MonthYearCollectionViewCell.cellIdentifier, for: indexPath) as? MonthYearCollectionViewCell else { return MonthYearCollectionViewCell() }
        switch indexPath.section {
        case 0:
            cell.label?.text = "\(months[indexPath.row])"
        default:
            cell.label?.text = "\(years[indexPath.row])"
        }
        cell.label?.textAlignment = .center
        if (month != nil && indexPath == month) || (year != nil && indexPath == year) {
            cell.cellBackground.backgroundColor = .gfGreen
            cell.label?.textColor = .black
        } else {
            cell.cellBackground.backgroundColor = .black
            cell.label?.textColor = .gfGrayText

        }
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, viewForSupplementaryElementOfKind kind: String, at indexPath: IndexPath) -> UICollectionReusableView {
        switch kind {
        case UICollectionView.elementKindSectionHeader:
            let headerView = collectionView.dequeueReusableSupplementaryView(ofKind: kind, withReuseIdentifier: "header", for: indexPath)
            headerView.backgroundColor = .black
            headerView.subviews.filter({ $0.tag == kLabelTag }).forEach({ $0.removeFromSuperview() })
            if indexPath.section == 0 {
            let label = UILabel(frame: CGRect(x: 0.0, y: 0.0, width: view.frame.size.width, height: 50.0))
            label.numberOfLines = 0
            headerView.addSubview(label)
            label.text = "What month and year were you born?"
            label.textColor = .white
            label.textAlignment = .center
            label.frame.origin = headerView.frame.origin
                label.tag = kLabelTag
            }
            return headerView
                        
        default:
            assert(false, "Unexpected element kind")
        }
        return UICollectionReusableView()
        
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        switch indexPath.section {
        case 0:
            return CGSize(width: view.frame.size.width/6, height: 40)
        default:
            return CGSize(width: view.frame.size.width/5, height: 40)
        }
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, insetForSectionAt section: Int) -> UIEdgeInsets {
            return UIEdgeInsets(top: 0.0, left: 0.0, bottom: 0.0, right: 0.0)
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, referenceSizeForHeaderInSection section: Int) -> CGSize {
        
        var tall: CGFloat = 100.0
        if section == 1 { tall = 50.0 }
        return CGSize(width: view.frame.size.width, height: tall)
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {

        if indexPath.section == 0 {
            month = indexPath
        } else {
            year = indexPath
        }
        if year != nil, month != nil {
            convertToDateOfBirth()
            let viewer = UIView(frame: self.view.frame)
            viewer.backgroundColor = .black
            self.view.addSubview(viewer)
            viewer.alpha = 0.0
            UIView.animate(withDuration: 0.5) {
                viewer.alpha = 1.0
           } completion:  { (_) in
            OnboardingStateMachine.didComplete(this: .age)
            self.dismiss(animated: true)
            }
        }
        collectionView.reloadData()
    }
}
