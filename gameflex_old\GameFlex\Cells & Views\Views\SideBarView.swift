//
//  SideBarView.swift
//  GameFlex
//
//  Created by <PERSON> on 10/11/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

enum SideBarButtonType {
    case share, like, comment, reflex, play
}

class SideBarView: UIView {
    
    @IBOutlet weak var shareView: UIView!
    @IBOutlet weak var likeView: UIView!
    @IBOutlet weak var commentView: UIView!
    @IBOutlet weak var reflexView: UIView!
    @IBOutlet weak var playView: UIView!
    
    @IBOutlet weak var shareButton: UIButton!
    @IBOutlet weak var likeButton: UIButton!
    @IBOutlet weak var reflexButton: UIButton!
    @IBOutlet weak var playButton: UIButton!
    @IBOutlet weak var commentButton: UIButton!
    
    @IBOutlet weak var shareLabel: UILabel!
    @IBOutlet weak var likeLabel: UILabel!
    @IBOutlet weak var playLabel: UILabel!
    @IBOutlet weak var reflexL<PERSON>: UILabel!
    @IBOutlet weak var commentLabel: UILabel!
    
    @IBOutlet weak var shareIcon: UIImageView!
    @IBOutlet weak var likeIcon: UIImageView!
    @IBOutlet weak var reflexIcon: UIImageView!
    @IBOutlet weak var playIcon: UIImageView!
    @IBOutlet weak var commentIcon: UIImageView!
    @IBOutlet weak var reflexPresentLight: UIView! // adjacent to playButton
    
    @IBOutlet weak var shareBacking: UIView!
    @IBOutlet weak var likeBacking: UIView!
    @IBOutlet weak var reflexBacking: UIView!
    @IBOutlet weak var playBacking: UIView!
    @IBOutlet weak var commentBacking: UIView!
        
    var backings: [UIView] = []
    
    weak var delegate: ChannelDelegate?
    
    // MARK: - lifecycle
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        let nib = UINib(nibName: String(describing: type(of: self)), bundle: Bundle(for: type(of: self)))
        let view = nib.instantiate(withOwner: self, options: nil).first as! UIView
        view.frame = frame
        view.frame = bounds
        view.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        setupView()
        addSubview(view)
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setupView() {
        backings = [shareBacking, likeBacking, reflexBacking, playBacking, commentBacking]
        backings.forEach({
            $0.layer.cornerRadius = 20
            $0.backgroundColor = .gfDarkBackground
            $0.alpha = 0.80
        })
        playIcon.image?.withRenderingMode(.alwaysTemplate)
        playIcon.tintColor = .gfOffWhite
        reflexPresentLight.backgroundColor = .gfGreen
        reflexPresentLight.layer.cornerRadius = reflexPresentLight.frame.width/2
        
        shareLabel.text = "More"
        likeLabel.text = "Like"
        playLabel.text = "View"
        reflexLabel.text = "Reflex"
        commentLabel.text = "Comment"
    }
    
    // MARK: - action
    
    @IBAction func didTapButton(_ sender: UIButton) {
        
        var action: ChannelAction = .like
        switch sender {
        case shareButton:
            action = .more
        case likeButton:
            action = .like
        case playButton:
            action = .play
        case reflexButton:
            action = .reflex
        case commentButton:
            action = .comment
        default: return
        }
    
        delegate?.didTapForChannelAction(action)
    }
    
    func isReflex() {
        reflexButton.isHidden = true
        reflexIcon.isHidden = true
    }
    
    func hasReflexes(_ has: Bool = false) {
        if has {
            playButton.isHidden = false
            playIcon.isHidden = false
            playBacking.isHidden = false
            playLabel.isHidden = false
        } else {
            playButton.isHidden = true
            playIcon.isHidden = true
            playBacking.isHidden = true
            playLabel.isHidden = true
        }
    }
    
    func hasSeenReflexes(_ has: Bool = false) {
        if has {
            reflexPresentLight.isHidden = true
        } else {
            reflexPresentLight.isHidden = false
        }
    }
    
    func highlight(this: SideBarButtonType, _ highlight: Bool = false) {
        switch this {
        case .share:
            if highlight {
                shareIcon.image = #imageLiteral(resourceName: "dotsSelected")
                shareIcon.tintColor = .gfGreen
                shareBacking.layer.borderWidth = 2
                shareBacking.layer.borderColor = UIColor.gfGreen.cgColor
            } else {
                shareIcon.image = #imageLiteral(resourceName: "dots")
                shareIcon.tintColor = .white
                shareBacking.layer.borderColor = UIColor.clear.cgColor
            }
        case .like:
            if highlight {
                likeIcon.image = #imageLiteral(resourceName: "likeSelected")
                likeBacking.layer.borderWidth = 2
                likeBacking.layer.borderColor = UIColor.gfGreen.cgColor
            } else {
                likeIcon.image = #imageLiteral(resourceName: "like")
                likeBacking.layer.borderColor = UIColor.clear.cgColor
            }

        case .comment:
            if highlight {
                commentIcon.image = #imageLiteral(resourceName: "commentSelected")
                commentBacking.layer.borderWidth = 2
                commentBacking.layer.borderColor = UIColor.gfGreen.cgColor
            } else {
                commentIcon.image = #imageLiteral(resourceName: "comment")
                commentBacking.layer.borderColor = UIColor.clear.cgColor
            }

        case .reflex:
            if highlight {
                reflexIcon.image = #imageLiteral(resourceName: "reflexSelected")
                reflexBacking.layer.borderWidth = 2
                reflexBacking.layer.borderColor = UIColor.gfGreen.cgColor
            } else {
                reflexIcon.image = #imageLiteral(resourceName: "reflex")
                reflexBacking.layer.borderColor = UIColor.clear.cgColor
            }

        case .play:
            if highlight {
                playIcon.image = #imageLiteral(resourceName: "playSelected")
                playBacking.layer.borderWidth = 2
                playBacking.layer.borderColor = UIColor.gfGreen.cgColor
            } else {
                playIcon.image = #imageLiteral(resourceName: "play")
                playBacking.layer.borderColor = UIColor.clear.cgColor
            }

        }
    }
}
