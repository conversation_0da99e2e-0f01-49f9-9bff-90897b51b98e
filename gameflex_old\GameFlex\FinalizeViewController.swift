//
//  FinalizeViewController.swift
//  GameFlex
//
//  Created by <PERSON> on 10/17/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import King<PERSON><PERSON>

enum FinalizeAction {
    case other, flex
}

protocol FinalizeDelegate: AnyObject {
    func didTapForFinalizeAction(_ action: FinalizeAction)
    func updatetheTextView()
}

class FinalizeViewController: GFViewController {
    
    var flexImage: UIImage?
    
    var textView: UITextView?
    var defaultSelection: IndexPath?
    
    @IBOutlet weak var tableView: UITableView!
    var isReflex = FlexManager.flex?.isReflex ?? false
    
    // MARK: - life cycle funcs
    
    static func storyboardInstance() -> FinalizeViewController {
        let sb = UIStoryboard(name: "Main", bundle: nil)
        return sb.instantiateViewController(withIdentifier: String(describing: FinalizeViewController.self)) as! FinalizeViewController
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        tableView.delegate = self
        tableView.dataSource = self
        tableView.tableFooterView = UIView()
        tableView.register(UINib(nibName: FinalizeTopTableViewCell.cellIdentifier, bundle: nil), forCellReuseIdentifier: FinalizeTopTableViewCell.cellIdentifier)
        tableView.register(UINib(nibName: HashTagsTableViewCell.cellIdentifier, bundle: nil), forCellReuseIdentifier: HashTagsTableViewCell.cellIdentifier)
        tableView.register(UINib(nibName:  FollowingTableViewCell.cellIdentifier, bundle: nil), forCellReuseIdentifier: FollowingTableViewCell.cellIdentifier)
        tableView.register(UITableViewCell.self, forCellReuseIdentifier: "cell")
        FinalizeViewModel.captionText = ""
        hideKeyboardWhenTappedAround()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        title = "Finalize"
        if isReflex {
            addReflexToNavBar(true)
        } else {
            addFlexToNavBar(true)
        }
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        selectTheDefault()
    }
    
    func selectTheDefault() {
        if defaultSelection != nil {
            tableView.selectRow(at: defaultSelection, animated: true, scrollPosition: .middle)
        } else {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                self.selectTheDefault()
            }
        }
    }
    
    override func didTapNext() {
        dismissKeyboard()
        Utilities.showSpinner()
        FlexManager.flex?.caption = FinalizeViewModel.captionText
        if let flex = FlexManager.flex, let image = flexImage, FlexManager.passesSafety(flex) {
            // Flexes it here
            if FinalizeViewModel.channelId == nil {
                FinalizeViewModel.channelId = User.userId
            }
            if let channelId = FinalizeViewModel.channelId {
                GFNetworkServices.uploadMedia(channelId: channelId, image: image) { (success, error) in
                    // save to photoLibrary
                    UIImageWriteToSavedPhotosAlbum((image), self as FinalizeViewController,
                                                   #selector(self.image(_:didFinishSavingWithError:contextInfo:)), nil)
                    print("\(success), \(error?.localizedDescription ?? "No Error?")")
                    DispatchQueue.main.async {
                        Utilities.hideSpinner()
                        if success {
                            self.successAlert()
                        } else {
                            self.successAlert(error)
                        }
                    }
                }
            } else {
                Utilities.hideSpinner()
                let alert = UIAlertController(title: "Pick a channel", message: "Flexes are flexed onto channels. Pick one before flexing.", preferredStyle: .alert)
                if  (User.flexter.channelsOwned?.count ?? 0) + (User.flexter.channelsParticipated?.count ?? 0) == 0 {
                    alert.title = "Create a New Channel"
                    alert.message = "Flexes are flexed onto channels. Create one before flexing. Then invite your friends to reFlex your flex."
                }

                let ok = UIAlertAction(title: "OK", style: .default, handler: nil)
                alert.addAction(ok)
                self.present(alert, animated: true, completion: nil)
            }
        }
    }
    
    private func successAlert(_ error: Error? = nil) {
        self.updateFlexCount()
        CameraViewModel.clearTheFlex(isRestart: true)
        CameraViewModel.state = .buttons
        guard error != nil else {
            Utilities.hideSpinner()
            self.navigationController?.popToRootViewController(animated: true) // special handling in custom class of navigationController
            return
        }
        let alert = UIAlertController(title: "Error",
                                      message: "\((error as? GFError)?.errorCode ?? 499) - \((error as? GFError)?.errorDescription ?? "unknown")",
                                      preferredStyle: .alert)
        let ok = UIAlertAction(title: "OK", style: .default) { (action) in
            self.navigationController?.popViewController(animated: true)
        }
        alert.addAction(ok)
        DispatchQueue.main.async {
            self.present(alert, animated: true)
        }
    }
    
    /// Process photo saving result
    @objc func image(_ image: UIImage,
        didFinishSavingWithError error: Error?, contextInfo: UnsafeRawPointer) {
        if error != nil {
            let alert = UIAlertController(title: "Saving Error", message: "Something unexpected happened. Try again.", preferredStyle: .alert)
            let ok = UIAlertAction(title: "OK", style: .default, handler: nil)
            alert.addAction(ok)
            DispatchQueue.main.async {
                self.present(alert, animated: true)
            }
        } else {
            successAlert()
        }
    }
    
    @objc func createNewChannel() {
        let cc = CreateChannelViewController.storyboardInstance()
        navigationController?.pushViewController(cc, animated: true)
    }

}

extension FinalizeViewController: UITableViewDelegate, UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        if isReflex {
            return 1
        }
        return 4 // top, owned channels, participant channels, other services
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        switch section {
        case 0: return 1
//        case 1: return (User.flexter.channelsOwned?.count ?? 0) + 1
//        case 2: return (User.flexter.channelsParticipated?.count ?? 0)
//        case 3: return 1
        default: return 0
        }
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        
        switch indexPath.section {
        case 0:
            guard let cell = tableView.dequeueReusableCell(withIdentifier: FinalizeTopTableViewCell.cellIdentifier, for: indexPath) as? FinalizeTopTableViewCell else { return FinalizeTopTableViewCell() }
            cell.flexImageView.image = flexImage
            cell.textView.becomeFirstResponder()
            self.textView = cell.textView
            cell.textView.tintColor = .gfGreen
            return cell
        case 1, 2:
            guard let cell = tableView.dequeueReusableCell(withIdentifier: FollowingTableViewCell.cellIdentifier, for: indexPath) as? FollowingTableViewCell else { return FollowingTableViewCell() }
            let flexter = User.flexter
            var channel = Channel()
            if indexPath.section == 1 {
                switch indexPath.row {
                case 0:
                    channel.channelId = User.userId
                    channel.channelImage = User.flexter.profileImage
                    channel.channelName = "MyFeed (default)"
                    defaultSelection = indexPath
                default:
                    let row = indexPath.row - 1
                    channel = flexter.channelsOwned?[row] ?? channel
                }
            } else {
                channel = flexter.channelsParticipated?[indexPath.row] ?? channel
            }
            cell.configureCell(following: true, channel: channel)
            if let pic = channel.channelImage, pic != "" {
                let url = URL(string: pic)
                let processor = DownsamplingImageProcessor(size: cell.profileImage?.bounds.size ?? CGSize(width: 40, height: 40))
                cell.profileImage?.kf.indicatorType = .activity
                cell.profileImage?.kf.setImage(
                    with: url,
                    placeholder: UIImage(named: FlexManager.randomImagePlaceholder(channel.channelId)),
                    options: [
                        .processor(processor),
                        .scaleFactor(UIScreen.main.scale),
                        .transition(.fade(1)),
                        .cacheOriginalImage
                    ], completionHandler:
                        {
                            result in
                            switch result {
                            case .success: break
                            case .failure(let error):
                                print("Job failed: \(error.localizedDescription)")
                            }
                        })
            } else {
                cell.profileImage.image = UIImage(named: FlexManager.randomImagePlaceholder(channel.channelId))
                cell.profileImage.transform = CGAffineTransform(a: 0.5, b: 0, c: 0, d: 0.5, tx: 0, ty: 0)
            }
            cell.isUserInteractionEnabled = true
            
            return cell

        case 3:
            let cell = tableView.dequeueReusableCell(withIdentifier: "cell", for: indexPath)
            cell.textLabel?.text = "Other services"
            cell.selectionStyle = .none
            return cell
        default: return UITableViewCell()
        }
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        switch indexPath.section {
        case 0: return 104
        case 1, 2, 3: return 44
        default: return 0
        }
    }
    
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        switch section {
        case 0, 3:
            return 0
        default:
            return 35
        }
    }
    
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        let header = UIView(frame: CGRect(x: 0, y: 0, width: view.frame.size.width, height: 35))
        let label = UILabel(frame: CGRect(x: 16, y: 0, width: header.frame.size.width, height: header.frame.size.height))
        label.font = .boldSystemFont(ofSize: 17)
        label.textColor = .gfOffWhite
        header.addSubview(label)
        
        header.backgroundColor = .gfDarkBackground40
        header.subviews.filter({ $0.tag == 99421 }).forEach({ $0.removeFromSuperview() })
        switch section {
        case 1:
            label.text = "Select a Channel - Owner"
//            let newButton = UIButton(frame: CGRect(x: header.frame.size.width - 116.0, y: 0.0, width: 100.0, height: header.frame.size.height))
//            newButton.addTarget(self, action: #selector(createNewChannel), for: .touchUpInside)
//            newButton.setTitleColor(.gfGreen, for: .normal)
//            newButton.setTitle("+ New", for: .normal)
//            newButton.titleLabel?.textColor = .gfGreen
//            newButton.titleLabel?.font = .boldSystemFont(ofSize: 14)
//            newButton.contentHorizontalAlignment = .right
//            newButton.tag = 99421
//            header.addSubview(newButton)
        case 2:
            label.text = "Select a Channel - Contributor"

        default: break
        }

        
        return header
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        switch indexPath.section {
        case 1:
            switch indexPath.row {
            case 0:
                FinalizeViewModel.channelId = User.userId
            default:
                let row = indexPath.row - 1
                FinalizeViewModel.channelId = User.flexter.channelsOwned?[row].channelId
            }
        case 2:
            FinalizeViewModel.channelId = User.flexter.channelsParticipated?[indexPath.row].channelId
        case 3: // these can be individually implemented for some popular services
            if let im = flexImage {
                var items: [Any] = []
                if FinalizeViewModel.captionText != "" {
                    items = [im, FinalizeViewModel.captionText]
                } else {
                    items = [im]
                }
                let avc = UIActivityViewController(activityItems: items, applicationActivities: nil)
                if #available(iOS 13.0, *) {
                    avc.activityItemsConfiguration = [UIActivity.ActivityType.message] as? UIActivityItemsConfigurationReading
                } else {
                    // Fallback on earlier versions
                }
                avc.excludedActivityTypes = [UIActivity.ActivityType.addToReadingList]
                avc.popoverPresentationController?.sourceView = self.view
                self.present(avc, animated: true, completion: nil)
            }
        default: break
        }
    }
}

extension FinalizeViewController: FinalizeDelegate {
    
    func didTapForFinalizeAction(_ action: FinalizeAction) {
        return
    }
    
    func updatetheTextView() {
        textView?.text = FinalizeViewModel.captionText
    }
}
