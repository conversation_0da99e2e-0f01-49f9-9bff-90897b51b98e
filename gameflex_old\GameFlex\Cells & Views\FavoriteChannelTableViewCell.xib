<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17506" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17505"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="iN0-l3-epB" customClass="FavoriteChannelTableViewCell" customModule="GameFlex" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="414" height="201"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" dataMode="none" translatesAutoresizingMaskIntoConstraints="NO" id="DHM-sb-hgC">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="201"/>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <collectionViewFlowLayout key="collectionViewLayout" minimumLineSpacing="10" minimumInteritemSpacing="10" id="zuI-DY-c1z">
                        <size key="itemSize" width="128" height="128"/>
                        <size key="headerReferenceSize" width="0.0" height="0.0"/>
                        <size key="footerReferenceSize" width="0.0" height="0.0"/>
                        <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                    </collectionViewFlowLayout>
                </collectionView>
            </subviews>
            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
            <constraints>
                <constraint firstAttribute="bottom" secondItem="DHM-sb-hgC" secondAttribute="bottom" id="919-qS-6R8"/>
                <constraint firstItem="DHM-sb-hgC" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" id="P5R-ma-o0Z"/>
                <constraint firstAttribute="trailing" secondItem="DHM-sb-hgC" secondAttribute="trailing" id="sk6-of-txg"/>
                <constraint firstItem="DHM-sb-hgC" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="vhi-kS-IzC"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="collectionView" destination="DHM-sb-hgC" id="eO1-sV-eMD"/>
            </connections>
            <point key="canvasLocation" x="137.68115942028987" y="354.57589285714283"/>
        </view>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
