//
//  FlareHeaderView.swift
//  GameFlex
//
//  Created by <PERSON> on 7/18/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

class FlareHeaderView: UICollectionReusableView {
    
    @IBOutlet weak var headButton: UIButton!
    @IBOutlet weak var favsButton: UIButton!
    @IBOutlet weak var collectionView: UICollectionView!
    @IBOutlet weak var favsUnderImageView: UIImageView!
        
    weak var delegate: CameraDelegate?
    
    static var viewIdentifier = String(describing: FlareHeaderView.self)
    
    // MARK: - Lifecycle

    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
    }

    override init(frame: CGRect) {
        super.init(frame: frame)
    }
    
    func setupView(_ showFavs: Bool = false) {
        headButton.setImage(#imageLiteral(resourceName: "homeSelected"), for: .normal)
        favsButton.setImage(#imageLiteral(resourceName: "favs"), for: .normal)
        favsUnderImageView.isHidden = true
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(UINib(nibName: FlareHeaderLabelCollectionViewCell.cellIdentifier, bundle: nil), forCellWithReuseIdentifier: FlareHeaderLabelCollectionViewCell.cellIdentifier)
        if showFavs {
            favsButton.setImage(#imageLiteral(resourceName: "favsSelected"), for: .normal)
            favsUnderImageView.isHidden = false
            headButton.setImage(#imageLiteral(resourceName: "home"), for: .normal)
            cancelTheHighLight()
        }
        collectionView.reloadData()
        if var index = CameraViewModel.flareTypes.firstIndex(of: CameraViewModel.state ) {
            if CameraViewModel.flareTypes[index] == .favs {
                index = 0
            } else {
                deHighlightButtons()
            }
            collectionView.scrollToItem(at: IndexPath(item: index, section: 0), at: .left, animated: true)
        }
    }
    
    @IBAction func didTapButton(_ sender: UIButton) {
        switch sender {
        case headButton:
            headButton.setImage(#imageLiteral(resourceName: "homeSelected"), for: .normal)
            favsButton.setImage(#imageLiteral(resourceName: "favs"), for: .normal)
            favsUnderImageView.isHidden = true
            delegate?.didTapForCameraAction(.flare)
        case favsButton:
            favsButton.setImage(#imageLiteral(resourceName: "favsSelected"), for: .normal)
            favsUnderImageView.isHidden = false
            headButton.setImage(#imageLiteral(resourceName: "home"), for: .normal)
            delegate?.didTapForCameraAction(.favs)
        default: break
        }
        cancelTheHighLight()
    }
    
    func deHighlightButtons() {
        favsButton.setImage(#imageLiteral(resourceName: "favs"), for: .normal)
        favsUnderImageView.isHidden = true
        headButton.setImage(#imageLiteral(resourceName: "home"), for: .normal)
    }

    private func cancelTheHighLight() {
        if CameraViewModel.selectedFlareCategoryIndexPath != nil {
            if let cell = collectionView.cellForItem(at: CameraViewModel.selectedFlareCategoryIndexPath!) as? FlareHeaderLabelCollectionViewCell {
                cell.showAsSelected(false)
                CameraViewModel.selectedFlareCategoryIndexPath = nil
                collectionView.reloadData()
            }
        }
    }
}

extension FlareHeaderView: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return CameraViewModel.flareCategories.count + 1
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: FlareHeaderLabelCollectionViewCell.cellIdentifier, for: indexPath) as? FlareHeaderLabelCollectionViewCell else { return FlareHeaderLabelCollectionViewCell() }
        cell.titleLabel.text = CameraViewModel.flareCategories[indexPath.row]?.keys.first
        let selected = CameraViewModel.state == CameraViewModel.flareTypes[indexPath.row]
        cell.showAsSelected(selected)
        
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        
        if indexPath.row == CameraViewModel.flareCategories.count { // last item is a spacer
            return CGSize(width: 20.0, height: 44.0)
        }
        
        let tempLabel = UILabel(frame: CGRect(x: 0, y: 0, width: 100, height: 22))
        tempLabel.text = CameraViewModel.flareCategories[indexPath.row]?.keys.first
        let width = tempLabel.width(.boldSystemFont(ofSize: 14))
        return CGSize(width: width + 8.0, height: 44.0)
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        
        if CameraViewModel.selectedFlareCategoryIndexPath != nil {
            if let cell = collectionView.cellForItem(at: CameraViewModel.selectedFlareCategoryIndexPath!) as? FlareHeaderLabelCollectionViewCell {
                cell.showAsSelected(false)
            }
        }
        CameraViewModel.selectedFlareCategoryIndexPath = indexPath
        if let cell = collectionView.cellForItem(at: indexPath) as? FlareHeaderLabelCollectionViewCell {
            cell.showAsSelected(true)
        }
        // trigger the view
        let cellType = CameraViewModel.flareTypes[indexPath.row]
        if CameraViewModel.flareDictionary[cellType] != nil {
            delegate?.didTapForCameraAction(CameraViewModel.flareDictionary[cellType] ?? .flare)
        }
    }
}
