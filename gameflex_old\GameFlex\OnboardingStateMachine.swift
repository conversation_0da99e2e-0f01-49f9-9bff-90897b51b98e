//
//  OnboardingStateMachine.swift
//  GameFlex
//
//  Created by <PERSON> on 11/12/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

enum OnboardState: Int {
    case none, first, intro, permissions, cameraWillAppear, login, flexterName, age, whatsnew
}

struct OnboardingStateMachine {
    
    static let shared = OnboardingStateMachine()
        
    static var existingAccount = false // used to avoid the getFlexterName stage
    
    // The flow: none -> (movie) intro,
    // next login show whats new
    // then when createFlex tab is tapped, login -> flexterName -> age
    // once completed, show whatsnew next time
    static var state: OnboardState {
        get { return GFDefaults.onboardState }
        set { GFDefaults.onboardState = newValue }
    }
    
    static var dob = false
    static let kAgeViewTag = 199332

    
    static func didComplete(this: OnboardState) {
        switch this {
        
        case .none:
            DispatchQueue.main.async {
                let fvc = FirstViewController.storyboardInstance()
                if let rvc = UIApplication.shared.delegate?.window??.rootViewController as? RootViewController {
                    rvc.transition(to: fvc)
                }
            }
        case .first:
            if state.rawValue > 1 {
                didComplete(this: .intro)
                return
            }
            DispatchQueue.main.async {
                // TODO: FOR TESTING ONLY
                //                didComplete(this: .flexterName)
                //                return
                // TODO: END OF TESTING
                let intro = QuickIntroViewController.storyboardInstance()
                if let rvc = UIApplication.shared.delegate?.window??.rootViewController as? RootViewController {
                    rvc.transition(to: intro)
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.75) {
                        intro.startTheAnimation()
                    }
                }
                
            }

        case .intro:
            if Int(User.app ?? "28") != User.lastVSeen {
                User.lastVSeen = Int(User.app ?? "28") ?? 29
                let wvc = WhatsNewViewController.storyboardInstance()
                let nc = GFNavigationController(rootViewController: wvc)
                if let rvc = UIApplication.shared.delegate?.window??.rootViewController as? RootViewController {
                    rvc.transition(to: nc)
                }
                return
            }
            state = .intro
            let lp = LandingPageViewController.storyboardInstance()
            lp.isFromIntro = true
            if let rvc = UIApplication.shared.delegate?.window??.rootViewController as? RootViewController {
                rvc.transition(to: lp)
            }
        case .permissions:
            didComplete(this: .cameraWillAppear)
        case .cameraWillAppear:
            guard User.isLoggedIn else {
                let svc = SignUpViewController.storyboardInstance()
                let nc = GFNavigationController(rootViewController: svc)
                nc.modalPresentationStyle = .overCurrentContext
                if let rvc = UIApplication.shared.delegate?.window??.rootViewController as? RootViewController {
                    rvc.transition(to: nc)
                }
                return
            }
        case .login:
            guard !existingAccount else {
                existingAccount = false
                didComplete(this: .age)
                return
            }
            let gfnvc = GetFlexterNameViewController.storyboardInstance()
            let nc = GFNavigationController(rootViewController: gfnvc)
            nc.modalPresentationStyle = .overCurrentContext
            if let rvc = UIApplication.shared.delegate?.window??.rootViewController as? RootViewController {
                rvc.transition(to: nc)
            }

        case .flexterName:
            if User.dateOfBirth == nil {
                let avc = AgeViewController.storyboardInstance()
                if let rvc = UIApplication.shared.delegate?.window??.rootViewController as? RootViewController {
                    if let nc = rvc.children[0] as? GFNavigationController { // from getFlexterNameViewController
                        nc.pushViewController(avc, animated: true)
                    }
                }
                return
            }
            didComplete(this: .age)
        case .age:
            let lp = LandingPageViewController.storyboardInstance()
            if let rvc = UIApplication.shared.delegate?.window??.rootViewController as? RootViewController {
                rvc.transition(to: lp)
                lp.selectedIndex = lp.flexIndex
            }

        case .whatsnew:
            let lp = LandingPageViewController.storyboardInstance()
            if let rvc = UIApplication.shared.delegate?.window??.rootViewController as? RootViewController {
                rvc.transition(to: lp)
                lp.selectedIndex = 0
            }
        }
    }
    
    // appDelegate starts here
    static func processOnboardState(at vc: UIViewController?) -> UIViewController {
        return RootViewController()
//            return FirstViewController.storyboardInstance()
    }
    
}
