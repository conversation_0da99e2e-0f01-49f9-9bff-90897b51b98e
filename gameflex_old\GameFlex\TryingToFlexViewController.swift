//
//  TryingToFlexViewController.swift
//  GameFlex
//
//  Created by <PERSON> on 7/5/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import AVFoundation
import AVKit
import Photos
import FirebaseMessaging

protocol PermissionsDelegate: AnyObject {
    func didFinishWithPermissions()
}

enum PrivacyPermissionsOption: String {
    case library, camera, microphone, notifications
}

class TryingToFlexViewController: GFViewController {
    
    /* UI elements are tied to the top. However, cancelButtonTopConstraint is calculated for proportional placement of the view. */
    @IBOutlet weak var cancelButton: UIButton!
    @IBOutlet weak var cancelButtonTopConstraint: NSLayoutConstraint!
    @IBOutlet weak var tryingLabel: UILabel!
    @IBOutlet weak var subTryingLabel: UILabel!
    @IBOutlet weak var photosAccessButton: UIButton!
    @IBOutlet weak var cameraAccessButton: UIButton!
    @IBOutlet weak var cameraCheckImageView: UIImageView!
    @IBOutlet weak var libraryCheckImageView: UIImageView!
//    @IBOutlet weak var microphoneAccessButton: UIButton!
//    @IBOutlet weak var microphoneCheckImageView: UIImageView!
    @IBOutlet weak var pushNotificationsButton: UIButton!
    @IBOutlet weak var pushCheckImageView: UIImageView!
    
    var selectionToUpdate: PrivacyPermissionsOption?
    var imagePicked: UIImage!
    var buttonsArray: [UIButton] = []
    var goToSettingsArray: [UIButton] = []
    
    weak var delegate: PermissionsDelegate?
    
    static func storyboardInstance() -> TryingToFlexViewController {
        let sb = UIStoryboard(name: "Main", bundle: nil)
        return sb.instantiateViewController(withIdentifier: String(describing: TryingToFlexViewController.self)) as! TryingToFlexViewController
    }

    
    // MARK: LifeCycle
    override func viewDidLoad() {
        super.viewDidLoad()
        tryingLabel.text = "trying.title".localized
        subTryingLabel.text = "trying.subTitle".localized
        subTryingLabel.textColor = .gfGrayText
        runTheButtons()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        cancelButtonTopConstraint.constant = 200 / 812 * view.frame.size.height
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        title = "trying.navTitle".localized
    }
    
    private func makeButtonGoToSettings(_ type: PrivacyPermissionsOption) {
        let title = "trying.goToSettings".localized
        DispatchQueue.main.async {
            switch type {
            case .library:
                self.photosAccessButton.setTitle("\(title)", for: .normal)
                self.photosAccessButton.setTitleColor(UIColor.systemBlue, for: .normal)
            case .camera:
                self.cameraAccessButton.setTitle("\(title)", for: .normal)
                self.cameraAccessButton.setTitleColor(UIColor.systemBlue, for: .normal)
            case .microphone: break
//                self.microphoneAccessButton.setTitle("\(title)", for: .normal)
//                self.microphoneAccessButton.setTitleColor(UIColor.systemBlue, for: .normal)
            case .notifications:
                self.pushNotificationsButton.setTitle("\(title)", for: .normal)
                self.pushNotificationsButton.setTitleColor(UIColor.systemBlue, for: .normal)
            }
        }
    }
    
    private func runTheButtons() {
        buttonsArray = []
        if GFDefaults.shared.hasAskedCameraPermission {
            if !buttonsArray.contains(cameraAccessButton) {
                buttonsArray.append(cameraAccessButton)
            }
            cameraAccessButton.setTitle("trying.cameraButtonPermission".localized, for: .normal)
            cameraAccessButton.setTitleColor(UIColor.gfGrayText, for: .normal)
            cameraCheckImageView.isHidden = false
        } else {
            cameraCheckImageView.isHidden = true
            cameraAccessButton.setTitle("trying.cameraButton".localized, for: .normal)
            cameraAccessButton.setTitleColor(UIColor.gfGreen, for: .normal)
        }
        if GFDefaults.shared.hasAskedLibraryPermission {
            if !buttonsArray.contains(photosAccessButton) {
                buttonsArray.append(photosAccessButton)
            }
            libraryCheckImageView.isHidden = false
            photosAccessButton.setTitle("trying.photosButtonPermission".localized, for: .normal)
            photosAccessButton.setTitleColor(UIColor.gfGrayText, for: .normal)
        } else {
            libraryCheckImageView.isHidden = true
            photosAccessButton.setTitle("trying.photosButton".localized, for: .normal)
            photosAccessButton.setTitleColor(UIColor.gfGreen, for: .normal)
        }
//        if GFDefaults.shared.hasAskedMicrophonePermission {
//            if !buttonsArray.contains(microphoneAccessButton) {
//                buttonsArray.append(microphoneAccessButton)
//            }
//            microphoneCheckImageView.isHidden = false
//            microphoneAccessButton.setTitle("trying.microphoneButtonPermission".localized, for: .normal)
//            microphoneAccessButton.setTitleColor(UIColor.gfGrayText, for: .normal)
//        } else {
//            microphoneCheckImageView.isHidden = true
//            microphoneAccessButton.setTitle("trying.microphoneButton".localized, for: .normal)
//            microphoneAccessButton.setTitleColor(UIColor.gfGreen, for: .normal)
//        }
        if GFDefaults.shared.hasAskedPushPermission {
            if !buttonsArray.contains(pushNotificationsButton) {
                buttonsArray.append(pushNotificationsButton)
            }
            pushCheckImageView.isHidden = false
            pushNotificationsButton.setTitle("trying.pushButtonPermission".localized, for: .normal)
            pushNotificationsButton.setTitleColor(UIColor.gfGrayText, for: .normal)
        } else {
            pushCheckImageView.isHidden = true
            pushNotificationsButton.setTitle("trying.pushButton".localized, for: .normal)
            pushNotificationsButton.setTitleColor(UIColor.gfGreen, for: .normal)
        }
        if buttonsArray.count == 3 {
            dismiss(animated: true, completion: {
                self.delegate?.didFinishWithPermissions()
            })
        }
    }
    
    @IBAction func didTapButton(_ sender: Any) {
        guard (sender as! UIButton).title(for: .normal) != "trying.goToSettings".localized else {
            if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
               UIApplication.shared.open(settingsUrl)
             }
            return
        }
        switch sender as! UIButton {
        case cancelButton:
            dismiss(animated: true, completion: {
                self.delegate?.didFinishWithPermissions()
            })
        case photosAccessButton:
            checkAuthorizationStatus(.library) {success,_ in
                GFDefaults.shared.hasAskedLibraryPermission = true
                if success {
                    DispatchQueue.main.async {
                        self.runTheButtons()
                    }
                } else {
                    DispatchQueue.main.async {
                        self.makeButtonGoToSettings(.library)
                    }
                }
            }
        case cameraAccessButton:
            checkAuthorizationStatus(.camera) {success,_ in
                GFDefaults.shared.hasAskedCameraPermission = true
                if success {
                    DispatchQueue.main.async {
                        self.runTheButtons()
                    }
                } else {
                    DispatchQueue.main.async {
                        self.makeButtonGoToSettings(.camera)
                    }
                }
            }
//        case microphoneAccessButton:
//            checkAuthorizationStatus(.microphone) {success,_ in
//                GFDefaults.shared.hasAskedMicrophonePermission = true
//                if success {
//                    DispatchQueue.main.async {
//                        self.runTheButtons()
//                    }
//                } else {
//                    DispatchQueue.main.async {
//                        self.makeButtonGoToSettings(.microphone)
//                    }
//                }
//            }
        case pushNotificationsButton:
            checkAuthorizationStatus(.notifications) {success,_ in
                GFDefaults.shared.hasAskedPushPermission = true
                DispatchQueue.main.async {
                    (UIApplication.shared.delegate as! AppDelegate).registerForPushNotification(UIApplication.shared)
                }
                if success {
                    DispatchQueue.main.async {
                        Messaging.messaging().subscribe(toTopic: "System") { error in
                        }
                        self.runTheButtons()
                    }
                } else {
                    DispatchQueue.main.async {
                        self.makeButtonGoToSettings(.notifications)
//                        self.showGoToSettingsAlert([.notifications])
                    }
                }
            }
        default: break
        }
    }    
}

