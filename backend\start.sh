#!/bin/bash

# GameFlex Backend Development Setup Script
# This script starts the Supabase development environment

set -e

echo "🚀 Starting GameFlex Development Backend..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose > /dev/null 2>&1; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose and try again."
    exit 1
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p volumes/storage
mkdir -p volumes/functions

# Set permissions for volumes
echo "🔐 Setting permissions..."
chmod -R 755 volumes/

# Load environment variables
if [ -f .env ]; then
    echo "📋 Loading environment variables..."
    export $(cat .env | grep -v '^#' | xargs)
else
    echo "❌ .env file not found. Please create one based on .env.example"
    exit 1
fi

# Start the services
echo "🐳 Starting Docker containers..."
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 30

# Check if services are healthy
echo "🔍 Checking service health..."

# Check if database is ready
until docker-compose exec -T db pg_isready -U postgres -h localhost; do
    echo "Waiting for database..."
    sleep 2
done

echo "✅ Database is ready!"

# Check if Kong is ready
until curl -f http://localhost:${KONG_HTTP_PORT:-54321}/health > /dev/null 2>&1; do
    echo "Waiting for API Gateway..."
    sleep 2
done

echo "✅ API Gateway is ready!"

# Check if Studio is ready
until curl -f http://localhost:${STUDIO_PORT:-54323}/api/profile > /dev/null 2>&1; do
    echo "Waiting for Supabase Studio..."
    sleep 2
done

echo "✅ Supabase Studio is ready!"

echo ""
echo "🎉 GameFlex Development Backend is now running!"
echo ""
echo "📊 Supabase Studio: http://localhost:${STUDIO_PORT:-54323}"
echo "🔌 API URL: http://localhost:${KONG_HTTP_PORT:-54321}"
echo "🗄️  Database: localhost:${POSTGRES_PORT:-54322}"
echo ""
echo "🔑 Development Credentials:"
echo "   Email: <EMAIL>"
echo "   Password: GameFlex123!"
echo ""
echo "🔑 Admin Credentials:"
echo "   Email: <EMAIL>"
echo "   Password: AdminGameFlex123!"
echo ""
echo "🔧 To stop the backend: docker-compose down"
echo "🔧 To view logs: docker-compose logs -f"
echo "🔧 To reset data: docker-compose down -v && ./start.sh"
echo ""
