//
//  FinalizeViewModel.swift
//  GameFlex
//
//  Created by <PERSON> on 10/18/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

enum FinalizeCellType {
    case top, recent, frequent, other
}

struct FinalizeViewModel {
    
    static let shared = FinalizeViewModel()
    
    static var selectedHashTags: [String] = []
    static var previouslySelectedHashTags: [String] = []
    
    static var captionText = ""

    static var sequenceOfCells: [FinalizeCellType] {
        if HashTags.hashTagsArray.isEmpty {
            return [.top, .other]
        }
        return [.top, .recent, .frequent, .other]
    }
    
    static func updateCaption() {
        // remove all old hashtags
        for str in previouslySelectedHashTags {
            if let range = captionText.range(of: str) {
                captionText.removeSubrange(range)
            }
        }
        captionText = captionText.replacingOccurrences(of: "  ", with: " ")
        // add the new hashtags
        for str in selectedHashTags {
            captionText = "\(captionText) \(str)"
        }
        previouslySelectedHashTags = selectedHashTags
    }
    
    static var channelId = User.userId
}
