// GameFlex Edge Functions
// Main entry point for Supabase Edge Functions

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

console.log("GameFlex Edge Functions started!")

serve(async (req) => {
  const { pathname } = new URL(req.url)

  // Health check endpoint
  if (pathname === "/health") {
    return new Response(
      JSON.stringify({ 
        status: "healthy", 
        timestamp: new Date().toISOString(),
        service: "gameflex-functions"
      }),
      { 
        headers: { "Content-Type": "application/json" },
        status: 200 
      }
    )
  }

  // Hello world endpoint
  if (pathname === "/hello") {
    return new Response(
      JSON.stringify({ 
        message: "Hello from GameFlex Edge Functions!",
        timestamp: new Date().toISOString()
      }),
      { 
        headers: { "Content-Type": "application/json" },
        status: 200 
      }
    )
  }

  // Default response
  return new Response(
    JSON.stringify({ 
      error: "Function not found",
      available_endpoints: ["/health", "/hello"]
    }),
    { 
      headers: { "Content-Type": "application/json" },
      status: 404 
    }
  )
})
