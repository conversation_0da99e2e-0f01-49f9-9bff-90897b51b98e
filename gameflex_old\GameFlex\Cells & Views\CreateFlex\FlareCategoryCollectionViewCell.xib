<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17703"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="iN0-l3-epB" customClass="FlareCategoryCollectionViewCell" customModule="GameFlex" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="177" height="50"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="XXd-Ms-Vg7">
                    <rect key="frame" x="0.0" y="0.0" width="177" height="50"/>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                            <integer key="value" value="4"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="color" keyPath="layer.shadowColor">
                            <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="point" keyPath="layer.shadowOffset">
                            <point key="value" x="2" y="2"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </imageView>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="arrow-circle-right-outline" translatesAutoresizingMaskIntoConstraints="NO" id="Phd-aA-nH4">
                    <rect key="frame" x="10" y="16" width="18" height="18"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="18" id="0p1-Cu-ggK"/>
                        <constraint firstAttribute="width" constant="18" id="ufI-az-y48"/>
                    </constraints>
                </imageView>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="uu8-hQ-RXq">
                    <rect key="frame" x="36" y="4" width="49.5" height="42"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="19"/>
                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <nil key="highlightedColor"/>
                </label>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="XXd-Ms-Vg7" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" id="2Z0-Ka-sD1"/>
                <constraint firstAttribute="bottom" secondItem="uu8-hQ-RXq" secondAttribute="bottom" constant="4" id="OLj-mv-ZFl"/>
                <constraint firstAttribute="bottom" secondItem="XXd-Ms-Vg7" secondAttribute="bottom" id="UNy-jS-QSH"/>
                <constraint firstItem="uu8-hQ-RXq" firstAttribute="leading" secondItem="XXd-Ms-Vg7" secondAttribute="leading" constant="36" id="bXT-Ib-gqj"/>
                <constraint firstAttribute="trailing" secondItem="XXd-Ms-Vg7" secondAttribute="trailing" id="bau-BB-Vt2"/>
                <constraint firstItem="Phd-aA-nH4" firstAttribute="centerY" secondItem="iN0-l3-epB" secondAttribute="centerY" id="idN-KZ-CpL"/>
                <constraint firstItem="Phd-aA-nH4" firstAttribute="leading" secondItem="XXd-Ms-Vg7" secondAttribute="leading" constant="10" id="jxn-1I-ro2"/>
                <constraint firstItem="XXd-Ms-Vg7" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="nkx-dw-TX3"/>
                <constraint firstItem="uu8-hQ-RXq" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="4" id="oZt-gC-YaP"/>
            </constraints>
            <nil key="simulatedTopBarMetrics"/>
            <nil key="simulatedBottomBarMetrics"/>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="arrowImageView" destination="Phd-aA-nH4" id="0m9-Jx-Mef"/>
                <outlet property="backgroundImageView" destination="XXd-Ms-Vg7" id="eM8-Ho-gIO"/>
                <outlet property="backgroundImageViewLeadingConstraint" destination="2Z0-Ka-sD1" id="0p9-hA-l3G"/>
                <outlet property="backgroundImageViewTrailingConstraint" destination="bau-BB-Vt2" id="Ghg-Gt-FZk"/>
                <outlet property="titleLabel" destination="uu8-hQ-RXq" id="Kz8-vy-5PK"/>
            </connections>
            <point key="canvasLocation" x="145.6521739130435" y="155.35714285714286"/>
        </view>
    </objects>
    <resources>
        <image name="arrow-circle-right-outline" width="22" height="22"/>
    </resources>
</document>
