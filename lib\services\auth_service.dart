import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'supabase_service.dart';

class AuthService {
  static AuthService? _instance;
  static AuthService get instance => _instance ??= AuthService._();
  
  AuthService._();
  
  SupabaseClient get _client => SupabaseService.instance.client;
  
  // Storage keys for JWT tokens
  static const String _accessTokenKey = 'supabase_access_token';
  static const String _refreshTokenKey = 'supabase_refresh_token';
  
  /// Sign up with email and password
  Future<AuthResponse> signUp({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _client.auth.signUp(
        email: email,
        password: password,
      );
      
      if (response.session != null) {
        await _saveTokens(response.session!);
      }
      
      return response;
    } catch (e) {
      rethrow;
    }
  }
  
  /// Sign in with email and password
  Future<AuthResponse> signIn({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _client.auth.signInWithPassword(
        email: email,
        password: password,
      );
      
      if (response.session != null) {
        await _saveTokens(response.session!);
      }
      
      return response;
    } catch (e) {
      rethrow;
    }
  }
  
  /// Sign out
  Future<void> signOut() async {
    try {
      await _client.auth.signOut();
      await _clearTokens();
    } catch (e) {
      rethrow;
    }
  }
  
  /// Get current user
  User? get currentUser => _client.auth.currentUser;
  
  /// Get current session
  Session? get currentSession => _client.auth.currentSession;
  
  /// Check if user is authenticated
  bool get isAuthenticated => currentUser != null;
  
  /// Get auth state changes stream
  Stream<AuthState> get authStateChanges => _client.auth.onAuthStateChange;
  
  /// Save JWT tokens to local storage
  Future<void> _saveTokens(Session session) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_accessTokenKey, session.accessToken);
    if (session.refreshToken != null) {
      await prefs.setString(_refreshTokenKey, session.refreshToken!);
    }
  }
  
  /// Clear JWT tokens from local storage
  Future<void> _clearTokens() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_accessTokenKey);
    await prefs.remove(_refreshTokenKey);
  }
  
  /// Get stored access token
  Future<String?> getStoredAccessToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_accessTokenKey);
  }
  
  /// Get stored refresh token
  Future<String?> getStoredRefreshToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_refreshTokenKey);
  }
  
  /// Restore session from stored tokens
  Future<bool> restoreSession() async {
    try {
      final accessToken = await getStoredAccessToken();
      final refreshToken = await getStoredRefreshToken();
      
      if (accessToken != null && refreshToken != null) {
        await _client.auth.setSession(refreshToken);
        return true;
      }
      return false;
    } catch (e) {
      // If session restoration fails, clear stored tokens
      await _clearTokens();
      return false;
    }
  }
  
  /// Reset password
  Future<void> resetPassword(String email) async {
    try {
      await _client.auth.resetPasswordForEmail(email);
    } catch (e) {
      rethrow;
    }
  }
}
