import 'package:supabase_flutter/supabase_flutter.dart';

class SupabaseService {
  static SupabaseService? _instance;
  static SupabaseService get instance => _instance ??= SupabaseService._();
  
  SupabaseService._();
  
  // Supabase configuration - these should match your backend/.env file
  static const String supabaseUrl = 'http://localhost:54321';
  static const String supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0';
  
  SupabaseClient get client => Supabase.instance.client;
  
  static Future<void> initialize() async {
    await Supabase.initialize(
      url: supabaseUrl,
      anonKey: supabase<PERSON><PERSON><PERSON><PERSON>,
      debug: true, // Enable debug mode for development
    );
  }
  
  // Get current user
  User? get currentUser => client.auth.currentUser;
  
  // Get current session
  Session? get currentSession => client.auth.currentSession;
  
  // Check if user is authenticated
  bool get isAuthenticated => currentUser != null;
  
  // Get auth stream for listening to auth state changes
  Stream<AuthState> get authStateChanges => client.auth.onAuthStateChange;
}
