<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17156" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17125"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB" customClass="FilterCollectionViewCell" customModule="GameFlex" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="103" height="140"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Bza-y6-sWi" customClass="SelectedFilterView" customModule="GameFlex" customModuleProvider="target">
                    <rect key="frame" x="5" y="28.5" width="93" height="93"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="93" id="MGM-s0-dom"/>
                        <constraint firstAttribute="width" constant="93" id="w95-bm-HoN"/>
                    </constraints>
                </view>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="hsS-8I-GId">
                    <rect key="frame" x="12" y="35.5" width="79" height="79"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="79" id="Cu5-km-6AI"/>
                        <constraint firstAttribute="height" constant="79" id="LRR-Q2-5F7"/>
                    </constraints>
                </imageView>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="01a-fE-qT4">
                    <rect key="frame" x="36" y="2" width="31" height="24.5"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="24.5" id="JTi-G3-qos"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <nil key="highlightedColor"/>
                </label>
                <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="GUU-A6-bO0">
                    <rect key="frame" x="12" y="36" width="79" height="79"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <connections>
                        <action selector="didTapButton:" destination="iN0-l3-epB" eventType="touchUpInside" id="zdB-5b-Kqy"/>
                    </connections>
                </button>
            </subviews>
            <viewLayoutGuide key="safeArea" id="vUN-kp-3ea"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="Bza-y6-sWi" firstAttribute="centerY" secondItem="hsS-8I-GId" secondAttribute="centerY" id="2kC-lb-5JT"/>
                <constraint firstItem="Bza-y6-sWi" firstAttribute="centerX" secondItem="hsS-8I-GId" secondAttribute="centerX" id="GrF-f0-hrv"/>
                <constraint firstItem="hsS-8I-GId" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="KM4-3a-D3q"/>
                <constraint firstItem="01a-fE-qT4" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="2" id="U2V-Te-aix"/>
                <constraint firstItem="hsS-8I-GId" firstAttribute="top" secondItem="01a-fE-qT4" secondAttribute="bottom" constant="9" id="iE1-OV-ECL"/>
                <constraint firstItem="01a-fE-qT4" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="vM7-so-KEa"/>
            </constraints>
            <nil key="simulatedTopBarMetrics"/>
            <nil key="simulatedBottomBarMetrics"/>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="filterImageView" destination="hsS-8I-GId" id="gz7-WR-fnh"/>
                <outlet property="selectFilterButton" destination="GUU-A6-bO0" id="05E-im-2o6"/>
                <outlet property="selectedFilterView" destination="Bza-y6-sWi" id="S3w-3J-pJf"/>
                <outlet property="titleLabel" destination="01a-fE-qT4" id="4NU-hA-kZF"/>
            </connections>
            <point key="canvasLocation" x="144.20289855072465" y="-206.25"/>
        </view>
    </objects>
</document>
