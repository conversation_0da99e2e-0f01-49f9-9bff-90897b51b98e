<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="16097.2" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="16087"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="iN0-l3-epB" customClass="TextFieldTableViewCell" customModule="GameFlex" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="414" height="55"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ZkO-a5-c2J">
                    <rect key="frame" x="16" y="5" width="382" height="45"/>
                    <subviews>
                        <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="dje-h0-vRV">
                            <rect key="frame" x="10" y="4" width="362" height="37"/>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <textInputTraits key="textInputTraits"/>
                        </textField>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="dra-00-sG8">
                            <rect key="frame" x="346" y="10.5" width="24" height="24"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="24" id="8vH-0a-sae"/>
                                <constraint firstAttribute="width" constant="24" id="lVx-dn-JBE"/>
                            </constraints>
                            <state key="normal" image="clearTextField"/>
                            <connections>
                                <action selector="didTapClearTextButton" destination="iN0-l3-epB" eventType="touchUpInside" id="vQ6-d9-aca"/>
                            </connections>
                        </button>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="W86-cC-k6A">
                            <rect key="frame" x="308" y="7.5" width="30" height="30"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="30" id="ZJV-kR-wI3"/>
                                <constraint firstAttribute="height" constant="30" id="swE-Ya-yBP"/>
                            </constraints>
                            <state key="normal" image="eye"/>
                            <connections>
                                <action selector="hideOrShowTheEyeButton" destination="iN0-l3-epB" eventType="touchUpInside" id="4hG-mU-Mi3"/>
                            </connections>
                        </button>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor" cocoaTouchSystemColor="whiteColor"/>
                    <constraints>
                        <constraint firstItem="dra-00-sG8" firstAttribute="leading" secondItem="W86-cC-k6A" secondAttribute="trailing" constant="8" id="4kL-N1-HbU"/>
                        <constraint firstAttribute="bottom" secondItem="dje-h0-vRV" secondAttribute="bottom" constant="4" id="Lqd-Bw-OYg"/>
                        <constraint firstAttribute="trailing" secondItem="dje-h0-vRV" secondAttribute="trailing" constant="10" id="MtN-Da-muX"/>
                        <constraint firstItem="W86-cC-k6A" firstAttribute="centerY" secondItem="ZkO-a5-c2J" secondAttribute="centerY" id="NmG-0F-BOF"/>
                        <constraint firstAttribute="trailing" secondItem="dra-00-sG8" secondAttribute="trailing" constant="12" id="Pgk-Lr-yIz"/>
                        <constraint firstItem="dje-h0-vRV" firstAttribute="leading" secondItem="ZkO-a5-c2J" secondAttribute="leading" constant="10" id="R08-Ab-OHm"/>
                        <constraint firstItem="dje-h0-vRV" firstAttribute="top" secondItem="ZkO-a5-c2J" secondAttribute="top" constant="4" id="mCr-RI-E8e"/>
                        <constraint firstItem="dra-00-sG8" firstAttribute="centerY" secondItem="ZkO-a5-c2J" secondAttribute="centerY" id="xK3-Dh-8k1"/>
                    </constraints>
                </view>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="ZkO-a5-c2J" secondAttribute="trailing" constant="16" id="Bb3-y2-G3k"/>
                <constraint firstItem="ZkO-a5-c2J" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="16" id="RbG-sk-mhp"/>
                <constraint firstAttribute="bottom" secondItem="ZkO-a5-c2J" secondAttribute="bottom" constant="5" id="ihc-3V-Pyz"/>
                <constraint firstItem="ZkO-a5-c2J" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="5" id="jbq-wx-8yC"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="clearTextButton" destination="dra-00-sG8" id="kFT-iV-fYV"/>
                <outlet property="clearTextButtonWidthConstraint" destination="lVx-dn-JBE" id="fyL-RX-DX5"/>
                <outlet property="grayBox" destination="ZkO-a5-c2J" id="dYi-CT-lKK"/>
                <outlet property="secureTextButton" destination="W86-cC-k6A" id="thT-5Z-uhq"/>
                <outlet property="textField" destination="dje-h0-vRV" id="8WN-1F-EQH"/>
            </connections>
            <point key="canvasLocation" x="217.39130434782609" y="-204.57589285714286"/>
        </view>
    </objects>
    <resources>
        <image name="clearTextField" width="24" height="24"/>
        <image name="eye" width="30" height="30"/>
    </resources>
</document>
