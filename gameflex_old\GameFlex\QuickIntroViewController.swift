//
//  QuickIntroViewController.swift
//  GameFlex
//
//  Created by <PERSON> on 9/26/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import LocalAuthentication

enum Pages: CaseIterable {
    case pageZero, pageOne, pageTwo
    
    var name: String {
        switch self {
        case .pageZero: return "quickIntro.0.title".localized
        case .pageOne: return "quickIntro.1.title".localized
        case .pageTwo: return "quickIntro.2.title".localized
        }
    }
    
    var message: String {
        switch self {
        case .pageZero: return "quickIntro.0.message".localized
        case .pageOne: return "quickIntro.1.message".localized
        case .pageTwo: return "quickIntro.2.message".localized
        }
    }
    
    var image: UIImage {
        switch self {
        case .pageZero: return #imageLiteral(resourceName: "image 24")
        case .pageOne: return #imageLiteral(resourceName: "image 25")
        case .pageTwo: return #imageLiteral(resourceName: "image 26")
        }
    }
}

class QuickIntroViewController: GFViewController {

    @IBOutlet weak var pageControl: UIPageControl!
    @IBOutlet weak var skipButton: UIButton!
    @IBOutlet weak var headerLabel: UILabel!
    @IBOutlet weak var headerLogo: UIImageView!
    
    @IBOutlet weak var blackMask: UIView!
    @IBOutlet weak var clearView: UIView!
    @IBOutlet weak var gameView: UIImageView!
    @IBOutlet weak var flexView: UIImageView!
    @IBOutlet weak var bigG: UIImageView!
    
    var hiddenObjects: [UIView] = []
        
    var pagesArray: [PageViewController] = []
    
    private var pageController: UIPageViewController?
    var currentIndex = 0
    private var pages: [Pages] = Pages.allCases

    static func storyboardInstance() -> QuickIntroViewController {
        let sb = UIStoryboard(name: "Main", bundle: nil)
        return sb.instantiateViewController(withIdentifier: String(describing: QuickIntroViewController.self)) as! QuickIntroViewController
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setBackgroundGradient()
        setupPageController()
        skipButton.layer.cornerRadius = 8.0
        skipButton.backgroundColor = .gfGreen
        skipButton.setTitle("quickIntro.skipIntro".localized, for: .normal)
        skipButton.setTitleColor(.black, for: .normal)
        headerLabel.text = "Quick Intro"
        pageControl.numberOfPages = pages.count
        hiddenObjects = [headerLogo, pageControl, headerLogo, skipButton, headerLabel]
        pageController?.view.alpha = 0.0
        hiddenObjects.forEach({ $0.isHidden = true })
        bigG.alpha = 0.0
        gameView.isHidden = true
        flexView.isHidden = true
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        var gf = gameView.frame
        gf.origin.x = -500.0
        gameView.frame = gf
        var ff = flexView.frame
        ff.origin.x = 800.0
        flexView.frame = ff
        bigG.transform = CGAffineTransform.init(scaleX: 0.0, y: 0.0)
    }
    
    func startTheAnimation() {
        gameView.isHidden = false
        flexView.isHidden = false
        var gf = gameView.frame
        gf.origin.x = view.frame.size.width/2 - 136.0
        var ff = flexView.frame
        ff.origin.x = view.frame.size.width/2
        
        UIView.animate(withDuration: 1.0) {
            self.bigG.alpha = 1.0
            self.bigG.transform = CGAffineTransform.identity
            self.gameView.frame = gf
            self.flexView.frame = ff
        } completion: { (end) in
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                self.endTheAnimation()
            }
        }
    }
    
    private func endTheAnimation() {
        UIView.animate(withDuration: 0.5) {
            self.blackMask.alpha = 0.0
            self.gameView.alpha = 0.0
            self.flexView.alpha = 0.0
            self.moveTheG()
        }
    }
    
    private func moveTheG() {
        UIView.animate(withDuration: 0.5) {
            self.bigG.transform = CGAffineTransform.init(scaleX: 0.18, y: 0.18)
            if UIDevice.isIPhoneX {
                self.bigG.center = CGPoint(x: self.view.frame.size.width/2 - 65.0 , y: 115.0)
            } else {
                self.bigG.center = CGPoint(x: self.view.frame.size.width/2 - 65.0 , y: 90.0)
            }
        } completion: { (_) in
            self.hiddenObjects.forEach({ $0.isHidden = false })
            let butt = UIButton(frame: self.skipButton.frame)
            butt.backgroundColor = .clear
            butt.addTarget(self, action: #selector(self.skipIntro(_:)), for: .touchUpInside)
            self.view.addSubview(butt)
            self.moveThePageControllerIn()
        }
    }
    
    private func moveThePageControllerIn() {
        var fr = pageController?.view.frame
        fr?.origin.x = view.frame.size.width
        pageController?.view.frame = fr ?? CGRect(origin: CGPoint(x: view.frame.size.width, y: 0), size: view.frame.size)
        UIView.animate(withDuration: 0.5) {
            self.clearView.alpha = 0.0
           self.pageController?.view.frame = self.view.frame
            self.pageController?.view.alpha = 1.0
        }
    }
    
    private func setupPageController() {
        self.pageController = UIPageViewController(transitionStyle: .scroll, navigationOrientation: .horizontal, options: nil)
        self.pageController?.dataSource = self
        self.pageController?.delegate = self
        self.pageController?.view.backgroundColor = .clear
        self.pageController?.view.frame = CGRect(x: 0,y: 0,width: self.view.frame.width,height: self.view.frame.height)
        self.addChild(self.pageController!)
        self.view.addSubview(self.pageController!.view)
        let ivc = PageViewController.storyboardInstance()
        ivc.page = pages[currentIndex]
        pageController?.setViewControllers([ivc], direction: .forward, animated: true, completion: nil)
        self.pageController?.didMove(toParent: self)
        
    }
    
    @objc @IBAction func skipIntro(_ sender: UIButton) {
        OnboardingStateMachine.didComplete(this: .intro)
    }
}

extension QuickIntroViewController: UIPageViewControllerDelegate, UIPageViewControllerDataSource {
    
    func pageViewController(_ pageViewController: UIPageViewController, viewControllerBefore viewController: UIViewController) -> UIViewController? {
        guard let pvc = viewController as? PageViewController else {
            return nil
        }
        
        var newPage: Pages? = pvc.page
        switch pvc.page {
        case .pageZero:
            currentIndex = 0
            newPage = nil
        case .pageOne:
            newPage = .pageZero
            currentIndex = 1
        case .pageTwo:
            newPage = .pageOne
            currentIndex = 2
        }
        pageControl.currentPage = currentIndex
        guard newPage != nil else { return nil }
        let bvc = PageViewController.storyboardInstance()
        
        bvc.page = newPage!
        return bvc
    }
    
    func pageViewController(_ pageViewController: UIPageViewController, viewControllerAfter viewController: UIViewController) -> UIViewController? {
        guard let pvc = viewController as? PageViewController else { return nil }
        
        var newPage: Pages? = pvc.page
        switch pvc.page {
        case .pageZero:
            newPage = .pageOne
            currentIndex = 0
        case .pageOne:
            newPage = .pageTwo
            currentIndex = 1
        case .pageTwo:
            currentIndex = 2
            newPage = nil
        }

        pageControl.currentPage = currentIndex
        guard newPage != nil else { return nil }
        let bvc = PageViewController.storyboardInstance()
        
        bvc.page = newPage!
        return bvc
    }
    
}
