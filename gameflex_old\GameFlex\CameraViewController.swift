//
//  CameraViewController.swift
//  GameFlex
//
//  Created by <PERSON> on 7/5/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import AVFoundation
import AWSRekognition
import SwiftyJSON
import CoreGraphics

enum CameraFaceOption {
    case front, back
}

protocol CameraDelegate: AnyObject {
    func didTapForCameraAction(_ action: CameraActionType)
    func sliderDidChange(_ sender: UISlider)
    func updateDrawingSubviews()
    func didStartDrawing()
    func didEndDrawing()
}

extension CameraDelegate {
    func sliderDidChange(_ sender: UISlider) { /* nothing here */ }
    func updateDrawingSubviews() { /* nothing here */ }
    func didStartDrawing() { /* nothing here */ }
    func didEndDrawing() { /* nothing here */ }
}

class CameraViewController: GFViewController {
    
    @IBOutlet weak var photoPreviewImageView: UIImageView!
    @IBOutlet weak var stickerBaseView: UIView!
    @IBOutlet weak var flexBackgroundView: UIView!
    @IBOutlet weak var photoButton: UIButton!
    @IBOutlet weak var libraryButton: UIButton!
    @IBOutlet weak var bottomButtonView: UIView!
    @IBOutlet weak var previewView: UIView!
    @IBOutlet weak var collectionView: UICollectionView! // for refined controls, other than fonts
    @IBOutlet weak var collectionViewHeight: NSLayoutConstraint!
    @IBOutlet weak var collectionViewBottomConstraint: NSLayoutConstraint!
    @IBOutlet weak var imageEnhancementControlsView: ImageEnhancementControlsView!
    @IBOutlet weak var verticalSlider: UISlider! {
        didSet { // does the rotation to vertical
            var t = CGAffineTransform.identity
            t = t.rotated(by: -CGFloat.pi/2)
            t = t.translatedBy(x: 0, y: -100.0)
            verticalSlider.transform = t
        }
    }
    @IBOutlet weak var lineWidthView: UIView!
    @IBOutlet weak var lineWidthCenterBox: UIView!
    @IBOutlet weak var lineWidthViewConstraint: NSLayoutConstraint!
    
    @IBOutlet weak var trashView: UIView! // for deleting flare and texts
    @IBOutlet weak var trashCan: UIButton!
    @IBOutlet weak var timerView: UIView!
    @IBOutlet weak var timerLabel: UILabel!
    
    var drawView: DrawView?
    var wasDrawState: Bool = false
    
    var timerCounter = 3
    
    var sequenceOfCells = CameraViewModel.sequenceOfCells(type: .buttons)
    var selectedFilter: IndexPath?
    var editServiceIndexPath: IndexPath?
    var toggleForeVersusBackgroundColors: Bool = true
    
    var captureSession: AVCaptureSession!
    var stillImageOutput: AVCapturePhotoOutput!
    var videoPreviewLayer: AVCaptureVideoPreviewLayer!
    
    var hideableComponents: [UIView] = []
    
    var discoveredCameras: [CameraFaceOption: AVCaptureDevice] = [:]
    var currentCamera: CameraFaceOption = .back
    var currentInput: AVCaptureDeviceInput?
    var transformAd: CGFloat = 1.0
    var lastLocation: CGPoint = CGPoint.zero
    var previewCenterOffset: CGPoint = CGPoint.zero // add each of x and y this to get the center
    var lastActiveStickerLocation: CGPoint = CGPoint.zero
    var keyboardHeight: CGFloat = 0.0
    
    var originalPhotoPreview: UIImage?
    
    var rekognition: AWSRekognition?
    
    let settings = AVCapturePhotoSettings(format: [AVVideoCodecKey: AVVideoCodecType.jpeg])

    // MARK: - Lifecycle
    
    static func storyboardInstance() -> CameraViewController {
        let sb = UIStoryboard(name: "Main", bundle: nil)
        return sb.instantiateViewController(withIdentifier: String(describing: CameraViewController.self)) as! CameraViewController
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        hideableComponents = [previewView]
        lineWidthView.layer.cornerRadius = lineWidthView.frame.height/2
        self.lineWidthView.isHidden = false
        self.lineWidthCenterBox.backgroundColor = .clear
        addNextToNavBar()
        self.photoButton.setTitle("camera.photo".localized, for: .normal)
        self.libraryButton.setTitle("camera.library".localized, for: .normal)
        self.photoButton.setTitleColor(.white, for: .normal)
        self.libraryButton.setTitleColor(.gfGrayText, for: .normal)
        doGestureSet(photoPreviewImageView)
        previewView.enableZoom()
        registerCells()
        collectionView.delegate = self
        collectionView.dataSource = self
        if let layout = collectionView.collectionViewLayout as? UICollectionViewFlowLayout {
            layout.scrollDirection = .horizontal
        }
        
        FlexManager.newFlex()
        FlexManager.flex?.isReflex = CameraViewModel.isReflex
        FlexManager.flex?.parentFlexId = CameraViewModel.parentFlexId
        trashView.isHidden = true
        trashView.layer.cornerRadius = trashView.frame.size.width/2
        trashView.clipsToBounds = true
        trashCan.addTarget(self, action: #selector(didTapTrashCan), for: .touchUpInside)
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(keyboardWillShow(_:)),
                                               name: UIResponder.keyboardWillShowNotification,
                                               object: nil)
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(keyboardWillHide(_:)),
                                               name: UIResponder.keyboardWillHideNotification,
                                               object: nil)
        self.verticalSlider.addTarget(self, action: #selector(sliderDidChange(_:)), for: .valueChanged)
        self.verticalSlider.addTarget(self, action: #selector(sliderDidEndSliding), for: [.touchUpInside, .touchUpOutside])
        self.verticalSlider.isHidden = true
        self.collectionViewBottomConstraint.constant = -95.0
        stickerBaseView.isUserInteractionEnabled = false
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.hideableComponents.forEach({ $0.isHidden = false })
        if !flexBackgroundView.subviews.contains(photoPreviewImageView) {
            flexBackgroundView.addSubview(photoPreviewImageView)
            flexBackgroundView.clipsToBounds = true
        }
        adjustCollectionView(true)
        title = "Flex"
        tabBarController?.tabBar.isHidden = true
        guard User.isLoggedIn else {
            OnboardingStateMachine.didComplete(this: .cameraWillAppear)
            return
        }
    }
      
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        setupTheCamera()
        self.navigationController?.interactivePopGestureRecognizer?.isEnabled = false
        Utilities.hideSpinner()
    }
        
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        if photoPreviewImageView != nil {
            photoPreviewImageView.image = nil
        }
        originalPhotoPreview = nil
        let isRestart = CameraViewModel.state == .restart ? true : false
        CameraViewModel.clearTheFlex(isRestart: isRestart)
        if !UIDevice.isSimulator, self.captureSession != nil {
            self.captureSession.stopRunning()
        }
        trashView.isHidden = true
        verticalSlider.isHidden = true
        collectionViewHeight.constant = 0.0
        if self.isMovingFromParent, CameraViewModel.state != .restart {
            CameraViewModel.state = .buttons
        }
        if drawView != nil {
            drawView?.resetDrawing()
            drawView?.removeFromSuperview()
        }
        tabBarController?.tabBar.isHidden = false
    }
    
    func setupTheCamera() {
        if !UIDevice.isSimulator {
            checkAuthorizationStatus(.camera) {success,_ in
                if success {
                    DispatchQueue.main.async {
                        self.captureSession = AVCaptureSession()
                        self.captureSession.sessionPreset = .high
                        self.discoveredCameras[CameraFaceOption.back] = AVCaptureDevice.DiscoverySession(deviceTypes: [AVCaptureDevice.DeviceType.builtInWideAngleCamera], mediaType: .video, position: .back).devices[0]
                        self.discoveredCameras[CameraFaceOption.front] = AVCaptureDevice.DiscoverySession(deviceTypes: [AVCaptureDevice.DeviceType.builtInWideAngleCamera], mediaType: .video, position: .front).devices[0]
                        
                        self.stillImageOutput = AVCapturePhotoOutput()
                        if self.discoveredCameras[.back] != nil {
                            if let input = try? AVCaptureDeviceInput(device: self.discoveredCameras[.back]!) {
                                if self.captureSession.canAddInput(input) && self.captureSession.canAddOutput(self.stillImageOutput) {
                                    self.currentCamera = .back
                                    self.currentInput = input
                                    self.captureSession.addInput(input)
                                    self.captureSession.addOutput(self.stillImageOutput)
                                    self.setupLivePreview()
                                }
                            }
                        } else {
                            // alert about permissions being in settings
                            self.showGoToSettingsAlert([.camera])
                        }
                    }
                } else {
                    DispatchQueue.main.async {
                        self.showGoToSettingsAlert([.camera])
                    }
                }
            }
        }
        if UIDevice.isSimulator {
            if let but = libraryButton {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    self.didTapButton(but)
                }
            }
        }
    }
    
    // MARK: - CollectionView Helpers
    
    func adjustCollectionView(_ animate: Bool = true) {
        // removes the flare state's tap background to move collectionView down
        if flexBackgroundView?.gestureRecognizers?.count ?? 0 > 0, !CameraViewModel.flareTypes.contains(CameraViewModel.state) {
            flexBackgroundView.gestureRecognizers?.removeAll()
        }
        
        // place the vertical slider if necessary
        let showsVSlider: [CameraCellType] = [.fonts, .colors]
        if showsVSlider.contains(CameraViewModel.state), keyboardHeight == 0 {
            if verticalSlider.isHidden {
                let frEnd = verticalSlider.frame
                let frStart = CGRect(x: -frEnd.origin.x, y: frEnd.origin.y, width: frEnd.size.width, height: frEnd.size.height)
                verticalSlider.isHidden = false
                verticalSlider.frame = frStart
                UIView.animate(withDuration: 0.5) {
                    self.verticalSlider.frame = frEnd
                    if CameraViewModel.state == .draw {
                        self.lineWidthView.isHidden = false
                    }
                }
            }
        } else {
            verticalSlider.isHidden = true
            lineWidthView.isHidden = true
        }
        
        if CameraViewModel.state != .fonts {
            UIView.animate(withDuration: 0.5) {
                self.collectionViewHeight.constant = CameraViewModel.heightOfCollectionView[CameraViewModel.state] ?? 222.0
                self.view.layoutIfNeeded()
                if CameraViewModel.flareTypes.contains(CameraViewModel.state) {
                    CameraViewModel.canCreateStickerStickerView = true
                }
                
            }
        }
        collectionView.reloadData()
        if CameraViewModel.state == .text {
            // puts textSticker on top of any aspect ratio pic or the center
            var yStart = photoPreviewImageView.contentClippingRect.origin.y - 50
            if yStart < 50 {
                yStart = view.center.y - 100.0
            }
            createTextStickerView(point: CGPoint(x: view.frame.size.width/2, y: yStart))
        }
        if CameraViewModel.state == .edit {
            let ip = IndexPath(row: 0, section: 0)
            UIView.animate(withDuration: 0.5) {
                self.collectionView.scrollToItem(at: self.editServiceIndexPath ?? ip, at: .left, animated: true)
            }
        }
        
        if CameraViewModel.state == .flare || CameraViewModel.flareTypes.contains(CameraViewModel.state) {
            if let layout = collectionView.collectionViewLayout as? UICollectionViewFlowLayout {
                layout.invalidateLayout()
                layout.scrollDirection = .vertical
                layout.sectionInset = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
                layout.minimumInteritemSpacing = 4
                layout.minimumLineSpacing = 4
                layout.sectionHeadersPinToVisibleBounds = true
            }
            if collectionView.backgroundView?.tag != 9922 {
                let blurEffect = UIBlurEffect(style: UIBlurEffect.Style.regular)
                let blurEffectView = UIVisualEffectView(effect: blurEffect)
                blurEffectView.frame = collectionView.bounds
                blurEffectView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
                blurEffectView.tag = 9922
                collectionView.backgroundView = blurEffectView
            }
            if collectionView.gestureRecognizers?.filter({ $0.isKind(of: UISwipeGestureRecognizer.self)}).count == 0 {
                let swipe = UISwipeGestureRecognizer(target: self, action: #selector(didSwipeFlare(_:)))
                swipe.direction = .left
                collectionView.addGestureRecognizer(swipe)
                let swiped = UISwipeGestureRecognizer(target: self, action: #selector(didSwipeFlare(_:)))
                swiped.direction = .right
                collectionView.addGestureRecognizer(swiped)
            }
            collectionView.reloadData()
            if animate  {
                animateTheCollectionView()
            }
            
        } else {
            trashView.isHidden = true
            disableTextStickerButtonsInNavBar()
            if CameraViewModel.textStates.contains(CameraViewModel.state) {
                addTextStickerButtonsToNavBar()
            }
            if animate {
                animateTheCollectionView()
            }
            if let layout = collectionView.collectionViewLayout as? UICollectionViewFlowLayout {
                layout.invalidateLayout()
                layout.scrollDirection = .horizontal
                layout.sectionInset = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
                layout.minimumInteritemSpacing = 0
                layout.minimumLineSpacing = 0
            }
            collectionView.backgroundView = nil
        }
        if CameraViewModel.stickersInProgress.count > 0 {
            trashView.isHidden = false
        }
        if !CameraViewModel.textStates.contains(CameraViewModel.state), navigationItem.rightBarButtonItems?.count ?? 3 > 1 {
            if CameraViewModel.state == .filters {
                addEditToNavBar()
            } else if CameraViewModel.state == .edit {
                addActiveEditToNavBar()
            } else if CameraViewModel.filterStates.contains(CameraViewModel.state), CameraViewModel.state != .filters {
                addActiveEditToNavBar()
            } else {
                addNextToNavBar()
            }
            title = "Flex"
            self.verticalSlider.isHidden = true
        }
        if CameraViewModel.state == .draw {
            if CameraViewModel.drawings.count > 0 {
                if CameraViewModel.isEraseEnabled {
                    addDrawButtonsToNavBar(true)
                } else {
                    addDrawButtonsToNavBar()
                }
            } else {
                if CameraViewModel.isEraseEnabled {
                    addDrawButtonsToNavBar(true)
                } else {
                    addDrawButtonToNavBar()
                }
            }
            verticalSlider.isHidden = false
            lineWidthView.isHidden = false
        }
        buttonsUpdate()
    }
    
    func animateTheCollectionView() {
        let isKeyboardUp = keyboardHeight > 0
        var frEnd = collectionView.frame
        let frStart = CGRect(x: 0.0, y: view.frame.size.height, width: frEnd.width, height: frEnd.height)
        if !isKeyboardUp {
            collectionView.frame = frStart
            self.view.layoutIfNeeded()
            self.collectionViewBottomConstraint.constant = -95.0
        } else {
            frEnd = CGRect(x: 0.0, y: view.frame.size.height - keyboardHeight, width: frEnd.width, height: frEnd.height)
        }
        UIView.animate(withDuration: 0.5, animations: {
            if !isKeyboardUp {
                self.collectionView.frame = frEnd
            } else {
                self.view.layoutIfNeeded()
                self.collectionViewBottomConstraint.constant = -self.keyboardHeight
            }
        }) { (ending) in
            self.moveToColorOrFont()
        }
    }
    
    func buttonsUpdate() {
        guard CameraViewModel.state != .buttons else {
            let _ = imageEnhancementControlsView
            imageEnhancementControlsView.setupView()
            imageEnhancementControlsView.isHidden = true
            imageEnhancementControlsView.delegate = self
            photoButton.isHidden = false
            libraryButton.isHidden = false
            return
        }
        imageEnhancementControlsView.setupView()
        photoButton.isHidden = true
        libraryButton.isHidden = true
        imageEnhancementControlsView.isHidden = false
    }
    
    // used in filters and flare states on tap in background
    func moveTheTray(up: Bool = true, fromCreateSticker: Bool) {
        guard CameraViewModel.state != .filters else {
            var const: CGFloat = 0.0
            if up {
                const = CameraViewModel.heightOfCollectionView[CameraViewModel.state] ?? 140.0
            }
            UIView.animate(withDuration: 0.5) {
                self.collectionViewHeight.constant = const
                self.view.layoutIfNeeded()
            }
            return
        }
        addFlipAndNextToNavBar()
        var const: CGFloat = 44.0
        if up {
            const = 222.0
        }
        UIView.animate(withDuration: 0.05) {
            if fromCreateSticker {
                CameraViewModel.activeSticker?.alpha = 1.0
                self.trashView.isHidden = false
                self.view.bringSubviewToFront(self.trashView)
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
                UIView.animate(withDuration: 0.5) {
                    self.collectionViewHeight.constant = const
                    self.view.layoutIfNeeded()
                }
            }
        }
    }
    
    func moveToColorOrFont() {
        if let lastFont = GFDefaults.lastFont, CameraViewModel.state == .fonts || CameraViewModel.state == .text {
            let ip = IndexPath(row: FontManager.flatFontsArray.firstIndex(of: lastFont.fontName) ?? 0, section: 0)
            self.collectionView.scrollToItem(at: ip, at: .left, animated: true)
            CameraViewModel.configureFontCell(ip: ip, cv: self.collectionView, isSelected: true)
        } else if CameraViewModel.state == .colors {
            let ip = CameraViewModel.selectedColorIndexPath
            self.collectionView.scrollToItem(at: ip, at: .left, animated: true)
            CameraViewModel.configureColorCell(ip: ip, cv: self.collectionView, isSelected: true)
        }
    }
    
    private func endTextEntry() {
        view.endEditing(true)
        CameraViewModel.state = .fonts
        adjustCollectionView()
//        guard !CameraViewModel.textStickerBeingEdited else {
//            dismissTheTextInputView()
//            CameraViewModel.state = .fonts
//            adjustCollectionView()
//            return
//        }
//
//        if textField.text?.count ?? 0 > 0, CameraViewModel.canCreateTextStickerView {
//        }
//        textField.resignFirstResponder()
//        dismissTheTextInputView()
//        CameraViewModel.state = .fonts
//        adjustCollectionView()
    }
    
    // MARK: - Keyboard Delegates
    
    @objc func keyboardWillShow(_ notification: NSNotification) {
        let userInfo = notification.userInfo!
        let keyboardSize = (userInfo[UIResponder.keyboardFrameEndUserInfoKey] as! NSValue).cgRectValue.size
        keyboardHeight = keyboardSize.height + 5
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.moveToColorOrFont()
        }
    }

    @objc func keyboardWillHide(_ notification: NSNotification) {
        keyboardHeight = 0.0
    }

    
    // MARK: - Camera operations

    func setupLivePreview() {
        videoPreviewLayer = AVCaptureVideoPreviewLayer(session: captureSession)
        videoPreviewLayer.videoGravity = .resizeAspect
        videoPreviewLayer.connection?.videoOrientation = .portrait
        if previewView.layer.sublayers?.count ?? 0 > 0 {
            previewView.layer.sublayers = []
        }
        previewView.layer.addSublayer(videoPreviewLayer)
        startTheCamera()
    }
    
    func startTheCamera() {
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            self?.captureSession.startRunning()
        }
        DispatchQueue.main.async {
            self.videoPreviewLayer.frame = self.previewView.bounds
            self.hideableComponents.forEach({ $0.isHidden = false })
        }
    }
    
    func stopTheCamera() {
        DispatchQueue.main.async {
            self.photoButton.setTitle("camera.photo".localized, for: .normal)
            self.hideableComponents.forEach({ $0.isHidden = true })
        }
    }
    
    
    @IBAction func didTapButton(_ sender: Any) {
        switch sender as? UIButton {
        case photoButton:
            photoButton.setTitleColor(.white, for: .normal)
            photoButton.titleLabel?.font = .boldSystemFont(ofSize: 15)
            libraryButton.titleLabel?.font = .systemFont(ofSize: 15)
            libraryButton.setTitleColor(.gfGrayText, for: .normal)
            
        case libraryButton:
            checkAuthorizationStatus(.library) {success,_ in
                if success {
                    DispatchQueue.main.async {
                        self.libraryButton.titleLabel?.font = .boldSystemFont(ofSize: 15)
                        self.photoButton.titleLabel?.font = .systemFont(ofSize: 15)
                        self.photoButton.setTitleColor(.gfGrayText, for: .normal)
                        self.libraryButton.setTitleColor(.white, for: .normal)
                        self.getImage(fromSourceType: .photoLibrary)
                    }
                } else {
                    DispatchQueue.main.async {
                        self.showGoToSettingsAlert([.library])
                    }
                }
            }
        default: break
        }
    }
    
    func photoOutput(_ output: AVCapturePhotoOutput, didFinishProcessingPhoto photo: AVCapturePhoto, error: Error?) {
        
        guard let imageData = photo.fileDataRepresentation()
            else { return }
        
        let image = UIImage(data: imageData)
        
        photoPreviewImageView.image = image
        photoPreviewImageView.transform = CGAffineTransform.init(a: transformAd, b: 0.0, c: 0.0, d: transformAd, tx: 0.0, ty: 0.0)
        doGestureSet(photoPreviewImageView)
        transformAd = 1.0
        stopTheCamera()
        doGestureSet(photoPreviewImageView)
        adjustCollectionView(false)
    }
    
    // MARK: - Gesture Recognizers
    // consolidates gesture assignment here
    func doGestureSet(_ object: UIView) {
        object.gestureRecognizers?.removeAll()
        object.enableZoom()
        let rotate = UIRotationGestureRecognizer(target: self, action: #selector(handleRotate(_:)))
        object.addGestureRecognizer(rotate)
        if object.isKind(of: StickerView.self) {
            let panRecognizer = UIPanGestureRecognizer(target:self, action:#selector(detectStickerPan(_:)))
            object.addGestureRecognizer(panRecognizer)
            if (object as? StickerView)?.viewType == .some(.text) {
                let tap = UITapGestureRecognizer(target: self, action: #selector(detectTextStickerTap(_:)))
                object.addGestureRecognizer(tap)
                let twice = UITapGestureRecognizer(target: self, action: #selector(detectTextStickerTappedTwice(_:)))
                twice.numberOfTapsRequired = 2
                object.addGestureRecognizer(twice)
            }
        } else {
            let panRecognizer = UIPanGestureRecognizer(target:self, action:#selector(detectPan(_:)))
            object.addGestureRecognizer(panRecognizer)
        }
        object.gestureRecognizers?.forEach({ $0.delegate = self })
    }
    
    // exclusive to flare state
    @objc func detectBackgroundTap(_ recognizer: UITapGestureRecognizer) {
        guard CameraViewModel.state != .filters else {
            moveTheTray(up: !(self.collectionView.frame.height == CameraViewModel.heightOfCollectionView[.filters]), fromCreateSticker: false)
            return
        }
        moveTheTray(up: false, fromCreateSticker: false)
    }
    
    @objc func detectPan(_ recognizer:UIPanGestureRecognizer) {
        let translation  = recognizer.translation(in: view)
        photoPreviewImageView.center = CGPoint(x: lastLocation.x + translation.x + previewCenterOffset.x,
                                               y: lastLocation.y + translation.y + previewCenterOffset.y)
    }
    
    @objc func detectStickerPan(_ recognizer: UIPanGestureRecognizer) {
        if let sticker = CameraViewModel.activeSticker {
            if recognizer.state == .began {
                lastActiveStickerLocation = sticker.center
            }
            let translation  = recognizer.translation(in: view)
            CameraViewModel.activeSticker!.center = CGPoint(x: lastActiveStickerLocation.x + translation.x, y: lastActiveStickerLocation.y + translation.y)
        }
    }
    
    @objc func detectTextStickerTap(_ recognizer: UITapGestureRecognizer) {
        let point = recognizer.location(in: view)
        handleStickerTouch(point)
    }
    
    @objc func detectTextStickerTappedTwice(_ recognizer: UITapGestureRecognizer) {
        if let textSticker = CameraViewModel.activeSticker as? TextStickerView, textSticker.viewType == .text {
            CameraViewModel.textStickerBeingEdited = true
            textSticker.textStickerView.isEditable = true
            textSticker.textStickerView.isSelectable = true
            textSticker.textStickerView.becomeFirstResponder()
            let tap = UITapGestureRecognizer(target: self, action: #selector(dismissKeyboard))
            flexBackgroundView.addGestureRecognizer(tap)
            adjustCollectionView(true)
        }
    }
    
    @objc private func handleRotate(_ sender: UIRotationGestureRecognizer) {
        if let view = sender.view {
            view.transform = view.transform.rotated(by: sender.rotation)
            sender.rotation = 0
        }
    }
    
    private func handleStickerTouch(_ point: CGPoint) {
        // handles sticker touch
        if CameraViewModel.stickersInProgress.count > 0 {
            let arr = CameraViewModel.stickersInProgress.filter({ $0.frame.contains(point)})
            if arr.count > 0 {
                CameraViewModel.activeSticker = arr.last
                if let sticker = CameraViewModel.activeSticker, (sticker.gestureRecognizers?.count ?? 0) < 3 {
                    doGestureSet(sticker)
                }
                lastActiveStickerLocation = (CameraViewModel.activeSticker?.center)!
                if CameraViewModel.activeSticker != nil {
                    flexBackgroundView.bringSubviewToFront(CameraViewModel.activeSticker!)
                    if keyboardHeight > 0.0 { // keyboard is set to 0.0 on dismissal
                        dismissKeyboard()
                    }
                }
            }
        }
    }
    
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        if let point = touches.first?.location(in: view) {
            handleStickerTouch(point)
            if let pointed = touches.first?.location(in: photoPreviewImageView) {
                let offsetX = pointed.x - photoPreviewImageView.center.x
                let offsetY = pointed.y - photoPreviewImageView.center.y
                previewCenterOffset = CGPoint(x: -offsetX, y: -offsetY)
                lastLocation = pointed
            }
        }
    }
    
    @objc func didSwipeFlare(_ sender: UISwipeGestureRecognizer) {
        guard sender.direction != .up, sender.direction != .down else { return }
        if let currentIndex = CameraViewModel.flareCarousel.lastIndex(of: CameraViewModel.state) {
            if sender.direction == .right {
                if currentIndex == 0 {
                    CameraViewModel.state = CameraViewModel.flareCarousel[CameraViewModel.flareCarousel.count - 1]
                    adjustCollectionView(false)
                    return
                }
                let nextIndex = currentIndex - 1
                CameraViewModel.state = CameraViewModel.flareCarousel[nextIndex]
                adjustCollectionView(false)
            } else if sender.direction == .left {
                
                if currentIndex == CameraViewModel.flareCarousel.count - 1 {
                    CameraViewModel.state = .flare
                    adjustCollectionView(false)
                    return
                }
                let nextIndex = currentIndex + 1
                CameraViewModel.state = CameraViewModel.flareCarousel[nextIndex]
                adjustCollectionView(false)
            }
        }
    }
    
    @objc override func dismissKeyboard() {
        if let textSticker = CameraViewModel.activeSticker as? TextStickerView {
            textSticker.textStickerView.isEditable = false
            textSticker.textStickerView.isSelectable = false
        }
        view.endEditing(true)
        self.adjustCollectionView()
        addTextStickerButtonsToNavBar(true)
    }
            
    // MARK: - Creating stickers
    
    func createTextStickerView(point: CGPoint) {
        if !(photoPreviewImageView.gestureRecognizers?.isEmpty ?? true) {
            photoPreviewImageView.gestureRecognizers?.removeAll()
        }
        
        // create the stickerView here
        let size = CGSize(width: view.frame.size.width * 2/3, height: 100.0)
        let textSticker = TextStickerView(frame: CGRect(x: 0.0, y: 0.0, width: size.width, height: size.height))
        textSticker.textStickerView.isScrollEnabled = false
        CameraViewModel.textStickerStartingHeight = size.height
        textSticker.center = point
        lastActiveStickerLocation = point
        textSticker.textStickerView.textAlignment = .center
        textSticker.textStickerView.tintColor = .gfGreen
        textSticker.setupView(.text)
        if GFDefaults.lastFont != nil {
            textSticker.textStickerView.font = GFDefaults.lastFont
        }
        textSticker.textStickerView.layer.shadowOffset = CGSize(width: 1.0, height: 1.0)
        textSticker.textStickerView.layer.shadowColor = UIColor.black.cgColor
        textSticker.textStickerView.layer.shadowRadius = 1.0
        textSticker.textStickerView.layer.shadowOpacity = 1.0
        textSticker.isMultipleTouchEnabled = true
        doGestureSet(textSticker)
        stickerBaseView.addSubview(textSticker)
        stickerBaseView.isUserInteractionEnabled = true
        CameraViewModel.state = .fonts
        trashView.isHidden = false
        adjustCollectionView()
        CameraViewModel.canCreateTextStickerView = false
        CameraViewModel.stickersInProgress.append(textSticker)
        CameraViewModel.activeSticker = textSticker
        verticalSlider.value = 0.5
        addTextStickerButtonsToNavBar()
        textSticker.textStickerView.becomeFirstResponder()
        textSticker.textStickerView.delegate = self
        let tap = UITapGestureRecognizer(target: self, action: #selector(dismissKeyboard))
        flexBackgroundView.addGestureRecognizer(tap)
    }
        
    func createStickerView(indexPath: IndexPath) {
        flexBackgroundView.gestureRecognizers?.removeAll()
        let tap = UITapGestureRecognizer(target: self, action: #selector(detectBackgroundTap))
        flexBackgroundView.addGestureRecognizer(tap)
        verticalSlider.isHidden = true
        if !(photoPreviewImageView.gestureRecognizers?.isEmpty ?? true) {
            photoPreviewImageView.gestureRecognizers?.removeAll()
        }
        // create the stickerView here
        let sticker = StickerView(frame: CGRect(x: 0.0, y: 0.0, width: 150.0, height: 150.0))
        let position = CGPoint(x: view.frame.size.width/2, y: view.frame.size.height/2)
        sticker.alpha = 0.0
        sticker.center = position
        lastActiveStickerLocation = position
        sticker.setupView(.sticker)
        if CameraViewModel.state == .favs {
            var row = indexPath.row - 1
            if row < GFDefaults.getStickersArray(.recently).count { // recently used
                let source = GFDefaults.getStickersArray(.recently)
                if !source.isEmpty {
                    if CameraViewModel.stickersSource[.emojis]?.contains(source[row]) ?? false {
                        sticker.imageView?.image = source[row].image()
                    } else {
                        sticker.imageView?.image = UIImage(named: source[row])
                    }
                    GFDefaults.addStickerToFavs(name: source[row])
                    sticker.name = source[row]
                }
            } else { // most frequently used
                let source = GFDefaults.getStickersArray(.frequently)
                let before = GFDefaults.getStickersArray(.recently).count > 5 ? 12 : 7
                row = indexPath.row - before
                if row >= 0 {
                    sticker.imageView?.image = UIImage(named: source[row])
                    GFDefaults.addStickerToFavs(name: source[row])
                    sticker.name = source[row]
                }
            }
        } else if let pic = CameraViewModel.stickersSource[CameraViewModel.state], indexPath.row < pic.count {
            if CameraViewModel.stickersSource[.emojis]?.contains(pic[indexPath.row]) ?? false {
                sticker.imageView?.image = pic[indexPath.row].image()
            } else {
                sticker.imageView?.image = UIImage(named: pic[indexPath.row])
            }
            GFDefaults.addStickerToFavs(name: pic[indexPath.row])
            sticker.name = pic[indexPath.row]
        }
        FlexManager.flex?.hashtags?.append(sticker.name ?? "unknown")

        sticker.isMultipleTouchEnabled = true
        doGestureSet(sticker)
        stickerBaseView.addSubview(sticker)
        stickerBaseView.isUserInteractionEnabled = true
        CameraViewModel.canCreateStickerStickerView = false
        CameraViewModel.stickersInProgress.append(sticker)
        CameraViewModel.activeSticker = sticker
        moveTheTray(up: false, fromCreateSticker: true)
    }
    
    // deletes sticker/textSticker makes last added sticker active
    @objc func didTapTrashCan() {
        var some = FlexManager.flex?.hashtags?.filter({ $0 == CameraViewModel.activeSticker?.name ?? "unknown" })
        some?.removeFirst()
        if CameraViewModel.activeSticker != nil {
            CameraViewModel.activeSticker?.removeFromSuperview()
            CameraViewModel.stickersInProgress.removeAll(where: {$0 == CameraViewModel.activeSticker})
            CameraViewModel.activeSticker = nil
        }
        if CameraViewModel.stickersInProgress.count > 0 {
            CameraViewModel.activeSticker = CameraViewModel.stickersInProgress.last
        }
        if CameraViewModel.activeSticker == nil {
            doGestureSet(photoPreviewImageView)
            stickerBaseView.isUserInteractionEnabled = false
            trashView.isHidden = true
        }
    }
    
    // MARK: - NavBar EXTRA buttons
    
    override func advanceTextAlignment() {
        if CameraViewModel.state == .text {
            endTextEntry()
        }
        let currentAlignment =  (CameraViewModel.activeSticker as? TextStickerView)?.textStickerView?.textAlignment
        if currentAlignment == .left {
            (CameraViewModel.activeSticker as? TextStickerView)?.textStickerView?.textAlignment = .center
        } else if currentAlignment == .center {
            (CameraViewModel.activeSticker as? TextStickerView)?.textStickerView?.textAlignment = .right
        } else if currentAlignment == .right {
            (CameraViewModel.activeSticker as? TextStickerView)?.textStickerView?.textAlignment = .left
        }
    }
    
    override func flip() {
        if let sticker = CameraViewModel.activeSticker, sticker.viewType != .text {
            let image = sticker.imageView?.image
            sticker.imageView.image = image?.withHorizontallyFlippedOrientation()
        }
    }
    
    override func tapForEdit() {
        didTapForCameraAction(.edit)
        imageEnhancementControlsView.changeOtherButtons(tapped: UIButton())
    }
    
    override func tapForErase() {
        if CameraViewModel.isEraseEnabled {
            CameraViewModel.isEraseEnabled = false
            addDrawButtonsToNavBar(false)
            // change the color back to selected color
            drawView?.strokeColor = CameraViewModel.colors[CameraViewModel.selectedColorIndexPath.row].cgColor
            CameraViewModel.isEraseEnabled = false
        } else {
            CameraViewModel.isEraseEnabled = true
            addDrawButtonsToNavBar(true)
            // change the color to clear
            drawView?.strokeColor = UIColor.clear.cgColor
            CameraViewModel.isEraseEnabled = true
        }
    }
    
    override func tapForFonts() {
        if CameraViewModel.state == .text {
            endTextEntry()
            return
        }
        CameraViewModel.state = .fonts
        adjustCollectionView()
    }
    
    override func tapForColorSwitching() {
        // cycle between text color and background color
        let currentColor = CameraViewModel.colors[CameraViewModel.selectedColorIndexPath.row]
        let previousColor = CameraViewModel.colors[CameraViewModel.previousSelectedColorIndexPath.row]
        let tsv = CameraViewModel.activeSticker as? TextStickerView
        tsv?.layer.cornerRadius = 4
        
        if CameraViewModel.colorText {
            if tsv?.backgroundColor != .clear, tsv?.backgroundColor != currentColor {
                tsv?.textStickerView?.textColor = currentColor
            } else {
                tsv?.textStickerView?.textColor = currentColor
                tsv?.backgroundColor = previousColor
            }
        } else {
            tsv?.textStickerView?.textColor = currentColor
        }
        CameraViewModel.colorText = !CameraViewModel.colorText
        if tsv?.textStickerView?.textColor == CameraViewModel.activeSticker?.backgroundColor {
            CameraViewModel.activeSticker?.backgroundColor = .clear
        }
    }
    
    override func tapForOpacity() { // no 0.0 opacitySetting please, icon shows next tap
        
        if !CameraViewModel.opacityUp, CameraViewModel.opacitySetting > 0.25 {
            CameraViewModel.opacitySetting -= 0.25
        } else if !CameraViewModel.opacityUp, CameraViewModel.opacitySetting == 0.25 {
            CameraViewModel.opacitySetting += 0.25
            CameraViewModel.opacityUp = true
        } else if CameraViewModel.opacityUp, CameraViewModel.opacitySetting == 1.0 {
            CameraViewModel.opacitySetting -= 0.25
            CameraViewModel.opacityUp = false
        } else if CameraViewModel.opacityUp, CameraViewModel.opacitySetting < 1.0 {
            CameraViewModel.opacitySetting += 0.25
        }
        if CameraViewModel.state == .draw, let draw = drawView {
            draw.opacity = CameraViewModel.opacitySetting
        }
        
        let navBut = navigationItem.rightBarButtonItems?.filter({$0.image != nil })[0]
        switch CameraViewModel.opacitySetting {
        case 0.25: navBut?.image = #imageLiteral(resourceName: "opacity25")
        case 0.50: navBut?.image = #imageLiteral(resourceName: "opacity50")
        case 0.75: navBut?.image = #imageLiteral(resourceName: "opacity75")
        default: navBut?.image = #imageLiteral(resourceName: "opacity100")
        }
    }
    
    override func tapForPalette() {
        if CameraViewModel.state == .text {
            endTextEntry()
            return
        }

        if toggleForeVersusBackgroundColors {
            toggleForeVersusBackgroundColors = !toggleForeVersusBackgroundColors
            CameraViewModel.state = .colors
            adjustCollectionView()
        } else {
            tapForColorSwitching()
            toggleForeVersusBackgroundColors = !toggleForeVersusBackgroundColors

        }
    }

    override func undoDraw() {
        if CameraViewModel.state == .draw, !CameraViewModel.drawings.isEmpty, let draw = drawView {
            draw.removeLastDrawing()
        }
        if CameraViewModel.drawings.isEmpty {
            addDrawButtonToNavBar()
            CameraViewModel.isEraseEnabled = false
        }
    }
    
    // Makes the flex
    override func didTapNext() {
        view.endEditing(true)
        let viewer = UIView(frame: self.flexBackgroundView.bounds)
        viewer.tag = CameraViewModel.viewerTag
        self.view.addSubview(viewer)
        viewer.clipsToBounds = true
        viewer.addSubview(self.flexBackgroundView)
        viewer.addSubview(self.stickerBaseView)
        if let image = viewer.capture() {
            GFSpinnerView.showIn(view: self.view)
            GFNetworkServices.sendImageToRekognition(image.jpegData(compressionQuality: 0.2)!) { (success, error) in
                guard error == nil else {
                    DispatchQueue.main.async {
                        Utilities.hideSpinner()
                        var message = ""
                        switch error {
                        case is GFError:
                            guard let gfError = error as? GFError else { return }
                            if let desc = gfError.errorDescription, let sugg = gfError.recoverySuggestion {
                                message = "\(desc)\n\n\(sugg)"
                            }
                        default:
                            if let desc = error?.localizedDescription {
                                message = "\(desc)"
                            }
                        }
                        let alert = UIAlertController(title: "Error", message: message, preferredStyle: .alert)
                        let ok = UIAlertAction(title: "OK", style: .default, handler: nil)
                        alert.addAction(ok)
                        self.present(alert, animated: true, completion: nil)
                    }
                    return
                }
                
                if let flex = FlexManager.flex, FlexManager.passesSafety(flex) {
                    // Flexes it here
                    DispatchQueue.main.async {
                        let fvc = FinalizeViewController.storyboardInstance()
                        fvc.flexImage = image
                        self.navigationController?.pushViewController(fvc, animated: true)
                    }
                } else {
                    // failed safety check
                    let reasons = FlexManager.safetyReasonString()
                    DispatchQueue.main.async {
                        let alert = UIAlertController(title: "Unsafe Content?", message: "This flex fails to meet GameFlex standards due to these issues: \(reasons). \n\nMake changes and try again.", preferredStyle: .alert)
                        let ok = UIAlertAction(title: "OK", style: .default) { (continue) in
                            // reassemble the last stage before next
                            self.view.addSubview(self.flexBackgroundView)
                            self.view.sendSubviewToBack(self.flexBackgroundView)
                            viewer.removeFromSuperview()
                        }
                        alert.addAction(ok)
                        DispatchQueue.main.async {
                            Utilities.hideSpinner()
                            self.present(alert, animated: true, completion: nil)
                        }
                    }
                }
            }
        }
    }
        
    func passToServer(_ image: UIImage) {// proves photo worked
        GFNetworkServices.uploadMediaToUserProfile(image: image) { (success, error) in
            DispatchQueue.main.async {
                Utilities.hideSpinner()
                if success {
                    self.successAlert()
                } else {
                    self.successAlert(error)
                }
            }
        }

    }

    private func successAlert(_ error: Error? = nil) {
        let alert = UIAlertController(title: "Success", message: "Flexed. Make a new Flex?", preferredStyle: .alert)
        if error == nil {
            let ok = UIAlertAction(title: "OK", style: .default) { (success) in
                self.updateFlexCount()
                self.view.addSubview(self.flexBackgroundView)
                self.view.sendSubviewToBack(self.flexBackgroundView)
                CameraViewModel.state = .restart
                self.view.viewWithTag(CameraViewModel.viewerTag)?.removeFromSuperview()
                CameraViewModel.clearTheFlex(isRestart: true)
                self.navigationController?.popViewController(animated: true)
            }
            let main = UIAlertAction(title: "Main Channel", style: .default) { (success) in
                self.updateFlexCount()
                self.navigationController?.popViewController(animated: true)
            }
            alert.addAction(ok)
            alert.addAction(main)
        } else {
            alert.title = "Error"
            alert.message = "\((error as? GFError)?.errorCode ?? 499) - \((error as? GFError)?.errorDescription ?? "unknown")"
            let ok = UIAlertAction(title: "OK", style: .default)
            alert.addAction(ok)
        }
        DispatchQueue.main.async {
            self.present(alert, animated: true)
        }
    }    
}

// MARK: -
// MARK: - CameraDelegate service
extension CameraViewController: CameraDelegate {
    func didTapForCameraAction(_ action: CameraActionType) {
        guard action != .draw else {
            CameraViewModel.activeSticker?.gestureRecognizers?.removeAll()
            wasDrawState = true
            CameraViewModel.state = .draw
            drawView = DrawView(frame: flexBackgroundView.frame)
            drawView?.delegate = self
            if drawView != nil {
                flexBackgroundView.addSubview(drawView!)
                drawView?.strokeColor = CameraViewModel.colors[CameraViewModel.selectedColorIndexPath.row].cgColor
                lineWidthView.backgroundColor = CameraViewModel.colors[CameraViewModel.selectedColorIndexPath.row]
                drawView?.lineWidth = 8.0
                drawView?.expectedLineWidth = 8.0
                verticalSlider.value = 0.3
                lineWidthView.frame.size.width = 8.0
                lineWidthView.layer.cornerRadius = lineWidthView.frame.height/2
                lineWidthView.center = lineWidthCenterBox.center
            }
            adjustCollectionView()
            return
        }
        if wasDrawState == true {
            drawView?.removeFromSuperview()
            wasDrawState = false
            addNextToNavBar()
            verticalSlider.isHidden = true
            lineWidthView.isHidden = true
            adjustCollectionView()
        }
        
        // make filter or edit stick
        if CameraViewModel.state == .filters, action != .filter, originalPhotoPreview != nil {
            originalPhotoPreview = photoPreviewImageView?.image
        }
        if CameraViewModel.editCellArray.contains(CameraViewModel.state), action == .edit, originalPhotoPreview != nil {
            originalPhotoPreview = photoPreviewImageView?.image
        }
        
        var animate = !(CameraViewModel.state.rawValue == action.rawValue)
        
        switch action {
        case .none:
            CameraViewModel.state = .none
            doGestureSet(photoPreviewImageView)
            adjustCollectionView(animate)
        case .favs:
            CameraViewModel.state = .favs
            adjustCollectionView(false)
        case .flexFun, .emojis, .smellies, .memes, .bubbles, .arrows, .smack:
            let cellType = CameraViewModel.flareDictionary.first {( $0.value == action)}
            CameraViewModel.state = cellType?.key ?? .flare
            adjustCollectionView(false)
        case .filter:
            let ani = !(CameraViewModel.state == .filters)
            CameraViewModel.state = .filters
            let tap = UITapGestureRecognizer(target: self, action: #selector(detectBackgroundTap))
            photoPreviewImageView.addGestureRecognizer(tap)
            adjustCollectionView(ani)
        case .edit:
            CameraViewModel.state = .edit
            adjustCollectionView(animate)
        case .brightness:
            CameraViewModel.state = .brightness
            adjustCollectionView(animate)
        case .contrast:
            CameraViewModel.state = .contrast
            adjustCollectionView(animate)
        case .temperature:
            CameraViewModel.state = .temperature
            adjustCollectionView(animate)
        case .saturation:
            CameraViewModel.state = .saturation
            adjustCollectionView(animate)
        case .shadowRemover:
            CameraViewModel.state = .shadowRemover
            adjustCollectionView(animate)
        case .flare:
            if CameraViewModel.state == .flare {
                let isUp = collectionViewHeight.constant == 222.0 ? true : false
                moveTheTray(up: !isUp, fromCreateSticker: false)
                return
            }
            CameraViewModel.state = .flare
            let tap = UITapGestureRecognizer(target: self, action: #selector(detectBackgroundTap))
            photoPreviewImageView.addGestureRecognizer(tap)
            if collectionView.frame.height == CameraViewModel.heightOfCollectionView[.flare] {
                animate = false
            }
            adjustCollectionView(animate)
        case .text:
            CameraViewModel.state = .text
            showCancelAsLeftNavBarButton(true)
            adjustCollectionView(animate)
        case .cancelText:
            CameraViewModel.state = .none
            showCancelAsLeftNavBarButton(false)
            dismissKeyboard()
            if CameraViewModel.stickersInProgress.isEmpty {
                stickerBaseView.isUserInteractionEnabled = false
            }
            adjustCollectionView(animate)
        case .fonts:
            CameraViewModel.state = .fonts
            adjustCollectionView()
        case .colors:
             CameraViewModel.state = .colors
            adjustCollectionView()
        case .draw:
            CameraViewModel.state = .draw
            adjustCollectionView()
        case .snapPicture:
            transformAd = previewView.transform.a
            stillImageOutput.capturePhoto(with: settings, delegate: self)
            CameraViewModel.state = .none
            adjustCollectionView()
            
        case .toggleFlash:
            if let avDevice = AVCaptureDevice.default(for: AVMediaType.video) {
                if avDevice.hasFlash {
                    if settings.flashMode == .auto {
                        settings.flashMode = .on
                    } else if settings.flashMode == .on {
                        settings.flashMode = .off
                    } else if settings.flashMode == .off {
                        settings.flashMode = .auto
                    }
                }
                adjustCollectionView(false)
            }

        case .runTimer:
            UIView.animate(withDuration: 0.5, animations: {
                self.timerView.alpha = 1.0
            }) { (_) in
                self.timerCounter = 3
                self.countdownToPicture()
            }

        case .swapCamera:
            captureSession.stopRunning()
            captureSession.beginConfiguration()
            defer {captureSession.commitConfiguration()}
            
            switch currentCamera {
            case .front:
                if discoveredCameras[.back] != nil {
                    if let input = try? AVCaptureDeviceInput(device: discoveredCameras[.back]!) {
                        captureSession.removeInput(currentInput!)
                        captureSession.removeOutput(stillImageOutput)
                        if captureSession.canAddInput(input) {
                            self.currentCamera = .back
                            if currentInput != nil {
                                captureSession.addInput(input)
                                currentInput = input
                                captureSession.addOutput(stillImageOutput)
                                setupLivePreview()
                            }
                        }
                    }
                } else {
                    // alert about permissions being in settings
                }
            case .back:
                if discoveredCameras[.front] != nil {
                    if let input = try? AVCaptureDeviceInput(device: discoveredCameras[.front]!) {
                        captureSession.removeInput(currentInput!)
                        captureSession.removeOutput(stillImageOutput)
                        if captureSession.canAddInput(input) {
                            self.currentCamera = .front
                            if currentInput != nil {
                                captureSession.addInput(input)
                                currentInput = input
                                captureSession.addOutput(stillImageOutput)
                                setupLivePreview()
                            }
                        }
                    }
                } else {
                    // alert about permissions being in settings
                }
                
            }
        }
    }
    
    @objc func sliderDidChange(_ sender: UISlider) {
        guard sender != verticalSlider else {
            if CameraViewModel.state == .draw {
                let value = 1.0 + CGFloat(sender.value) * 20.0
                drawView?.lineWidth = value
                drawView?.expectedLineWidth = value
                lineWidthViewConstraint.constant = value
                lineWidthView.layer.cornerRadius = lineWidthView.frame.height/2
                return
            }
            if CameraViewModel.activeSticker?.viewType == .text {
                let sticker = CameraViewModel.activeSticker
                let value = CGFloat(sender.value) + 0.5
                if sticker?.transform.a == 1.0 || sticker?.transform.b == 0.0 {
                    let height = CameraViewModel.textStickerStartingHeight * value
                    sticker?.frame.size.height = height
                } else {
                    if let tr = sticker?.transform {
                        sticker?.transform = CGAffineTransform.init(a: value, b: tr.b, c: tr.c, d: value, tx: tr.tx, ty: tr.ty)
                    }
                }
                sticker?.layoutIfNeeded()
            }
            return
        }
        let dict: [CameraCellType: SliderFilterType] = [.brightness: .brightness, .contrast: .contrast, .temperature: .temperature, .shadowRemover: .shadowRemover]
        let filter = dict[CameraViewModel.state]
        
        if originalPhotoPreview != nil, filter != nil {
            photoPreviewImageView?.image = FilterView.applyEditFilter(image: originalPhotoPreview!, filter: filter!, slider: sender)
        }
    }
    
    @objc func sliderDidEndSliding() {
        if let sticker = CameraViewModel.activeSticker as? TextStickerView, CameraViewModel.activeSticker?.viewType == .text {
            
            sticker.textStickerView.updateTextFont()
        }
    }
    
    func updateDrawingSubviews() {
        if CameraViewModel.drawings.count > 0 {
            addDrawButtonsToNavBar()
        }
        flexBackgroundView.subviews.filter({ $0.tag == CameraViewModel.drawingsTag }).forEach({ $0.removeFromSuperview()})
        CameraViewModel.drawings.forEach({ self.flexBackgroundView.addSubview($0) })
        if let dv = drawView {
            flexBackgroundView.bringSubviewToFront(dv)
        }
    }
    
    func didStartDrawing() {
        UIView.animate(withDuration: 0.5) {
            self.verticalSlider.alpha = 0
            self.lineWidthView.alpha = 0
            self.collectionView.alpha = 0
            self.imageEnhancementControlsView.alpha = 0.0
            self.navigationController?.setNavigationBarHidden(true, animated: true)
            self.trashView.alpha = 0.0
        }
    }
    
    func didEndDrawing() {
        UIView.animate(withDuration: 0.5) {
            self.verticalSlider.alpha = 1
            self.collectionView.alpha = 1
            self.lineWidthView.alpha = 1
            self.trashView.alpha = 1
            self.imageEnhancementControlsView.alpha = 1
            self.navigationController?.setNavigationBarHidden(false, animated: true)
            self.addDrawButtonsToNavBar(CameraViewModel.isEraseEnabled)
        }
    }
}

// MARK: -
// MARK: - UICollectionViewCellDelegate, DataSource, DelegateFlowLayout services
extension CameraViewController: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    
    // although not part of this extension, this is best kept close to the collectionView definitions
    func registerCells() {
        collectionView.register(UINib(nibName: CameraControlsCollectionViewCell.cellIdentifier, bundle: nil), forCellWithReuseIdentifier: CameraControlsCollectionViewCell.cellIdentifier)
        collectionView.register(UINib(nibName: FilterCollectionViewCell.cellIdentifier, bundle: nil), forCellWithReuseIdentifier: FilterCollectionViewCell.cellIdentifier)
        collectionView.register(UINib(nibName: EditCollectionViewCell.cellIdentifier, bundle: nil), forCellWithReuseIdentifier: EditCollectionViewCell.cellIdentifier)
        collectionView.register(UINib(nibName: SliderCollectionViewCell.cellIdentifier, bundle: nil), forCellWithReuseIdentifier: SliderCollectionViewCell.cellIdentifier)
        collectionView.register(UINib(nibName: FlareCategoryCollectionViewCell.cellIdentifier, bundle: nil), forCellWithReuseIdentifier: FlareCategoryCollectionViewCell.cellIdentifier)
        collectionView.register(UINib(nibName: StickerCollectionViewCell.cellIdentifier, bundle: nil), forCellWithReuseIdentifier: StickerCollectionViewCell.cellIdentifier)
        collectionView.register(UINib(nibName: FavsHeaderCollectionViewCell.cellIdentifier, bundle: nil), forCellWithReuseIdentifier: FavsHeaderCollectionViewCell.cellIdentifier)

        collectionView.register(UINib(nibName: FlareHeaderView.viewIdentifier, bundle: nil), forSupplementaryViewOfKind: UICollectionView.elementKindSectionHeader, withReuseIdentifier: FlareHeaderView.viewIdentifier)
        collectionView.register(UINib(nibName: FlareEmptyCollectionViewCell.cellIdentifier, bundle: nil), forCellWithReuseIdentifier: FlareEmptyCollectionViewCell.cellIdentifier)
        collectionView.register(UINib(nibName: FontCollectionViewCell.cellIdentifier, bundle: nil), forCellWithReuseIdentifier: FontCollectionViewCell.cellIdentifier)
        collectionView.register(UINib(nibName: ColorCollectionViewCell.cellIdentifier, bundle: nil), forCellWithReuseIdentifier: ColorCollectionViewCell.cellIdentifier)
        collectionView.register(UICollectionViewCell.self, forCellWithReuseIdentifier: String(describing: UICollectionViewCell.self))
    }
    
    func numberOfSections(in collectionView: UICollectionView) -> Int {
        return 1
    }
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        switch CameraViewModel.state {
        case .buttons, .brightness, .contrast, .temperature, .saturation, .shadowRemover: return 1
        case .flexFun, .smellies, .memes, .bubbles, .arrows, .smack, .emojis: return CameraViewModel.stickersSource[CameraViewModel.state]?.count ?? 1
        case .favs:
            if GFDefaults.getStickersArray(.recently).count > 0 {
                if GFDefaults.getStickersArray(.recently).count > 5 {
                    if GFDefaults.getStickersArray(.frequently).count > 5 {
                        return 23
                    } else {
                        return 13 + GFDefaults.getStickersArray(.frequently).count
                    }
                }
                return 13
            }
            return 1
            
        case .filters: return CameraViewModel.filterArray.count
        case .edit: return CameraViewModel.editArray.count
        case .flare: return 7
        case .fonts, .text: return FontManager.flatFontsArray.count
        case .colors, .draw: return CameraViewModel.colors.count
        default: break
        }
        return 0
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        if !CameraViewModel.editCellArray.contains(CameraViewModel.state) {
            editServiceIndexPath = nil
        }
        switch CameraViewModel.state {
        case .buttons:
            guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: CameraControlsCollectionViewCell.cellIdentifier, for: indexPath) as? CameraControlsCollectionViewCell else { return CameraControlsCollectionViewCell() }
            CameraViewModel.configureCell(cell: cell, type: .buttons)
            
            if let avDevice = AVCaptureDevice.default(for: AVMediaType.video) {
                if avDevice.hasFlash {
                    if settings.flashMode == .auto {
                        cell.flashButton.setImage(#imageLiteral(resourceName: "flashAuto"), for: .normal)
                    } else if settings.flashMode == .on {
                        var img = #imageLiteral(resourceName: "flashAuto")
                        if #available(iOS 13.0, *) {
                            img = #imageLiteral(resourceName: "flashAuto").withTintColor(.gfGreen, renderingMode: .alwaysTemplate)
                        } else {
                            // Fallback on earlier versions
                            img = img.withRenderingMode(.alwaysTemplate)
                            cell.flashButton.tintColor = .gfGreen
                        }
                        cell.flashButton.setImage(img, for: .normal)
                        cell.flashButton.tintColor = .gfGreen
                    } else if settings.flashMode == .off {
                        cell.flashButton.setImage(#imageLiteral(resourceName: "flashOff"), for: .normal)
                    }
                }
            }

            cell.delegate = self
            return cell

        case .filters:
            guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: FilterCollectionViewCell.cellIdentifier, for: indexPath) as? FilterCollectionViewCell else { return FilterCollectionViewCell() }
            CameraViewModel.configureCell(cell: cell, type: .filters)
            let strType = CameraViewModel.filterArray[indexPath.row]
            let filterType = strType?.values.first ?? .normal
            let basePic: UIImage? = (originalPhotoPreview != nil ? originalPhotoPreview : photoPreviewImageView.image)
            if cell.filterImageView != nil, let im = basePic {
                let filtered = FilterView.applyFilter(type: filterType, image: im, option: photoPreviewImageView)
                cell.filterImageView.image = filtered
            }
            cell.titleLabel.text = strType?.keys.first
            if selectedFilter == nil {
                switch indexPath.row {
                case 0:
                    cell.selectThisFilter(true)
                    selectedFilter = indexPath

                default:
                    cell.selectThisFilter(false)
                }
            } else if indexPath == selectedFilter {
                cell.selectThisFilter(true)
            } else {
                cell.selectThisFilter(false)
            }
            return cell
            
        case .edit:
            guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: EditCollectionViewCell.cellIdentifier, for: indexPath) as? EditCollectionViewCell else { return EditCollectionViewCell() }
            CameraViewModel.configureCell(cell: cell, type: .edit)
            cell.editImageView.image = CameraViewModel.editArray[indexPath.row]?.values.first
            cell.titleLabel.text = CameraViewModel.editArray[indexPath.row]?.keys.first
            
            return cell
            
        case .brightness:
            guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: SliderCollectionViewCell.cellIdentifier, for: indexPath) as? SliderCollectionViewCell else { return SliderCollectionViewCell() }
            cell.imageView.image = #imageLiteral(resourceName: "brightnessSelected")
            cell.sendSubviewToBack(cell.contentView)
            cell.slider.setValue(0.0, animated: true)
            cell.delegate = self

            return cell
        case .contrast:
            guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: SliderCollectionViewCell.cellIdentifier, for: indexPath) as? SliderCollectionViewCell else { return SliderCollectionViewCell() }
            cell.imageView.image = #imageLiteral(resourceName: "contrastSelected")
            cell.sendSubviewToBack(cell.contentView)
            cell.slider.setValue(0.5, animated: true)
            cell.delegate = self

            return cell
        case .temperature:
            guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: SliderCollectionViewCell.cellIdentifier, for: indexPath) as? SliderCollectionViewCell else { return SliderCollectionViewCell() }
            cell.imageView.image = #imageLiteral(resourceName: "temperatureSelected")
            cell.sendSubviewToBack(cell.contentView)
            cell.slider.setValue(0.5, animated: true)
            cell.delegate = self

            return cell
        case .saturation:
            guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: SliderCollectionViewCell.cellIdentifier, for: indexPath) as? SliderCollectionViewCell else { return SliderCollectionViewCell() }
            cell.imageView.image = #imageLiteral(resourceName: "structureSelected")
            cell.sendSubviewToBack(cell.contentView)
            cell.slider.setValue(0.5, animated: true)
            cell.delegate = self

            return cell

        case .shadowRemover:
            guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: SliderCollectionViewCell.cellIdentifier, for: indexPath) as? SliderCollectionViewCell else { return SliderCollectionViewCell() }
            cell.imageView.image = #imageLiteral(resourceName: "brightnessSelected")
            cell.sendSubviewToBack(cell.contentView)
            cell.slider.setValue(0.5, animated: true)
            cell.delegate = self

            return cell
            
        case .flare:
            guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: FlareCategoryCollectionViewCell.cellIdentifier, for: indexPath) as? FlareCategoryCollectionViewCell else { return FlareCategoryCollectionViewCell() }
            if indexPath.row < CameraViewModel.flareCategories.count {
                cell.backgroundImageView.image =  CameraViewModel.flareCategories[indexPath.row]?.values.first
                cell.titleLabel.text =  CameraViewModel.flareCategories[indexPath.row]?.keys.first
                // commented out because this might have caused infamous iPhone 11 crashes?
                if indexPath.row % 2 == 0 {
                    cell.backgroundImageViewLeadingConstraint.constant = 16
                    cell.backgroundImageViewTrailingConstraint.constant = 0
                } else {
                    cell.backgroundImageViewLeadingConstraint.constant = 0
                    cell.backgroundImageViewTrailingConstraint.constant = 16
                }
            }
            return cell
            
        case .favs:
            guard GFDefaults.getStickersArray(.recently).count > 0  else { // empty cell
                guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: FlareEmptyCollectionViewCell.cellIdentifier, for: indexPath) as? FlareEmptyCollectionViewCell else { return FlareEmptyCollectionViewCell() }
                cell.label.text = "favs.note".localized
                return cell
            }
            let cellClass = getTheCellClass(indexPath)
            switch cellClass {
            case 0: // header-like cells
                guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: FavsHeaderCollectionViewCell.cellIdentifier, for: indexPath) as? FavsHeaderCollectionViewCell else { return FavsHeaderCollectionViewCell() }
                if indexPath.row == 0 {
                    cell.label.text = "favs.recentlyUsedTitle".localized.uppercased()
                } else {
                    cell.label.text = "favs.favsTitle".localized.uppercased()
                }
                cell.isUserInteractionEnabled = false
                return cell
                
            default: // sticker cells
                guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: StickerCollectionViewCell.cellIdentifier, for: indexPath) as? StickerCollectionViewCell else { return StickerCollectionViewCell() }
                if cellClass == 1 { // recently used
                    let source = GFDefaults.getStickersArray(.recently)
                    if CameraViewModel.stickersSource[.emojis]?.contains(source[indexPath.row - 1]) ?? false {
                        cell.imageView?.image = source[indexPath.row - 1].image()
                    } else {
                        cell.imageView?.image = UIImage(named: source[indexPath.row - 1] )
                    }
                    return cell
                } else if cellClass == 2 { // most frequently used
                    let source = GFDefaults.getStickersArray(.frequently)
                    let before = GFDefaults.getStickersArray(.recently).count > 5 ? 12 : 7
                    let newIPRow = indexPath.row - before
                    if newIPRow >= 0, newIPRow < GFDefaults.getStickersArray(.recently).count, newIPRow < source.count {
                        if CameraViewModel.stickersSource[.emojis]?.contains(source[newIPRow]) ?? false {
                            cell.imageView?.image = source[newIPRow].image()
                        } else {
                            cell.imageView?.image = UIImage(named: source[newIPRow])
                        }
                    } else { // empty sticker cell please
                        cell.backgrounder.isHidden = true
                        cell.isUserInteractionEnabled = false
                    }
                    return cell

                } // empty sticker cell
                cell.backgrounder.isHidden = true
                cell.isUserInteractionEnabled = false
                return cell
            }
           
        case .flexFun, .smellies, .memes, .bubbles, .arrows, .smack, .emojis:
            guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: StickerCollectionViewCell.cellIdentifier, for: indexPath) as? StickerCollectionViewCell else { return StickerCollectionViewCell() }
            if let source = CameraViewModel.stickersSource[CameraViewModel.state] {
                if CameraViewModel.state == .emojis {
                    cell.imageView?.image = source[indexPath.row].image()
                } else {
                    if indexPath.row < source.count,  CameraViewModel.stickersSource[.emojis]?.contains(source[indexPath.row]) ?? false {
                        cell.imageView?.image = source[indexPath.row].image()
                    } else  if indexPath.row >= source.count {
                        return cell
                    } else {
                        cell.imageView?.image = UIImage(named: source[indexPath.row] )
                    }
                }
            }
            
            return cell
        case .fonts, .text:
            guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: FontCollectionViewCell.cellIdentifier, for: indexPath) as? FontCollectionViewCell else {
                return FontCollectionViewCell() }
            if indexPath.row < FontManager.flatFontsArray.count {
                let font = FontManager.flatFontsArray[indexPath.row]
                cell.font = UIFont(name: font, size: 24)
                if cell.font == GFDefaults.lastFont {
                    cell.configureLabel(true)
                } else {
                    cell.configureLabel(false)
                }
            }
            return cell

        case .colors, .draw:
            guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: ColorCollectionViewCell.cellIdentifier, for: indexPath) as? ColorCollectionViewCell else { return ColorCollectionViewCell()}
            guard indexPath.row < CameraViewModel.colors.count else { return cell }
            cell.colorView.backgroundColor = CameraViewModel.colors[indexPath.row]
            if CameraViewModel.selectedColorIndexPath == indexPath {
                cell.configureColorView(true)
            } else {
                cell.configureColorView(false)
            }
            return cell
            
        default:
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: String(describing: UICollectionViewCell.self), for: indexPath)
            return cell
        }
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, referenceSizeForHeaderInSection section: Int) -> CGSize {
        let headerRequired: [CameraCellType] = [ .favs, .flexFun, .emojis, .smellies, .memes, .bubbles, .arrows, .smack, .flare]
        if headerRequired.contains(CameraViewModel.state) {
            return CGSize(width: collectionView.frame.size.width, height: 44)
        }
        return CGSize.zero
    }

    // header for flare presentation
    func collectionView(_ collectionView: UICollectionView, viewForSupplementaryElementOfKind kind: String, at indexPath: IndexPath) -> UICollectionReusableView {
        switch kind {
        case UICollectionView.elementKindSectionHeader:
            guard let headerView = collectionView.dequeueReusableSupplementaryView(ofKind: kind, withReuseIdentifier: FlareHeaderView.viewIdentifier, for: indexPath) as? FlareHeaderView else { return FlareHeaderView() }
            headerView.setupView((CameraViewModel.state == .favs))
            headerView.delegate = self
            headerView.backgroundColor = .black // otherwise the cells passing under it will be visible through the headerView

            return headerView
            
        case UICollectionView.elementKindSectionFooter:
            let footerView = collectionView.dequeueReusableSupplementaryView(ofKind: kind, withReuseIdentifier: "Footer", for: indexPath)
            footerView.backgroundColor = UIColor.green
            return footerView
            
        default:
            assert(false, "Unexpected element kind")
        }
        return FlareHeaderView()
    }

    
    func collectionView(_ collectionView: UICollectionView,
                        layout collectionViewLayout: UICollectionViewLayout,
                        sizeForItemAt indexPath: IndexPath) -> CGSize {
        switch CameraViewModel.state {
        case .filters, .edit:
            return CGSize(width: 103.0, height: CameraViewModel.heightOfCollectionView[CameraViewModel.state] ?? 222.0)
        case .flare:
            return CGSize(width: view.frame.size.width/2.0 - 8.0, height: 42.0)
        case .favs:
            if GFDefaults.getStickersArray(.recently).count > 0 {
                let cellClass = getTheCellClass(indexPath)
                switch cellClass {
                case 0: return CGSize(width: view.frame.size.width, height: 20.0)
                default: return CGSize(width: 68.0, height: 68.0)
                }
            }
            return CGSize(width: view.frame.size.width, height: (CameraViewModel.heightOfCollectionView[CameraViewModel.state] ?? 222.0) - 44.0)
        case .text, .colors, .fonts, .draw: return CGSize(width: 55.0, height: 55.0)
        case .flexFun, .smellies, .memes, .bubbles, .arrows, .smack, .emojis:
            return CGSize(width: 68.0, height: 68.0)
        default:
            return CGSize(width: view.frame.size.width, height: CameraViewModel.heightOfCollectionView[CameraViewModel.state] ?? 222.0)
        }
    }
    
    func collectionView(_ collectionView: UICollectionView,
                        layout collectionViewLayout: UICollectionViewLayout,
                        insetForSectionAt section: Int) -> UIEdgeInsets {
        return UIEdgeInsets(top: 0.0, left: 0.0, bottom: 0.0, right: 0.0)
    }

    // for service selection
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if CameraViewModel.state == .filters, selectedFilter != nil {
            selectedFilter = indexPath
            adjustCollectionView(false)
            if originalPhotoPreview == nil {
                originalPhotoPreview = photoPreviewImageView.image
            }
            let strType = CameraViewModel.filterArray[indexPath.row]
            let filterType = strType?.values.first ?? .normal
            if let im = originalPhotoPreview {
                let filtered = FilterView.applyFilter(type: filterType, image: im, option: photoPreviewImageView)
                photoPreviewImageView.image = filtered
            }
            return
        }
        if CameraViewModel.state == .edit { // from selecting an edit, so no animation please
            didTapForCameraAction(CameraViewModel.editActionArray[indexPath.row])
            editServiceIndexPath = indexPath
            if originalPhotoPreview == nil {
                originalPhotoPreview = photoPreviewImageView.image
            }
            adjustCollectionView(false)
            return
        }
        if CameraViewModel.state == .flare {
            let cellType = CameraViewModel.flareTypes[indexPath.row]
            if CameraViewModel.flareDictionary[cellType] != nil {
                didTapForCameraAction(CameraViewModel.flareDictionary[cellType] ?? .flare)
                CameraViewModel.canCreateStickerStickerView = true
            }
            return
        }
        if CameraViewModel.flareTypes.contains(CameraViewModel.state), CameraViewModel.canCreateStickerStickerView {
            createStickerView(indexPath: indexPath)
        }
        if CameraViewModel.state == .fonts || CameraViewModel.state == .text {
            if CameraViewModel.selectedFontIndexPath != indexPath {
                CameraViewModel.configureFontCell(ip: CameraViewModel.selectedFontIndexPath, cv: collectionView, isSelected: true)
            }
            CameraViewModel.selectedFontIndexPath = indexPath
            CameraViewModel.configureFontCell(ip: indexPath, cv: collectionView, isSelected: true)
            GFDefaults.lastFont = UIFont(name: FontManager.flatFontsArray[indexPath.row], size: 24)
            collectionView.reloadData()
        }
        if CameraViewModel.state == .colors || CameraViewModel.state == .draw {
            guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: ColorCollectionViewCell.cellIdentifier, for: indexPath) as? ColorCollectionViewCell else {
                return
            }
            if CameraViewModel.selectedColorIndexPath != indexPath {
                CameraViewModel.previousSelectedColorIndexPath = CameraViewModel.selectedColorIndexPath
                CameraViewModel.selectedColorIndexPath = indexPath
            }
            cell.configureColorView(true)
            collectionView.reloadData()
            if CameraViewModel.state == .draw {
                drawView?.strokeColor = CameraViewModel.colors[CameraViewModel.selectedColorIndexPath.row].cgColor
                lineWidthView.backgroundColor = CameraViewModel.colors[CameraViewModel.selectedColorIndexPath.row]
            } else {
                // handle the color change
                let tsv = (CameraViewModel.activeSticker as? TextStickerView)
                if CameraViewModel.colorText {
                    tsv?.textStickerView?.textColor = CameraViewModel.colors[indexPath.row]
                } else {
                    tsv?.textStickerView?.backgroundColor = CameraViewModel.colors[indexPath.row]
                }
                if tsv?.textStickerView?.textColor == tsv?.textStickerView?.backgroundColor {
                    tsv?.textStickerView?.backgroundColor = .clear
                }
            }
        }
    }
    
    // MARK: - Favs Helper
    // returns 0, 1, 2 or 3: 0 = header cell, 1 = recently used, 2 = frequently used, 3 = dummy cell
    private func getTheCellClass(_ indexPath: IndexPath) -> Int {
        guard indexPath.row != 0 else {
            return 0
        }
        let count = GFDefaults.getStickersArray(.recently).count > 5 ? 11 : 6
        if indexPath.row > GFDefaults.getStickersArray(.recently).count, indexPath.row < count {
            return 3
        }
        if indexPath.row + 1 <= count { return 1 }
        if indexPath.row == count { return 0 }
        return 2
    }
        
}
 
/* TODO: - use this to manipulate the image capture lifecycle: */
 extension CameraViewController: AVCapturePhotoCaptureDelegate {
    
    @objc func countdownToPicture() {
        if timerLabel.text == "3" {
            if timerCounter == 3 {
                timerCounter = 2
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    self.timerLabel.text = "2"
                    self.countdownToPicture()
                }
            }
        } else if timerLabel.text == "2" {
            timerCounter = 1
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                self.timerLabel.text = "1"
                self.countdownToPicture()
            }
        } else if timerLabel.text == "1" {
            timerCounter = 0
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                self.timerView.alpha = 0.0
                self.timerLabel.text = ""
                self.timerCounter = 0
                self.countdownToPicture()
            }
        } else {
            timerCounter = 3
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                self.timerLabel.text = "3"
                self.timerCounter = 3
                self.didTapForCameraAction(.snapPicture)
            }
        }
    }
}

// MARK: - ImagePickerControllerDelegate services

extension CameraViewController: UIImagePickerControllerDelegate, UINavigationControllerDelegate {
    //get image from source type
    private func getImage(fromSourceType sourceType: UIImagePickerController.SourceType) {
        
        //Check is source type available
        if UIImagePickerController.isSourceTypeAvailable(sourceType) {
            if sourceType == .camera {
                let cvc = CameraViewController.storyboardInstance()
                self.present(cvc, animated: true)
                return
            }
            let imagePickerController = UIImagePickerController()
            imagePickerController.delegate = self
            imagePickerController.sourceType = sourceType
            self.present(imagePickerController, animated: true, completion: nil)
        }
    }
    
    //MARK:- UIImagePickerViewDelegate.
    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
        
        self.dismiss(animated: true) { [weak self] in
            
            guard let image = info[UIImagePickerController.InfoKey.originalImage] as? UIImage else { return }
            //Setting image to your image view
            self?.photoPreviewImageView.image = image
            if let ppiv = self?.photoPreviewImageView {
                self?.doGestureSet(ppiv)
            }
            self?.stopTheCamera()
            CameraViewModel.state = .none
            self?.adjustCollectionView()
        }
    }
    
    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
        picker.dismiss(animated: true, completion: nil)
        if photoButton != nil {
            didTapButton(photoButton!)
        }
    }
}

// MARK: - UITextViewDelegate
extension CameraViewController: UITextViewDelegate {
    
    func textViewDidChange(_ textView: UITextView) {
        let fixedWidth = textView.frame.size.width
        let newSize = textView.sizeThatFits(CGSize(width: fixedWidth, height: CGFloat.greatestFiniteMagnitude))
        textView.frame.size = CGSize(width: max(newSize.width, fixedWidth), height: newSize.height)
    }
    
    func textViewShouldBeginEditing(_ textView: UITextView) -> Bool {
        textView.textColor = .white
        CameraViewModel.canCreateTextStickerView = false
        return true
    }
    
    func textView(_ textView: UITextView, shouldChangeTextIn range: NSRange, replacementText text: String) -> Bool {
        if textView.text?.count ?? 0 == 0 {
            addTextStickerButtonsToNavBar()
        }
        return true
    }
    
    func textViewShouldReturn(_ textView: UITextView) -> Bool {
        if textView.text == nil {
            return false
        }
        textView.resignFirstResponder()
        return true
    }
}

//MARK:- UIGestureRecognizerDelegate Methods
extension CameraViewController: UIGestureRecognizerDelegate {
    
    func gestureRecognizer(_: UIGestureRecognizer, shouldRecognizeSimultaneouslyWith
        shouldRecognizeSimultaneouslyWithGestureRecognizer:UIGestureRecognizer) -> Bool {
        return true
    }

}
