# Generated code do not commit.
file(TO_CMAKE_PATH "P:\\flutter_sdk\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "P:\\GameFlex\\gameflex_mobile" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=P:\\flutter_sdk\\flutter"
  "PROJECT_DIR=P:\\GameFlex\\gameflex_mobile"
  "FLUTTER_ROOT=P:\\flutter_sdk\\flutter"
  "FLUTTER_EPHEMERAL_DIR=P:\\GameFlex\\gameflex_mobile\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=P:\\GameFlex\\gameflex_mobile"
  "FLUTTER_TARGET=P:\\GameFlex\\gameflex_mobile\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=P:\\GameFlex\\gameflex_mobile\\.dart_tool\\package_config.json"
)
