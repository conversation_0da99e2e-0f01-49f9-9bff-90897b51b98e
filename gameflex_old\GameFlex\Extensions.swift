//
//  Extensions.swift
//  GameFlex
//
//  Created by <PERSON> on 7/5/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import Foundation
import UIKit
import SwiftDate
import LocalAuthentication

extension Date {
    /// String Calculation of the amount of time ago from past date
    func timeAgoSinceDate(numericDates: Bool) -> String {
        let now = Date()

        let diff = now.timeIntervalSince(self).toUnits([.day, .hour, .minute, .second])

        if let day = diff[.day] {
            if day >= 2 {
                return "\(day) " + "date.daysAgo".localized
            } else if day >= 1 {
                return stringToReturn(flag: numericDates, strings: ("date.daysAgo".localized, "date.aDayAgo".localized))
            }
        }
        
        if let hour = diff[.hour] {
            if hour >= 2 {
                return "\(hour) " + "date.hoursAgo".localized
            } else if hour >= 1 {
                return stringToReturn(flag: numericDates, strings: ("date.hourAgo".localized, "date.anHourAgo".localized))
            }
        }

        if let minute = diff[.minute] {
            if minute >= 5 {
                return "\(minute) " + "date.minutesAgo".localized
            } else {
                return "date.justNow".localized
            }
        }

        if let _ = diff[.second] {
            return "date.justNow".localized
        }

        return "date.justNow".localized
    }

    private func stringToReturn(flag: Bool, strings: (String, String)) -> String {
        if flag {
            return strings.0
        } else {
            return strings.1
        }
    }

    func toLocalTime() -> Date {
        let timezone = TimeZone.current
        let seconds = TimeInterval(timezone.secondsFromGMT(for: self))
        return Date(timeInterval: seconds, since: self)
    }
}

extension Double
{
    func truncate(places : Int)-> Double {
        return Double(floor(pow(10.0, Double(places)) * self)/pow(10.0, Double(places)))
    }
}


extension Notification.Name {
    
    static let updateProgress = Notification.Name("updateProgress") // used in NetworkServices to track %
    static let swordsMovieFinished = Notification.Name("swordsMovieFinished")
}

extension String {
    var localized: String {
        return NSLocalizedString(self, comment: "")
    }
    
    func image() -> UIImage? {
        let size = CGSize(width: 100, height: 100)
        UIGraphicsBeginImageContextWithOptions(size, false, 0);
        UIColor.clear.set()

        let stringBounds = (self as NSString).size(withAttributes: [NSAttributedString.Key.font: UIFont.systemFont(ofSize: 75)])
        let originX = (size.width - stringBounds.width)/2
        let originY = (size.height - stringBounds.height)/2
        let rect = CGRect(origin: CGPoint(x: originX, y: originY), size: size)
        UIRectFill(rect)

        (self as NSString).draw(in: rect, withAttributes: [NSAttributedString.Key.font: UIFont.systemFont(ofSize: 75)])

        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return image
    }
    
    func trim() -> String {
        return self.trimmingCharacters(in: .whitespacesAndNewlines)
    }

}

extension UIButton {
    
    // for 'primary' standard, enforces min width, colors, height, font, cornerRadius, centered
    func applyPrimaryGFButtonStandards(_ enabled: Bool = true) {
        let minWidth: CGFloat = 80.0
        layer.cornerRadius = frame.size.height/2
        clipsToBounds = true
        setTitleColor(UIColor.black, for: .normal)
        self.titleLabel?.font = .boldSystemFont(ofSize: 12)
        self.titleLabel?.textAlignment = .center

        tintColor = UIColor.black
        let width = (self.titleLabel?.width(.boldSystemFont(ofSize: 12)) ?? minWidth)
        let constant = width > 100.0 ? width + 16.0 : minWidth
        if !(superview?.superview?.isKind(of: UITableViewCell.self) ?? false) { // prevents the button appearing in wrong places after reload
            addConstraint(NSLayoutConstraint(item: self, attribute: .width, relatedBy: .equal, toItem: nil, attribute: .notAnAttribute, multiplier: 1, constant: constant))
            addConstraint(NSLayoutConstraint(item: self, attribute: .height, relatedBy: .equal, toItem: nil, attribute: .notAnAttribute, multiplier: 1, constant: 24))
            translatesAutoresizingMaskIntoConstraints = false
            center.y = UIScreen.main.bounds.width/2.0
        }
        if enabled {
            alpha = 1.0
            isEnabled = true
        } else {
            alpha = 0.6
            isEnabled = false
        }
    }

}

extension UIColor {
    
    static var gfGreen: UIColor {
        if let color = UIColor(hex: "#28F4C3ff") {
            return color
        }
        return .green
    }
    
    static let gfGrayText = UIColor(red: 244.0/255.0, green: 244.0/255.0, blue: 244.0/255.0, alpha: 0.5)
    static let gfDarkGrayMask = UIColor(red: 244.0/255.0, green: 244.0/255.0, blue: 244.0/255.0, alpha: 0.1)
    static let gfOffWhite = UIColor(red: 244.0/255.0, green: 244.0/255.0, blue: 244.0/255.0, alpha: 1.0)
    static let gfDarkBackground = UIColor(red: 41/255.0, green: 54/255.0, blue: 66/255, alpha: 0.6)
    static let gfDarkBackground100 = UIColor(red: 41/255.0, green: 54/255.0, blue: 66/255, alpha: 1.0)
    static let gfDarkBackground40 = UIColor(red: 41/255.0, green: 54/255.0, blue: 66/255, alpha: 0.4)
    static let gfTextLight_8FAABD = UIColor(red: 143/255.0, green: 170/255.0, blue: 189/255.0, alpha: 1)
    static let gfYellow_F2DE76 = UIColor(red: 242/255.0, green: 222/255.0, blue: 118/255, alpha: 1.0)
    static let gfBlue_00D5FF = UIColor(red: 0, green: 213/255, blue: 1, alpha: 1)
    
    public convenience init?(hex: String) {
        let r, g, b, a: CGFloat

        if hex.hasPrefix("#") {
            let start = hex.index(hex.startIndex, offsetBy: 1)
            let hexColor = String(hex[start...])

            if hexColor.count == 8 {
                let scanner = Scanner(string: hexColor)
                var hexNumber: UInt64 = 0

                if scanner.scanHexInt64(&hexNumber) {
                    r = CGFloat((hexNumber & 0xff000000) >> 24) / 255
                    g = CGFloat((hexNumber & 0x00ff0000) >> 16) / 255
                    b = CGFloat((hexNumber & 0x0000ff00) >> 8) / 255
                    a = CGFloat(hexNumber & 0x000000ff) / 255

                    self.init(red: r, green: g, blue: b, alpha: a)
                    return
                }
            }
        }
        return nil
    }
}

extension CGRect {
    var center: CGPoint { return CGPoint(x: midX, y: midY) }
}

extension String {
    func height(withWidth width: CGFloat, font: UIFont) -> CGFloat {
        let maxSize = CGSize(width: width, height: CGFloat.greatestFiniteMagnitude)
        let actualSize = self.boundingRect(with: maxSize, options: [.usesLineFragmentOrigin], attributes: [.font: font], context: nil)
        return actualSize.height
    }
}

extension NSAttributedString {
    func height(withWidth width: CGFloat) -> CGFloat {
        let maxSize = CGSize(width: width, height: CGFloat.greatestFiniteMagnitude)
        let actualSize = boundingRect(with: maxSize, options: [.usesLineFragmentOrigin], context: nil)
        return actualSize.height
    }
}

extension UILabel {
    func textHeight(withWidth width: CGFloat) -> CGFloat {
        guard let text = text else {
            return 0
        }
        return text.height(withWidth: width, font: font)
    }

    func attributedTextHeight(withWidth width: CGFloat) -> CGFloat {
        guard let attributedText = attributedText else {
            return 0
        }
        return attributedText.height(withWidth: width)
    }

    func width(_ font: UIFont) -> CGFloat {
        var rect: CGRect = frame //get frame of label
        rect.size = (text?.size(withAttributes: [NSAttributedString.Key.font: UIFont(name: font.fontName, size: font.pointSize)!]))! //Calculate as per label font
        return rect.width
    }
    
    func underline() {
        self.attributedText = NSAttributedString(string: self.text ?? "", attributes:
        [.underlineStyle: NSUnderlineStyle.single.rawValue])
    }
    
    func noUnderline() {
        self.attributedText = NSAttributedString(string: self.text ?? "", attributes: [:])
    }
}

extension UIImage {
    
    func cropped(boundingBox: CGRect) -> UIImage? {
        guard let cgImage = self.cgImage?.cropping(to: boundingBox) else {
            return nil
        }
        return UIImage(cgImage: cgImage)
    }
    
    func resized(withPercentage percentage: CGFloat, isOpaque: Bool = true) -> UIImage? {
        let canvas = CGSize(width: size.width * percentage, height: size.height * percentage)
        let format = imageRendererFormat
        format.opaque = isOpaque
        return UIGraphicsImageRenderer(size: canvas, format: format).image {
            _ in draw(in: CGRect(origin: .zero, size: canvas))
        }
    }
    
    func compress(to kb: Int, allowedMargin: CGFloat = 0.2) -> UIImage? {
        let bytes = kb * 1024
        var compression: CGFloat = 1.0
        let step: CGFloat = 0.05
        var holderImage = self
        var complete = false
        while(!complete) {
            if let data = holderImage.jpegData(compressionQuality: 1.0) {
                let ratio = data.count / bytes
                if data.count < Int(CGFloat(bytes) * (1 + allowedMargin)) {
                    complete = true
                    return UIImage(data: data)
                } else {
                    let multiplier:CGFloat = CGFloat((ratio / 5) + 1)
                    compression -= (step * multiplier)
                }
            }
            
            guard let newImage = holderImage.resized(withPercentage: compression) else { break }
            holderImage = newImage
        }
        return holderImage
    }
    
    func flipHorizontally() -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(self.size, true, self.scale)
        let context = UIGraphicsGetCurrentContext()!
        
        context.translateBy(x: self.size.width/2, y: self.size.height/2)
        context.scaleBy(x: -1.0, y: 1.0)
        context.translateBy(x: -self.size.width/2, y: -self.size.height/2)
        
        self.draw(in: CGRect(x: 0, y: 0, width: self.size.width, height: self.size.height))
        
        let newImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        
        return newImage
    }
    
    func mergeWith(topImage: UIImage) -> UIImage {
        let bottomImage = self
        
        UIGraphicsBeginImageContext(size)
        
        let areaSize = CGRect(x: 0, y: 0, width: bottomImage.size.width, height: bottomImage.size.height)
        bottomImage.draw(in: areaSize)
        
        topImage.draw(in: areaSize, blendMode: .normal, alpha: 1.0)
        
        let mergedImage = UIGraphicsGetImageFromCurrentImageContext()!
        UIGraphicsEndImageContext()
        return mergedImage
    }
    
    func combine(with backgroundColor: UIColor) -> UIImage {
        //  Create rect to fit the image
        let rect = CGRect(x: 0, y: 0, width: self.size.width, height: self.size.height)
        // Create image context. 0 means scale of device's main screen
        UIGraphicsBeginImageContextWithOptions(rect.size, false, 0)
        let context = UIGraphicsGetCurrentContext()!
        
        //  Fill the rect by the final color
        backgroundColor.setFill()
        context.fill(rect)
        
        //  Make the final shape by masking the drawn color with the images alpha values
        self.draw(in: rect, blendMode: .destinationIn, alpha: 1)
        
        //  Create new image from the context
        let combined = UIGraphicsGetImageFromCurrentImageContext()!
        
        //  Release context
        UIGraphicsEndImageContext()

        return combined
    }
    
    func replace(color: UIColor, withColor replacingColor: UIColor) -> UIImage {
            guard let inputCGImage = self.cgImage else {
                return self
            }
            let colorSpace       = CGColorSpaceCreateDeviceRGB()
            let width            = inputCGImage.width
            let height           = inputCGImage.height
            let bytesPerPixel    = 4
            let bitsPerComponent = 8
            let bytesPerRow      = bytesPerPixel * width
            let bitmapInfo       = RGBA32.bitmapInfo

            guard let context = CGContext(data: nil, width: width, height: height, bitsPerComponent: bitsPerComponent, bytesPerRow: bytesPerRow, space: colorSpace, bitmapInfo: bitmapInfo) else {
                print("unable to create context")
                return self
            }
            context.draw(inputCGImage, in: CGRect(x: 0, y: 0, width: width, height: height))

            guard let buffer = context.data else {
                return self
            }

            let pixelBuffer = buffer.bindMemory(to: RGBA32.self, capacity: width * height)

        let inColor = RGBA32(color: color)
        let outColor = RGBA32(color: replacingColor)
            for row in 0 ..< Int(height) {
                for column in 0 ..< Int(width) {
                    let offset = row * width + column
                    if pixelBuffer[offset] == inColor {
                        pixelBuffer[offset] = outColor
                    }
                }
            }

            guard let outputCGImage = context.makeImage() else {
                return self
            }
            return UIImage(cgImage: outputCGImage, scale: self.scale, orientation: self.imageOrientation)
    }
    
}
    
struct RGBA32: Equatable {
    private var color: UInt32

    var redComponent: UInt8 {
        return UInt8((self.color >> 24) & 255)
    }

    var greenComponent: UInt8 {
        return UInt8((self.color >> 16) & 255)
    }

    var blueComponent: UInt8 {
        return UInt8((self.color >> 8) & 255)
    }

    var alphaComponent: UInt8 {
        return UInt8((self.color >> 0) & 255)
    }

    init(red: UInt8, green: UInt8, blue: UInt8, alpha: UInt8) {
        self.color = (UInt32(red) << 24) | (UInt32(green) << 16) | (UInt32(blue) << 8) | (UInt32(alpha) << 0)
    }

    init(color: UIColor) {
        let components = color.cgColor.components ?? [0.0, 0.0, 0.0, 1.0]
        let colors = components.map { UInt8($0 * 255) }
        if colors.count < 4 {
            self.init(red: 0, green: 0, blue: 0, alpha: 1) // black
        } else {
            self.init(red: colors[0], green: colors[1], blue: colors[2], alpha: colors[3])
        }
    }

    static let bitmapInfo = CGImageAlphaInfo.premultipliedLast.rawValue | CGBitmapInfo.byteOrder32Little.rawValue

    static func ==(lhs: RGBA32, rhs: RGBA32) -> Bool {
        return lhs.color == rhs.color
    }

}


extension UIImageView{
    
    var contentClippingRect: CGRect {
        guard let image = image else { return bounds }
        guard contentMode == .scaleAspectFit else { return bounds }
        guard image.size.width > 0 && image.size.height > 0 else { return bounds }

        let scale: CGFloat
        if image.size.width > image.size.height {
            scale = bounds.width / image.size.width
        } else {
            scale = bounds.height / image.size.height
        }

        let size = CGSize(width: image.size.width * scale, height: image.size.height * scale)
        let x = (bounds.width - size.width) / 2.0
        let y = (bounds.height - size.height) / 2.0

        return CGRect(x: x, y: y, width: size.width, height: size.height)
    }

    
    func imageFrame() -> CGRect{
        let imageViewSize = self.frame.size
        guard let imageSize = self.image?.size else{return CGRect.zero}
        let imageRatio = imageSize.width / imageSize.height
        let imageViewRatio = imageViewSize.width / imageViewSize.height
        
        // rotates the view
        if imageRatio < imageViewRatio {
            let scaleFactor = imageViewSize.height / imageSize.height
            let width = imageSize.width * scaleFactor
            let topLeftX = (imageViewSize.width - width) * 0.5
            return CGRect(x: topLeftX, y: 0, width: width, height: imageViewSize.height)
        } else {
            let scalFactor = imageViewSize.width / imageSize.width
            let height = imageSize.height * scalFactor
            let topLeftY = (imageViewSize.height - height) * 0.5
            return CGRect(x: 0, y: topLeftY, width: imageViewSize.width, height: height)
        }
    }
    
    func resizeImage(image: UIImage, targetSize: CGSize) -> UIImage {
        let size = image.size
        let widthRatio = targetSize.width / size.width
        let heightRatio = targetSize.height / size.height
        let newSize = CGSize(width: size.width * widthRatio, height: size.height * heightRatio)
        let rect = CGRect(x: 0, y: 0, width: newSize.width, height: newSize.height)
        UIGraphicsBeginImageContextWithOptions(newSize, false, 1.0)
        image.draw(in: rect)
        let newImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return newImage!
    }
}

extension UIView {
    
    func asImage() -> UIImage {
        let renderer = UIGraphicsImageRenderer(bounds: bounds)
        return renderer.image { rendererContext in
            layer.render(in: rendererContext.cgContext)
        }
    }
    
    // merges the photo and stickers into a single UIImage
    func capture() -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(self.frame.size, self.isOpaque, UIScreen.main.scale)
        self.layer.render(in: UIGraphicsGetCurrentContext()!)
        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        
        return image
    }

    func enableZoom() {
        let pinchGesture = UIPinchGestureRecognizer(target: self, action: #selector(startZooming(_:)))
        isUserInteractionEnabled = true
        addGestureRecognizer(pinchGesture)
    }
    
    @objc private func startZooming(_ sender: UIPinchGestureRecognizer) {
        let scaleResult = sender.view?.transform.scaledBy(x: sender.scale, y: sender.scale)
        guard let scale = scaleResult, scale.a > 0.5, scale.d > 0.5 else { return }
        sender.view?.transform = scale
        if let view = sender.view as? StickerView {
            view.buttons.forEach({ $0.transform = CGAffineTransform(scaleX: 1.0/scale.a, y: 1.0/scale.d) })
        }
        sender.scale = 1
    }
    
    func addDashedBorder() {
        let color = UIColor.white.cgColor
        
        let shapeLayer:CAShapeLayer = CAShapeLayer()
        let frameSize = self.frame.size
        let shapeRect = CGRect(x: 0, y: 0, width: frameSize.width, height: frameSize.height)
        
        shapeLayer.bounds = shapeRect
        shapeLayer.position = CGPoint(x: frameSize.width/2, y: frameSize.height/2)
        shapeLayer.fillColor = UIColor.clear.cgColor
        shapeLayer.strokeColor = color
        shapeLayer.lineWidth = 2
        shapeLayer.lineJoin = CAShapeLayerLineJoin.round
        shapeLayer.lineDashPattern = [6,3]
        shapeLayer.path = UIBezierPath(roundedRect: shapeRect, cornerRadius: 5).cgPath
        shapeLayer.name = "dashedBorder"
        self.layer.addSublayer(shapeLayer)
    }
}

extension UIDevice {
    // USAGE: let modelName = UIDevice.modelName
    //        returns "Simulator iPad Pro 9.7 Inch"
    
    static let modelName: String = {
        var systemInfo = utsname()
        uname(&systemInfo)
        let machineMirror = Mirror(reflecting: systemInfo.machine)
        let identifier = machineMirror.children.reduce("") { identifier, element in
            guard let value = element.value as? Int8, value != 0 else { return identifier }
            return identifier + String(UnicodeScalar(UInt8(value)))
        }
        
        func mapToDevice(identifier: String) -> String { // swiftlint:disable:this cyclomatic_complexity
            #if os(iOS)
            switch identifier {
            case "iPod5,1":                                 return "iPod Touch 5"
            case "iPod7,1":                                 return "iPod Touch 6"
            case "iPhone5,1", "iPhone5,2":                  return "iPhone 5"
            case "iPhone5,3", "iPhone5,4":                  return "iPhone 5c"
            case "iPhone6,1", "iPhone6,2":                  return "iPhone 5s"
            case "iPhone7,2":                               return "iPhone 6"
            case "iPhone7,1":                               return "iPhone 6 Plus"
            case "iPhone8,1":                               return "iPhone 6s"
            case "iPhone8,2":                               return "iPhone 6s Plus"
            case "iPhone9,1", "iPhone9,3":                  return "iPhone 7"
            case "iPhone9,2", "iPhone9,4":                  return "iPhone 7 Plus"
            case "iPhone8,4":                               return "iPhone SE"
            case "iPhone10,1", "iPhone10,4":                return "iPhone 8"
            case "iPhone10,2", "iPhone10,5":                return "iPhone 8 Plus"
            case "iPhone10,3", "iPhone10,6":                return "iPhone X"
            case "iPhone11,8":                              return "iPhone XR"
            case "iPhone11,2":                              return "iPhone XS"
            case "iPhone11,4", "iPhone11,6":                return "iPhone XS Max"
            case "iPhone12,1":                              return "iPhone 11"
            case "iPhone12,3":                              return "iPhone 11 Pro"
            case "iPhone12,5":                              return "iPhone 11 Pro Max"
            case "iPad4,1", "iPad4,2", "iPad4,3":           return "iPad Air"
            case "iPad5,3", "iPad5,4":                      return "iPad Air 2"
            case "iPad6,11", "iPad6,12":                    return "iPad 5"
            case "iPad7,5", "iPad7,6":                      return "iPad 6"
            case "iPad4,4", "iPad4,5", "iPad4,6":           return "iPad Mini 2"
            case "iPad4,7", "iPad4,8", "iPad4,9":           return "iPad Mini 3"
            case "iPad5,1", "iPad5,2":                      return "iPad Mini 4"
            case "iPad6,3", "iPad6,4":                      return "iPad Pro 9.7 Inch"
            case "iPad6,7", "iPad6,8":                      return "iPad Pro 12.9 Inch"
            case "iPad7,1", "iPad7,2":                      return "iPad Pro 12.9 Inch 2nd Generation"
            case "iPad7,3", "iPad7,4":                      return "iPad Pro 10.5 Inch"
            case "iPad8,1", "iPad8,2", "iPad8,3", "iPad8,4": return "iPad Pro 11 Inch"
            case "iPad8,5", "iPad8,6", "iPad8,7", "iPad8,8": return "iPad Pro 12.9 Inch 3rd Generation"
            case "iPad11,3", "iPad11,4":                    return "iPad Air 3rd Generation"
                
            case "i386", "x86_64":                          return "Simulator \(mapToDevice(identifier: ProcessInfo().environment["SIMULATOR_MODEL_IDENTIFIER"] ?? "iOS"))"
            default:                                        return identifier
            }
            #endif
        }
        
        return mapToDevice(identifier: identifier)
    }()
    
    static let isSimulator: Bool = {
        if modelName.contains("Simulator") {
            return true
        } else {
            return false
        }
    }()
    
    static let isIPhoneX: Bool = {
        let context = LAContext()
        var error: NSError?
        if context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error) {
            if context.responds(to: #selector(getter: LAContext.biometryType)) {
                if context.biometryType == .faceID {
                    return true
                }
            }
        }
        return false
    }()
    
    @objc static let isIPad: Bool = {
        let modelName = UIDevice.modelName.replacingOccurrences(of: "Simulator", with: "")
        if modelName.contains("iPad") {
            return true
        } else {
            return false
        }
    }()
    
    @objc static let isSkinnyIPhone: Bool = {
        let modelName = UIDevice.modelName.replacingOccurrences(of: "Simulator ", with: "")
        let skinnyArray = ["iPod Touch 5", "iPod Touch 6", "iPhone 5", "iPhone 5c", "iPhone 5s", "iPhone 6", "iPhone 6s", "iPhone SE", "iPhone 7", "iPhone 8"]
        if skinnyArray.contains(modelName) {
            return true
        } else {
            return false
        }
    }()
    
}

extension UITextView {
    func addDoneButtonToKeyboard() {
        let doneToolbar: UIToolbar = UIToolbar(frame: CGRect(x: 0, y: 0, width: 300, height: 40))
        doneToolbar.barStyle = UIBarStyle.default
        
        let flexSpace = UIBarButtonItem(barButtonSystemItem: UIBarButtonItem.SystemItem.flexibleSpace, target: nil, action: nil)
        let done: UIBarButtonItem = UIBarButtonItem(title: "Done", style: UIBarButtonItem.Style.done, target: self, action: #selector(self.resignFirstResponder))
        var items = [UIBarButtonItem]()
        items.append(flexSpace)
        items.append(done)
        
        doneToolbar.items = items
        doneToolbar.sizeToFit()
        
        self.inputAccessoryView = doneToolbar
    }
    
    func setHTMLFromString(text: String) {
        let fontName = "Arial" //self.font!.fontName
        let fontSize = 14 //self.font!.pointSize+5.0
        let modifiedFont = NSString(format:"<span style=\"font-family: \(fontName); font-size: \(fontSize)\">%@</span>" as NSString, text)
        
        let attrStr = try! NSAttributedString(
            data: modifiedFont.data(using: String.Encoding.unicode.rawValue, allowLossyConversion: true)!,
            options: [NSAttributedString.DocumentReadingOptionKey.documentType:NSAttributedString.DocumentType.html, NSAttributedString.DocumentReadingOptionKey.characterEncoding: String.Encoding.utf8.rawValue],
            documentAttributes: nil)
        self.attributedText = attrStr
    }
    
    func width(_ font: UIFont) -> CGFloat {
        var rect: CGRect = frame //get frame of textView
        rect.size = (text?.size(withAttributes: [NSAttributedString.Key.font: UIFont(name: font.fontName, size: font.pointSize)!]))! //Calculate as per label font
        return rect.width
    }
    
    func updateTextFont() {
        if (text.isEmpty || CGSize().equalTo(bounds.size)) {
            return;
        }
        let textViewSize = frame.size
        let fixedWidth = textViewSize.width
        let expectSize = sizeThatFits(CGSize(width: fixedWidth, height: CGFloat(MAXFLOAT)))
        
        var expectFont = font
        if (expectSize.height > textViewSize.height) {
            while (sizeThatFits(CGSize(width: fixedWidth, height: CGFloat(MAXFLOAT))).height > textViewSize.height) {
                expectFont = font!.withSize(font!.pointSize - 1)
                font = expectFont
                if font!.pointSize < 17 { return }
            }
        } else {
            while (sizeThatFits(CGSize(width: fixedWidth, height: CGFloat(MAXFLOAT))).height < textViewSize.height) {
                expectFont = font
                font = font!.withSize(font!.pointSize + 1)
            }
            font = expectFont
            if font!.pointSize > 40 { return }
        }
    }
}

extension UIViewController {
    open func convertToFullScreen() {
        if #available(iOS 13.0, *) {
            if self.modalPresentationStyle == .automatic || self.modalPresentationStyle == .pageSheet {
                self.modalPresentationStyle = .fullScreen
            }
        }
    }

    func hideKeyboardWhenTappedAround() {
        let tap: UITapGestureRecognizer = UITapGestureRecognizer(target: self, action: #selector(UIViewController.dismissKeyboard))
        tap.cancelsTouchesInView = false
        view.addGestureRecognizer(tap)
    }

    @objc func dismissKeyboard() {
        view.endEditing(true)
    }

}

extension UISearchBar {
    func addDoneButtonToKeyboard(myAction:Selector?) {
        let doneToolbar: UIToolbar = UIToolbar(frame: CGRect(x: 0, y: 0, width: 300, height: 40))
        doneToolbar.barStyle = UIBarStyle.default
        
        let flexSpace = UIBarButtonItem(barButtonSystemItem: UIBarButtonItem.SystemItem.flexibleSpace, target: nil, action: nil)
        let done: UIBarButtonItem = UIBarButtonItem(title: "Done", style: UIBarButtonItem.Style.done, target: self, action: myAction)
        
        var items = [UIBarButtonItem]()
        items.append(flexSpace)
        items.append(done)
        
        doneToolbar.items = items
        doneToolbar.sizeToFit()
        
        self.inputAccessoryView = doneToolbar
    }
}

// too many issues to make it work uniformly -> compiler confused about optional state, yuck.
//extension UIViewController {
//    func keyWindow() -> UIWindow {
//        return (UIApplication.shared.connectedScenes
//                    .filter({$0.activationState == .foregroundActive})
//                    .map({$0 as? UIWindowScene})
//                    .compactMap({$0})
//                    .first?.windows
//                    .filter({$0.isKeyWindow}).first)!
//    }
//}
