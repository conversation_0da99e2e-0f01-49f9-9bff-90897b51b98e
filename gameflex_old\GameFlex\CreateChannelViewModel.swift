//
//  CreateChannelViewModel.swift
//  GameFlex
//
//  Created by <PERSON> on 11/22/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

struct CreateChannelViewModel {
    
    static let shared = CreateChannelViewModel()
    
    static var channelColors: [UIColor] = [UIColor(red: 250.0/255.0, green: 5.0/255.0, blue: 5.0/255.0, alpha: 1.0),
                                           UIColor(red: 250.0/255.0, green: 46.0/255.0, blue: 5.0/255.0, alpha: 1.0),
                                           UIColor(red: 250.0/255.0, green: 58.0/255.0, blue: 5.0/255.0, alpha: 1.0),
                                           UIColor(red: 250.0/255.0, green: 111.0/255.0, blue: 5.0/255.0, alpha: 1.0),
                                           UIColor(red: 250.0/255.0, green: 152.0/255.0, blue: 5.0/255.0, alpha: 1.0),
                                           UIColor(red: 250.0/255.0, green: 205.0/255.0, blue: 5.0/255.0, alpha: 1.0),
                                           UIColor(red: 250.0/255.0, green: 246.0/255.0, blue: 5.0/255.0, alpha: 1.0),
                                           UIColor(red: 221.0/255.0, green: 250.0/255.0, blue: 5.0/255.0, alpha: 1.0),
                                           UIColor(red: 173.0/255.0, green: 173.0/255.0, blue: 10.0/255.0, alpha: 1.0),
                                           UIColor(red: 114.0/255.0, green: 173.0/255.0, blue: 10.0/255.0, alpha: 1.0),
                                           UIColor(red: 73.0/255.0, green: 173.0/255.0, blue: 10.0/255.0, alpha: 1.0),
                                           UIColor(red: 10.0/255.0, green: 173.0/255.0, blue: 10.0/255.0, alpha: 1.0),
                                           UIColor(red: 10.0/255.0, green: 173.0/255.0, blue: 70.0/255.0, alpha: 1.0),
                                           UIColor(red: 10.0/255.0, green: 173.0/255.0, blue: 138.0/255.0, alpha: 1.0),
                                           UIColor(red: 0.0/255.0, green: 255.0/255.0, blue: 200.0/255.0, alpha: 1.0),
                                           UIColor(red: 0.0/255.0, green: 255.0/255.0, blue: 251.0/255.0, alpha: 1.0),
                                           UIColor(red: 0.0/255.0, green: 255.0/255.0, blue: 251.0/255.0, alpha: 1.0),
                                           UIColor(red: 0.0/255.0, green: 162.0/255.0, blue: 255.0/255.0, alpha: 1.0),
                                           UIColor(red: 0.0/255.0, green: 123.0/255.0, blue: 255.0/255.0, alpha: 1.0),
                                           UIColor(red: 0.0/255.0, green: 55.0/255.0, blue: 255.0/255.0, alpha: 1.0),
                                           UIColor(red: 0.0/255.0, green: 13.0/255.0, blue: 255.0/255.0, alpha: 1.0),
                                           UIColor(red: 89.0/255.0, green: 0.0/255.0, blue: 255.0/255.0, alpha: 1.0),
                                           UIColor(red: 136.0/255.0, green: 0.0/255.0, blue: 255.0/255.0, alpha: 1.0),
                                           UIColor(red: 200.0/255.0, green: 0.0/255.0, blue: 255.0/255.0, alpha: 1.0),
                                           UIColor(red: 238.0/255.0, green: 0.0/255.0, blue: 255.0/255.0, alpha: 1.0),
                                           UIColor(red: 255.0/255.0, green: 0.0/255.0, blue: 230.0/255.0, alpha: 1.0),
                                           UIColor(red: 255.0/255.0, green: 0.0/255.0, blue: 179.0/255.0, alpha: 1.0),
                                           UIColor(red: 255.0/255.0, green: 0.0/255.0, blue: 132.0/255.0, alpha: 1.0),
                                           UIColor(red: 255.0/255.0, green: 0.0/255.0, blue: 85.0/255.0, alpha: 1.0),
                                           UIColor(red: 255.0/255.0, green: 0.0/255.0, blue: 43.0/255.0, alpha: 1.0)]


    static var randomChannelColor: (UIColor) {
        if let color = channelColors.randomElement() {
            return color
        }
        return .blue
    }
    
    static var profileImage: UIImage? // used to pass images from PortraitViewController to CreateChannelViewController
    static var profileImageUrl: String?
    
    static var createChannelParameters: [ProfileUpdateDataType: String]? // used to store channel details for consumption in createChannel API
    
    static var invitees: [String] = []
}
