// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 51;
	objects = {

/* Begin PBXBuildFile section */
		597B3284347EDC1BAE84344D /* Pods_GameFlex.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6E8D4AB8A42E0FBBA4512BE3 /* Pods_GameFlex.framework */; };
		E8020D0124BFF50800C434B4 /* EditCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E8020D0024BFF50800C434B4 /* EditCollectionViewCell.xib */; };
		E80267742551EF59005637D0 /* LuckiestGuy-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E80267712551EF58005637D0 /* LuckiestGuy-Regular.ttf */; };
		E80267752551EF59005637D0 /* JollyLodger-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E80267722551EF59005637D0 /* JollyLodger-Regular.ttf */; };
		E80267762551EF59005637D0 /* ZillaSlabHighlight-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E80267732551EF59005637D0 /* ZillaSlabHighlight-Bold.ttf */; };
		E80D3324254DBCB1005B40E8 /* ReflexViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E80D3323254DBCB1005B40E8 /* ReflexViewController.swift */; };
		E80FF70A24F2DD2900599F9B /* SignUpViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E80FF70924F2DD2900599F9B /* SignUpViewController.swift */; };
		E80FF70C24F2F96100599F9B /* Utilities.swift in Sources */ = {isa = PBXBuildFile; fileRef = E80FF70B24F2F96100599F9B /* Utilities.swift */; };
		E823BA332591044000C09166 /* ChezLuiViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E823BA322591044000C09166 /* ChezLuiViewController.swift */; };
		E82E0B1525AA2C2A00A32223 /* FeedViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = E82E0B1425AA2C2A00A32223 /* FeedViewModel.swift */; };
		E82EC2222543CBD20061AF32 /* SideBarObject.swift in Sources */ = {isa = PBXBuildFile; fileRef = E82EC2212543CBD20061AF32 /* SideBarObject.swift */; };
		E82EC22725452E130061AF32 /* CommentTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E82EC22625452E130061AF32 /* CommentTableViewCell.swift */; };
		E82EC24B25452EE70061AF32 /* CommentTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E82EC24A25452EE70061AF32 /* CommentTableViewCell.xib */; };
		E82EC27625466E5A0061AF32 /* Comment.swift in Sources */ = {isa = PBXBuildFile; fileRef = E82EC27525466E5A0061AF32 /* Comment.swift */; };
		E82EC29A2546F8C60061AF32 /* CommentViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E82EC2992546F8C60061AF32 /* CommentViewController.swift */; };
		E831682B253697BD0086CA75 /* TextStickerView.xib in Resources */ = {isa = PBXBuildFile; fileRef = E831682A253697BD0086CA75 /* TextStickerView.xib */; };
		E8344C252539CDC2008B1A5F /* RandomNames.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8344C242539CDC2008B1A5F /* RandomNames.swift */; };
		E83D18F524F33AD0008A3B79 /* EmailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E83D18F424F33AD0008A3B79 /* EmailViewController.swift */; };
		E83D18F724F3500E008A3B79 /* LoginHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E83D18F624F3500E008A3B79 /* LoginHeaderView.swift */; };
		E83D18F924F3501F008A3B79 /* LoginHeaderView.xib in Resources */ = {isa = PBXBuildFile; fileRef = E83D18F824F3501F008A3B79 /* LoginHeaderView.xib */; };
		E83D18FB24F3E96A008A3B79 /* TextFieldTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E83D18FA24F3E96A008A3B79 /* TextFieldTableViewCell.swift */; };
		E83D18FD24F3EC10008A3B79 /* TextFieldTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E83D18FC24F3EC10008A3B79 /* TextFieldTableViewCell.xib */; };
		E83EC6B024C12BD000B73083 /* SliderCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E83EC6AF24C12BD000B73083 /* SliderCollectionViewCell.swift */; };
		E83EC6B224C12CC700B73083 /* SliderCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E83EC6B124C12CC700B73083 /* SliderCollectionViewCell.xib */; };
		E83EC6B424C26EC000B73083 /* FlareCategoryCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E83EC6B324C26EC000B73083 /* FlareCategoryCollectionViewCell.swift */; };
		E83EC6B624C26F4E00B73083 /* FlareCategoryCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E83EC6B524C26F4E00B73083 /* FlareCategoryCollectionViewCell.xib */; };
		E83EC6B824C340F500B73083 /* FlareHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E83EC6B724C340F500B73083 /* FlareHeaderView.swift */; };
		E83EC6BA24C342A400B73083 /* FlareHeaderView.xib in Resources */ = {isa = PBXBuildFile; fileRef = E83EC6B924C342A400B73083 /* FlareHeaderView.xib */; };
		E83EC6BC24C3764200B73083 /* FlareHeaderLabelCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E83EC6BB24C3764200B73083 /* FlareHeaderLabelCollectionViewCell.swift */; };
		E83EC6BE24C3778600B73083 /* FlareHeaderLabelCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E83EC6BD24C3778600B73083 /* FlareHeaderLabelCollectionViewCell.xib */; };
		E83EC6C124C3CF6600B73083 /* FlareEmptyCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E83EC6C024C3CF6600B73083 /* FlareEmptyCollectionViewCell.swift */; };
		E83EC6C324C3CF8A00B73083 /* FlareEmptyCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E83EC6C224C3CF8A00B73083 /* FlareEmptyCollectionViewCell.xib */; };
		E85ACB972530960000C8C9D0 /* FlexCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E85ACB962530960000C8C9D0 /* FlexCollectionViewCell.swift */; };
		E85ACB9C2530967000C8C9D0 /* FlexCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E85ACB9B2530967000C8C9D0 /* FlexCollectionViewCell.xib */; };
		E85ACBA125329C6F00C8C9D0 /* ProfileMiddleTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E85ACBA025329C6F00C8C9D0 /* ProfileMiddleTableViewCell.swift */; };
		E85ACBA62532A19400C8C9D0 /* ProfileMiddleTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E85ACBA52532A19400C8C9D0 /* ProfileMiddleTableViewCell.xib */; };
		E85ACBAB2532B13B00C8C9D0 /* ProfileBottomTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E85ACBAA2532B13B00C8C9D0 /* ProfileBottomTableViewCell.swift */; };
		E85ACBB02532B14E00C8C9D0 /* ProfileBottomTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E85ACBAF2532B14E00C8C9D0 /* ProfileBottomTableViewCell.xib */; };
		E85ACC042533A56600C8C9D0 /* GFChannelNavigationController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E85ACC032533A56600C8C9D0 /* GFChannelNavigationController.swift */; };
		E85ACC0E2533A60400C8C9D0 /* MyFeedViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E85ACC0D2533A60400C8C9D0 /* MyFeedViewController.swift */; };
		E85ACC382533D49B00C8C9D0 /* SideBarView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E85ACC372533D49B00C8C9D0 /* SideBarView.swift */; };
		E85ACC5B2533D51800C8C9D0 /* SideBarView.xib in Resources */ = {isa = PBXBuildFile; fileRef = E85ACC5A2533D51800C8C9D0 /* SideBarView.xib */; };
		E85ACCA22535371100C8C9D0 /* Reaction.swift in Sources */ = {isa = PBXBuildFile; fileRef = E85ACCA12535371100C8C9D0 /* Reaction.swift */; };
		E85ACCCE2535E00B00C8C9D0 /* WhatsNewViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E85ACCCD2535E00B00C8C9D0 /* WhatsNewViewController.swift */; };
		E85ACCFF25367A8200C8C9D0 /* TextStickerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E85ACCFD25367A8200C8C9D0 /* TextStickerView.swift */; };
		E862E8232540AC460054E94D /* HomeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E862E8222540AC460054E94D /* HomeViewController.swift */; };
		E86967672526C3DD003F5264 /* Channel.swift in Sources */ = {isa = PBXBuildFile; fileRef = E86967662526C3DD003F5264 /* Channel.swift */; };
		E869678A25275A4B003F5264 /* Flexter.swift in Sources */ = {isa = PBXBuildFile; fileRef = E869678925275A4B003F5264 /* Flexter.swift */; };
		E86AFBD125BCBA5400013CA4 /* ChannelViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E86AFBD025BCBA5400013CA4 /* ChannelViewController.swift */; };
		E86AFC3A25BE71D500013CA4 /* ImageViewTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E86AFC3925BE71D500013CA4 /* ImageViewTableViewCell.swift */; };
		E86AFC5F25BE729900013CA4 /* ImageViewTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E86AFC5E25BE729900013CA4 /* ImageViewTableViewCell.xib */; };
		E86AFC6525BE7F0B00013CA4 /* BreadcrumbChannelTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E86AFC6325BE7F0B00013CA4 /* BreadcrumbChannelTableViewCell.swift */; };
		E86AFC6625BE7F0B00013CA4 /* BreadcrumbChannelTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E86AFC6425BE7F0B00013CA4 /* BreadcrumbChannelTableViewCell.xib */; };
		E86AFCAE25C11CFB00013CA4 /* ChannelReviewViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E86AFCAD25C11CFB00013CA4 /* ChannelReviewViewController.swift */; };
		E86D86A9252C9B4A00257956 /* PSA-Character_Flex-Smoothflex.png in Resources */ = {isa = PBXBuildFile; fileRef = E86D86A8252C9B4A00257956 /* PSA-Character_Flex-Smoothflex.png */; };
		E86D86CC252CB8F200257956 /* EditProfileTextFieldTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E86D86CB252CB8F200257956 /* EditProfileTextFieldTableViewCell.swift */; };
		E86D86F4252CB93D00257956 /* EditProfileTextViewTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E86D86F3252CB93D00257956 /* EditProfileTextViewTableViewCell.swift */; };
		E86D86FC252CB95D00257956 /* EditProfileTextFieldTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E86D86FB252CB95D00257956 /* EditProfileTextFieldTableViewCell.xib */; };
		E86D8709252CB9A800257956 /* EditProfileTextViewTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E86D8708252CB9A800257956 /* EditProfileTextViewTableViewCell.xib */; };
		E86D870E252D12E700257956 /* EditProfileButtonTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E86D870D252D12E700257956 /* EditProfileButtonTableViewCell.xib */; };
		E86D8713252D12FD00257956 /* EditProfileButtonTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E86D8712252D12FD00257956 /* EditProfileButtonTableViewCell.swift */; };
		E86D871B252D1EAD00257956 /* EditProfileViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E86D871A252D1EAD00257956 /* EditProfileViewController.swift */; };
		E86FA7A42523F13C00C8EA09 /* ProfileTopTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E86FA7A32523F13C00C8EA09 /* ProfileTopTableViewCell.swift */; };
		E86FA7C72523F14C00C8EA09 /* ProfileTopTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E86FA7C62523F14C00C8EA09 /* ProfileTopTableViewCell.xib */; };
		E86FBEB9256B3D7E004A18D6 /* CreateChannelPictureTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E86FBEB7256B3D7D004A18D6 /* CreateChannelPictureTableViewCell.xib */; };
		E86FBEBA256B3D7E004A18D6 /* CreateChannelPictureTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E86FBEB8256B3D7D004A18D6 /* CreateChannelPictureTableViewCell.swift */; };
		E86FBEBF256B4033004A18D6 /* CreateChannelViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = E86FBEBE256B4033004A18D6 /* CreateChannelViewModel.swift */; };
		E86FBEE6256E12E7004A18D6 /* ChannelTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E86FBEE5256E12E7004A18D6 /* ChannelTableViewCell.swift */; };
		E86FBF0B256E12FD004A18D6 /* ChannelTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E86FBF0A256E12FD004A18D6 /* ChannelTableViewCell.xib */; };
		E86FBF1025717630004A18D6 /* PSA-GameOver.jpg in Resources */ = {isa = PBXBuildFile; fileRef = E86FBF0F2571762F004A18D6 /* PSA-GameOver.jpg */; };
		E86FBF352571797A004A18D6 /* PSA-MakeMoreFlex.jpg in Resources */ = {isa = PBXBuildFile; fileRef = E86FBF342571797A004A18D6 /* PSA-MakeMoreFlex.jpg */; };
		E86FBF5D25717C72004A18D6 /* InviteViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E86FBF5C25717C72004A18D6 /* InviteViewController.swift */; };
		E86FBF8225732924004A18D6 /* ChannelDetail.swift in Sources */ = {isa = PBXBuildFile; fileRef = E86FBF8125732924004A18D6 /* ChannelDetail.swift */; };
		E880B6B225571A2A00CF5D83 /* FollowingTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E880B6B025571A2A00CF5D83 /* FollowingTableViewCell.swift */; };
		E880B6B325571A2A00CF5D83 /* FollowingTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E880B6B125571A2A00CF5D83 /* FollowingTableViewCell.xib */; };
		E880B70A2558F8CD00CF5D83 /* GFNotification.swift in Sources */ = {isa = PBXBuildFile; fileRef = E880B7092558F8CD00CF5D83 /* GFNotification.swift */; };
		E880B742255A39F300CF5D83 /* AlertTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E880B741255A39F300CF5D83 /* AlertTableViewCell.swift */; };
		E880B767255A3A0000CF5D83 /* AlertTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E880B766255A3A0000CF5D83 /* AlertTableViewCell.xib */; };
		E880B793255AFBB700CF5D83 /* GFSproketView2.swift in Sources */ = {isa = PBXBuildFile; fileRef = E880B791255AFBB700CF5D83 /* GFSproketView2.swift */; };
		E880B794255AFBB700CF5D83 /* GFSproketView2.xib in Resources */ = {isa = PBXBuildFile; fileRef = E880B792255AFBB700CF5D83 /* GFSproketView2.xib */; };
		E880B808255DF7C800CF5D83 /* OnboardingStateMachine.swift in Sources */ = {isa = PBXBuildFile; fileRef = E880B807255DF7C800CF5D83 /* OnboardingStateMachine.swift */; };
		E8895E1C25675B50000F5B7A /* RootViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8895E1B25675B4F000F5B7A /* RootViewController.swift */; };
		E88D59EC25ADD64000070477 /* BackgroundTasks.swift in Sources */ = {isa = PBXBuildFile; fileRef = E88D59EB25ADD64000070477 /* BackgroundTasks.swift */; };
		E88D5A3D25AE9D8E00070477 /* FlagTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E88D5A3C25AE9D8E00070477 /* FlagTableViewCell.swift */; };
		E88D5A4225AE9D9F00070477 /* FlagTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E88D5A4125AE9D9F00070477 /* FlagTableViewCell.xib */; };
		E88FB2432510E3E800F57306 /* AgeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E88FB2422510E3E800F57306 /* AgeViewController.swift */; };
		E88FB2452510E52E00F57306 /* MonthYearCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E88FB2442510E52E00F57306 /* MonthYearCollectionViewCell.swift */; };
		E88FB2472510E5A400F57306 /* MonthYearCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E88FB2462510E5A400F57306 /* MonthYearCollectionViewCell.xib */; };
		E890294E24B9213600C83F72 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = E890294D24B9213500C83F72 /* GoogleService-Info.plist */; };
		E890297024BCE72400C83F72 /* CameraViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = E890296F24BCE72400C83F72 /* CameraViewModel.swift */; };
		E890297224BCE9B400C83F72 /* CameraControlsCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E890297124BCE9B400C83F72 /* CameraControlsCollectionViewCell.swift */; };
		E890297424BCE9C700C83F72 /* CameraControlsCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E890297324BCE9C700C83F72 /* CameraControlsCollectionViewCell.xib */; };
		E890297624BD13F600C83F72 /* FilterCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E890297524BD13F600C83F72 /* FilterCollectionViewCell.swift */; };
		E890297824BD140B00C83F72 /* FilterCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E890297724BD140B00C83F72 /* FilterCollectionViewCell.xib */; };
		E890297B24BD3A2700C83F72 /* SelectedFilterView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E890297A24BD3A2700C83F72 /* SelectedFilterView.swift */; };
		E890297D24BE908C00C83F72 /* ImageEnhancementControlsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E890297C24BE908C00C83F72 /* ImageEnhancementControlsView.swift */; };
		E890297F24BE909D00C83F72 /* ImageEnhancementControlsView.xib in Resources */ = {isa = PBXBuildFile; fileRef = E890297E24BE909D00C83F72 /* ImageEnhancementControlsView.xib */; };
		E890298324BFE1EF00C83F72 /* EditCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E890298124BFE1EF00C83F72 /* EditCollectionViewCell.swift */; };
		E892A01924CB201A00037057 /* Flex.swift in Sources */ = {isa = PBXBuildFile; fileRef = E892A01824CB201A00037057 /* Flex.swift */; };
		E892A01B24CB206A00037057 /* User.swift in Sources */ = {isa = PBXBuildFile; fileRef = E892A01A24CB206A00037057 /* User.swift */; };
		E892A01E24CB20F300037057 /* StickerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E892A01D24CB20F300037057 /* StickerView.swift */; };
		E892A02924CDFA9B00037057 /* StickerView.xib in Resources */ = {isa = PBXBuildFile; fileRef = E892A02824CDFA9B00037057 /* StickerView.xib */; };
		E892A02C24CF0FB100037057 /* ColorCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E892A02B24CF0FB100037057 /* ColorCollectionViewCell.swift */; };
		E892A02E24CF0FC000037057 /* ColorCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E892A02D24CF0FC000037057 /* ColorCollectionViewCell.xib */; };
		E892A12424CF71DB00037057 /* StickerCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E892A12324CF71DB00037057 /* StickerCollectionViewCell.swift */; };
		E892A12624CF752E00037057 /* StickerCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E892A12524CF752E00037057 /* StickerCollectionViewCell.xib */; };
		E893723A24DB8AC10070CECA /* BugViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E893723924DB8AC10070CECA /* BugViewController.swift */; };
		E893723D24DC1F670070CECA /* GFNetworkServices.swift in Sources */ = {isa = PBXBuildFile; fileRef = E893723C24DC1F670070CECA /* GFNetworkServices.swift */; };
		E89FB4C92569FB1D0082925B /* FavoriteChannelTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E89FB4C82569FB1D0082925B /* FavoriteChannelTableViewCell.swift */; };
		E89FB4EE2569FBCC0082925B /* FavoriteChannelTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E89FB4ED2569FBCC0082925B /* FavoriteChannelTableViewCell.xib */; };
		E89FB4F3256A02EB0082925B /* FavoriteChannelCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E89FB4F2256A02EB0082925B /* FavoriteChannelCollectionViewCell.swift */; };
		E89FB4F8256A03EF0082925B /* FavoriteChannelCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E89FB4F7256A03EF0082925B /* FavoriteChannelCollectionViewCell.xib */; };
		E89FB500256B0D0E0082925B /* CreateChannelViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E89FB4FF256B0D0E0082925B /* CreateChannelViewController.swift */; };
		E8A2F36C25B61A9B0092BC8C /* ChannelDirectoryViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8A2F36B25B61A9B0092BC8C /* ChannelDirectoryViewController.swift */; };
		E8A2F39525B6F9310092BC8C /* ChannelDirectoryHeaderLabelCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8A2F39325B6F9310092BC8C /* ChannelDirectoryHeaderLabelCollectionViewCell.swift */; };
		E8A2F39625B6F9310092BC8C /* ChannelDirectoryHeaderLabelCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E8A2F39425B6F9310092BC8C /* ChannelDirectoryHeaderLabelCollectionViewCell.xib */; };
		E8A8D4612575F87D00126FC7 /* FlexChannelTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8A8D45F2575F87D00126FC7 /* FlexChannelTableViewCell.swift */; };
		E8A8D4622575F87D00126FC7 /* FlexChannelTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E8A8D4602575F87D00126FC7 /* FlexChannelTableViewCell.xib */; };
		E8A8D4AE2579188500126FC7 /* AlertButtonTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8A8D4AC2579188400126FC7 /* AlertButtonTableViewCell.swift */; };
		E8A8D4AF2579188500126FC7 /* AlertButtonTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E8A8D4AD2579188500126FC7 /* AlertButtonTableViewCell.xib */; };
		E8A8D6212587051F00126FC7 /* FeedObject.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8A8D6202587051F00126FC7 /* FeedObject.swift */; };
		E8B18D2C253BE1B4001DF5C3 /* FinalizeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8B18D2B253BE1B4001DF5C3 /* FinalizeViewController.swift */; };
		E8B18D31253BE515001DF5C3 /* FinalizeTopTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8B18D30253BE515001DF5C3 /* FinalizeTopTableViewCell.swift */; };
		E8B18D36253BE636001DF5C3 /* FinalizeTopTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E8B18D35253BE636001DF5C3 /* FinalizeTopTableViewCell.xib */; };
		E8B18D64253C65FF001DF5C3 /* HashTags.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8B18D63253C65FF001DF5C3 /* HashTags.swift */; };
		E8B18D8A253CF2F2001DF5C3 /* HashTagsTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8B18D89253CF2F2001DF5C3 /* HashTagsTableViewCell.swift */; };
		E8B18DAD253CF432001DF5C3 /* HashTagsTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E8B18DAC253CF432001DF5C3 /* HashTagsTableViewCell.xib */; };
		E8B18DD3253D1C0F001DF5C3 /* FinalizeViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8B18DD2253D1C0F001DF5C3 /* FinalizeViewModel.swift */; };
		E8B8526824F17829006E9530 /* ProfileViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8B8526724F17829006E9530 /* ProfileViewController.swift */; };
		E8BF8A2C25B0679000897F74 /* BreadcrumbTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8BF8A2A25B0679000897F74 /* BreadcrumbTableViewCell.swift */; };
		E8BF8A2D25B0679000897F74 /* BreadcrumbTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E8BF8A2B25B0679000897F74 /* BreadcrumbTableViewCell.xib */; };
		E8BF8A3225B080AE00897F74 /* CounterTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8BF8A3125B080AE00897F74 /* CounterTableViewCell.swift */; };
		E8BF8A3725B0811B00897F74 /* CounterTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E8BF8A3625B0811B00897F74 /* CounterTableViewCell.xib */; };
		E8BF8A3C25B0860E00897F74 /* CounterCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E8BF8A3B25B0860E00897F74 /* CounterCollectionViewCell.xib */; };
		E8BF8A4125B0861D00897F74 /* CounterCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8BF8A4025B0861D00897F74 /* CounterCollectionViewCell.swift */; };
		E8C2C5D7253003F300462ED7 /* SearchResult.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8C2C5D6253003F300462ED7 /* SearchResult.swift */; };
		E8C2C5FA2530866800462ED7 /* SearchResultTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8C2C5F92530866800462ED7 /* SearchResultTableViewCell.swift */; };
		E8C2C5FF253086BF00462ED7 /* SearchResultTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E8C2C5FE253086BF00462ED7 /* SearchResultTableViewCell.xib */; };
		E8C2C60525308BD900462ED7 /* GFSproketView.xib in Resources */ = {isa = PBXBuildFile; fileRef = E8C2C60325308BD800462ED7 /* GFSproketView.xib */; };
		E8C2C60625308BD900462ED7 /* GFSproketView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8C2C60425308BD800462ED7 /* GFSproketView.swift */; };
		E8C4258024B2B48D00958ED7 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8C4257F24B2B48D00958ED7 /* AppDelegate.swift */; };
		E8C4258424B2B48D00958ED7 /* TryingToFlexViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8C4258324B2B48D00958ED7 /* TryingToFlexViewController.swift */; };
		E8C4258724B2B48D00958ED7 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = E8C4258524B2B48D00958ED7 /* Main.storyboard */; };
		E8C4258924B2B48E00958ED7 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = E8C4258824B2B48E00958ED7 /* Assets.xcassets */; };
		E8C4258C24B2B48E00958ED7 /* Launch.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = E8C4258A24B2B48E00958ED7 /* Launch.storyboard */; };
		E8C4259724B2B48E00958ED7 /* GameFlexTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8C4259624B2B48E00958ED7 /* GameFlexTests.swift */; };
		E8C425A224B2B48E00958ED7 /* GameFlexUITests.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8C425A124B2B48E00958ED7 /* GameFlexUITests.swift */; };
		E8C425B024B2B7F600958ED7 /* Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8C425AF24B2B7F600958ED7 /* Extensions.swift */; };
		E8C425B224B2B83400958ED7 /* GFViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8C425B124B2B83400958ED7 /* GFViewController.swift */; };
		E8C425B424B2BFB200958ED7 /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = E8C425B324B2BFB200958ED7 /* Localizable.strings */; };
		E8C425B624B2C62000958ED7 /* GFDefaults.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8C425B524B2C62000958ED7 /* GFDefaults.swift */; };
		E8C425B824B2D0B600958ED7 /* CameraViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8C425B724B2D0B600958ED7 /* CameraViewController.swift */; };
		E8C425BF24B3FD2500958ED7 /* PreCameraViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8C425BE24B3FD2500958ED7 /* PreCameraViewController.swift */; };
		E8C425C224B6A8AF00958ED7 /* FlexManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8C425C124B6A8AF00958ED7 /* FlexManager.swift */; };
		E8C63B2225605EF5007112E9 /* GetFlexterNameViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8C63B2125605EF5007112E9 /* GetFlexterNameViewController.swift */; };
		E8C63B2A25606C77007112E9 /* EditProfilePictureTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8C63B2925606C77007112E9 /* EditProfilePictureTableViewCell.swift */; };
		E8C63B2F25606C90007112E9 /* EditProfilePictureTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E8C63B2E25606C90007112E9 /* EditProfilePictureTableViewCell.xib */; };
		E8C63B342560ABE3007112E9 /* PortraitViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8C63B332560ABE3007112E9 /* PortraitViewController.swift */; };
		E8CB9BEE24C681C9000085EA /* FontManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8CB9BED24C681C9000085EA /* FontManager.swift */; };
		E8CB9BF424C9C4B3000085EA /* FontCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8CB9BF324C9C4B3000085EA /* FontCollectionViewCell.swift */; };
		E8CB9BF624C9C55C000085EA /* FontCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E8CB9BF524C9C55C000085EA /* FontCollectionViewCell.xib */; };
		E8D8B133251FD4F6006E2067 /* QuickIntroViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8D8B132251FD4F6006E2067 /* QuickIntroViewController.swift */; };
		E8D8B13B251FDABF006E2067 /* PageViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8D8B13A251FDABF006E2067 /* PageViewController.swift */; };
		E8D8B1402520B042006E2067 /* swords.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = E8D8B13F2520B042006E2067 /* swords.mp4 */; };
		E8D8B1632520B170006E2067 /* FirstViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8D8B1622520B170006E2067 /* FirstViewController.swift */; };
		E8E3018624D382CA007B5CF7 /* FilterView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8E3018524D382CA007B5CF7 /* FilterView.swift */; };
		E8E301D224D65638007B5CF7 /* IMFellDoublePica-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E8E301A124D64E9D007B5CF7 /* IMFellDoublePica-Regular.ttf */; };
		E8E301D324D65638007B5CF7 /* IndieFlower-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E8E301A024D64E9D007B5CF7 /* IndieFlower-Regular.ttf */; };
		E8E301D524D65638007B5CF7 /* PermanentMarker-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E8E301A624D64E9E007B5CF7 /* PermanentMarker-Regular.ttf */; };
		E8E301DE24D65638007B5CF7 /* Roboto-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E8E3019624D64E9B007B5CF7 /* Roboto-Medium.ttf */; };
		E8E301E424D65638007B5CF7 /* PressStart2P-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E8916AF024D0390700A32DAE /* PressStart2P-Regular.ttf */; };
		E8E301EE24D739E1007B5CF7 /* FavsHeaderCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8E301ED24D739E1007B5CF7 /* FavsHeaderCollectionViewCell.swift */; };
		E8E301F024D73AB9007B5CF7 /* FavsHeaderCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E8E301EF24D73AB9007B5CF7 /* FavsHeaderCollectionViewCell.xib */; };
		E8EE8DFA24F511F6003C79BB /* LoginViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8EE8DF924F511F6003C79BB /* LoginViewController.swift */; };
		E8EE8DFE24FB0B4F003C79BB /* StoreKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = E8EE8DFD24FB0B4F003C79BB /* StoreKit.framework */; };
		E8F91161250421FF002C7394 /* GFNavigationController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8F91160250421FF002C7394 /* GFNavigationController.swift */; };
		E8F911632504450A002C7394 /* Temp2ViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8F911622504450A002C7394 /* Temp2ViewController.swift */; };
		E8F91165250488A8002C7394 /* DrawView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8F91164250488A8002C7394 /* DrawView.swift */; };
		E8F911672505AB7F002C7394 /* GFFirebaseNetworking.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8F911662505AB7F002C7394 /* GFFirebaseNetworking.swift */; };
		E8F911692505BE1E002C7394 /* AppInfo.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8F911682505BE1E002C7394 /* AppInfo.swift */; };
		E8F9116B250695FD002C7394 /* GFError.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8F9116A250695FD002C7394 /* GFError.swift */; };
		E8F9116D2507112A002C7394 /* GFSpinnerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8F9116C2507112A002C7394 /* GFSpinnerView.swift */; };
		E8F9116F25086B5B002C7394 /* MainViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8F9116E25086B5B002C7394 /* MainViewController.swift */; };
		E8F9117225086BE4002C7394 /* GFChannelViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8F9117025086BE4002C7394 /* GFChannelViewController.swift */; };
		E8F9117525094D1D002C7394 /* LandingPageViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8F9117425094D1D002C7394 /* LandingPageViewController.swift */; };
		E8F9117725096A46002C7394 /* AlertViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8F9117625096A46002C7394 /* AlertViewController.swift */; };
		E8F9117925096AAA002C7394 /* SearchViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8F9117825096AAA002C7394 /* SearchViewController.swift */; };
		E8F9117B2509BEFF002C7394 /* SafeAreaFixTabBar.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8F9117A2509BEFF002C7394 /* SafeAreaFixTabBar.swift */; };
		E8F91181250AF5B2002C7394 /* FlexTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8F91180250AF5B2002C7394 /* FlexTableViewCell.swift */; };
		E8F91183250AF5C4002C7394 /* FlexTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = E8F91182250AF5C4002C7394 /* FlexTableViewCell.xib */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		E8C4259324B2B48E00958ED7 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = E8C4257424B2B48D00958ED7 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = E8C4257B24B2B48D00958ED7;
			remoteInfo = GameFlex;
		};
		E8C4259E24B2B48E00958ED7 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = E8C4257424B2B48D00958ED7 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = E8C4257B24B2B48D00958ED7;
			remoteInfo = GameFlex;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		6E8D4AB8A42E0FBBA4512BE3 /* Pods_GameFlex.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_GameFlex.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		9D3DDA13B2204D2426E1DD17 /* Pods-GameFlex.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-GameFlex.debug.xcconfig"; path = "Target Support Files/Pods-GameFlex/Pods-GameFlex.debug.xcconfig"; sourceTree = "<group>"; };
		A94FBC2E7AE6237DAAD041A5 /* Pods-GameFlex.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-GameFlex.release.xcconfig"; path = "Target Support Files/Pods-GameFlex/Pods-GameFlex.release.xcconfig"; sourceTree = "<group>"; };
		E8020D0024BFF50800C434B4 /* EditCollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = EditCollectionViewCell.xib; sourceTree = "<group>"; };
		E80267712551EF58005637D0 /* LuckiestGuy-Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "LuckiestGuy-Regular.ttf"; sourceTree = "<group>"; };
		E80267722551EF59005637D0 /* JollyLodger-Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "JollyLodger-Regular.ttf"; sourceTree = "<group>"; };
		E80267732551EF59005637D0 /* ZillaSlabHighlight-Bold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "ZillaSlabHighlight-Bold.ttf"; sourceTree = "<group>"; };
		E80D3323254DBCB1005B40E8 /* ReflexViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ReflexViewController.swift; sourceTree = "<group>"; };
		E80FF70924F2DD2900599F9B /* SignUpViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SignUpViewController.swift; sourceTree = "<group>"; };
		E80FF70B24F2F96100599F9B /* Utilities.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Utilities.swift; sourceTree = "<group>"; };
		E823BA322591044000C09166 /* ChezLuiViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChezLuiViewController.swift; sourceTree = "<group>"; };
		E82E0B1425AA2C2A00A32223 /* FeedViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FeedViewModel.swift; sourceTree = "<group>"; };
		E82EC2212543CBD20061AF32 /* SideBarObject.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SideBarObject.swift; sourceTree = "<group>"; };
		E82EC22625452E130061AF32 /* CommentTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CommentTableViewCell.swift; sourceTree = "<group>"; };
		E82EC24A25452EE70061AF32 /* CommentTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = CommentTableViewCell.xib; sourceTree = "<group>"; };
		E82EC27525466E5A0061AF32 /* Comment.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Comment.swift; sourceTree = "<group>"; };
		E82EC2992546F8C60061AF32 /* CommentViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CommentViewController.swift; sourceTree = "<group>"; };
		E831682A253697BD0086CA75 /* TextStickerView.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = TextStickerView.xib; sourceTree = "<group>"; };
		E8344C242539CDC2008B1A5F /* RandomNames.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RandomNames.swift; sourceTree = "<group>"; };
		E839E5B424EE0D81006E9D93 /* GameFlex.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = GameFlex.entitlements; sourceTree = "<group>"; };
		E83D18F424F33AD0008A3B79 /* EmailViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EmailViewController.swift; sourceTree = "<group>"; };
		E83D18F624F3500E008A3B79 /* LoginHeaderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoginHeaderView.swift; sourceTree = "<group>"; };
		E83D18F824F3501F008A3B79 /* LoginHeaderView.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = LoginHeaderView.xib; sourceTree = "<group>"; };
		E83D18FA24F3E96A008A3B79 /* TextFieldTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TextFieldTableViewCell.swift; sourceTree = "<group>"; };
		E83D18FC24F3EC10008A3B79 /* TextFieldTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = TextFieldTableViewCell.xib; sourceTree = "<group>"; };
		E83EC6AF24C12BD000B73083 /* SliderCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SliderCollectionViewCell.swift; sourceTree = "<group>"; };
		E83EC6B124C12CC700B73083 /* SliderCollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = SliderCollectionViewCell.xib; sourceTree = "<group>"; };
		E83EC6B324C26EC000B73083 /* FlareCategoryCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FlareCategoryCollectionViewCell.swift; sourceTree = "<group>"; };
		E83EC6B524C26F4E00B73083 /* FlareCategoryCollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = FlareCategoryCollectionViewCell.xib; sourceTree = "<group>"; };
		E83EC6B724C340F500B73083 /* FlareHeaderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FlareHeaderView.swift; sourceTree = "<group>"; };
		E83EC6B924C342A400B73083 /* FlareHeaderView.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = FlareHeaderView.xib; sourceTree = "<group>"; };
		E83EC6BB24C3764200B73083 /* FlareHeaderLabelCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FlareHeaderLabelCollectionViewCell.swift; sourceTree = "<group>"; };
		E83EC6BD24C3778600B73083 /* FlareHeaderLabelCollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = FlareHeaderLabelCollectionViewCell.xib; sourceTree = "<group>"; };
		E83EC6C024C3CF6600B73083 /* FlareEmptyCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FlareEmptyCollectionViewCell.swift; sourceTree = "<group>"; };
		E83EC6C224C3CF8A00B73083 /* FlareEmptyCollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = FlareEmptyCollectionViewCell.xib; sourceTree = "<group>"; };
		E85ACB962530960000C8C9D0 /* FlexCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FlexCollectionViewCell.swift; sourceTree = "<group>"; };
		E85ACB9B2530967000C8C9D0 /* FlexCollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = FlexCollectionViewCell.xib; sourceTree = "<group>"; };
		E85ACBA025329C6F00C8C9D0 /* ProfileMiddleTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProfileMiddleTableViewCell.swift; sourceTree = "<group>"; };
		E85ACBA52532A19400C8C9D0 /* ProfileMiddleTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = ProfileMiddleTableViewCell.xib; sourceTree = "<group>"; };
		E85ACBAA2532B13B00C8C9D0 /* ProfileBottomTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProfileBottomTableViewCell.swift; sourceTree = "<group>"; };
		E85ACBAF2532B14E00C8C9D0 /* ProfileBottomTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = ProfileBottomTableViewCell.xib; sourceTree = "<group>"; };
		E85ACC032533A56600C8C9D0 /* GFChannelNavigationController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GFChannelNavigationController.swift; sourceTree = "<group>"; };
		E85ACC0D2533A60400C8C9D0 /* MyFeedViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MyFeedViewController.swift; sourceTree = "<group>"; };
		E85ACC372533D49B00C8C9D0 /* SideBarView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SideBarView.swift; sourceTree = "<group>"; };
		E85ACC5A2533D51800C8C9D0 /* SideBarView.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = SideBarView.xib; sourceTree = "<group>"; };
		E85ACCA12535371100C8C9D0 /* Reaction.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Reaction.swift; sourceTree = "<group>"; };
		E85ACCCD2535E00B00C8C9D0 /* WhatsNewViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WhatsNewViewController.swift; sourceTree = "<group>"; };
		E85ACCFD25367A8200C8C9D0 /* TextStickerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TextStickerView.swift; sourceTree = "<group>"; };
		E862E8222540AC460054E94D /* HomeViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomeViewController.swift; sourceTree = "<group>"; };
		E86967662526C3DD003F5264 /* Channel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Channel.swift; sourceTree = "<group>"; };
		E869678925275A4B003F5264 /* Flexter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Flexter.swift; sourceTree = "<group>"; };
		E86AFBD025BCBA5400013CA4 /* ChannelViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChannelViewController.swift; sourceTree = "<group>"; };
		E86AFC3925BE71D500013CA4 /* ImageViewTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageViewTableViewCell.swift; sourceTree = "<group>"; };
		E86AFC5E25BE729900013CA4 /* ImageViewTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = ImageViewTableViewCell.xib; sourceTree = "<group>"; };
		E86AFC6325BE7F0B00013CA4 /* BreadcrumbChannelTableViewCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BreadcrumbChannelTableViewCell.swift; sourceTree = "<group>"; };
		E86AFC6425BE7F0B00013CA4 /* BreadcrumbChannelTableViewCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = BreadcrumbChannelTableViewCell.xib; sourceTree = "<group>"; };
		E86AFCAD25C11CFB00013CA4 /* ChannelReviewViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChannelReviewViewController.swift; sourceTree = "<group>"; };
		E86D86A8252C9B4A00257956 /* PSA-Character_Flex-Smoothflex.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "PSA-Character_Flex-Smoothflex.png"; sourceTree = "<group>"; };
		E86D86CB252CB8F200257956 /* EditProfileTextFieldTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EditProfileTextFieldTableViewCell.swift; sourceTree = "<group>"; };
		E86D86F3252CB93D00257956 /* EditProfileTextViewTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EditProfileTextViewTableViewCell.swift; sourceTree = "<group>"; };
		E86D86FB252CB95D00257956 /* EditProfileTextFieldTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = EditProfileTextFieldTableViewCell.xib; sourceTree = "<group>"; };
		E86D8708252CB9A800257956 /* EditProfileTextViewTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = EditProfileTextViewTableViewCell.xib; sourceTree = "<group>"; };
		E86D870D252D12E700257956 /* EditProfileButtonTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = EditProfileButtonTableViewCell.xib; sourceTree = "<group>"; };
		E86D8712252D12FD00257956 /* EditProfileButtonTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EditProfileButtonTableViewCell.swift; sourceTree = "<group>"; };
		E86D871A252D1EAD00257956 /* EditProfileViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EditProfileViewController.swift; sourceTree = "<group>"; };
		E86FA7A32523F13C00C8EA09 /* ProfileTopTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProfileTopTableViewCell.swift; sourceTree = "<group>"; };
		E86FA7C62523F14C00C8EA09 /* ProfileTopTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = ProfileTopTableViewCell.xib; sourceTree = "<group>"; };
		E86FBEB7256B3D7D004A18D6 /* CreateChannelPictureTableViewCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = CreateChannelPictureTableViewCell.xib; sourceTree = "<group>"; };
		E86FBEB8256B3D7D004A18D6 /* CreateChannelPictureTableViewCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CreateChannelPictureTableViewCell.swift; sourceTree = "<group>"; };
		E86FBEBE256B4033004A18D6 /* CreateChannelViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CreateChannelViewModel.swift; sourceTree = "<group>"; };
		E86FBEE5256E12E7004A18D6 /* ChannelTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChannelTableViewCell.swift; sourceTree = "<group>"; };
		E86FBF0A256E12FD004A18D6 /* ChannelTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = ChannelTableViewCell.xib; sourceTree = "<group>"; };
		E86FBF0F2571762F004A18D6 /* PSA-GameOver.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = "PSA-GameOver.jpg"; sourceTree = "<group>"; };
		E86FBF342571797A004A18D6 /* PSA-MakeMoreFlex.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = "PSA-MakeMoreFlex.jpg"; sourceTree = "<group>"; };
		E86FBF5C25717C72004A18D6 /* InviteViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InviteViewController.swift; sourceTree = "<group>"; };
		E86FBF8125732924004A18D6 /* ChannelDetail.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChannelDetail.swift; sourceTree = "<group>"; };
		E880B6B025571A2A00CF5D83 /* FollowingTableViewCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FollowingTableViewCell.swift; sourceTree = "<group>"; };
		E880B6B125571A2A00CF5D83 /* FollowingTableViewCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = FollowingTableViewCell.xib; sourceTree = "<group>"; };
		E880B7092558F8CD00CF5D83 /* GFNotification.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GFNotification.swift; sourceTree = "<group>"; };
		E880B741255A39F300CF5D83 /* AlertTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AlertTableViewCell.swift; sourceTree = "<group>"; };
		E880B766255A3A0000CF5D83 /* AlertTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = AlertTableViewCell.xib; sourceTree = "<group>"; };
		E880B791255AFBB700CF5D83 /* GFSproketView2.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GFSproketView2.swift; sourceTree = "<group>"; };
		E880B792255AFBB700CF5D83 /* GFSproketView2.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = GFSproketView2.xib; sourceTree = "<group>"; };
		E880B807255DF7C800CF5D83 /* OnboardingStateMachine.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OnboardingStateMachine.swift; sourceTree = "<group>"; };
		E8895E1B25675B4F000F5B7A /* RootViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RootViewController.swift; sourceTree = "<group>"; };
		E88D59EB25ADD64000070477 /* BackgroundTasks.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BackgroundTasks.swift; sourceTree = "<group>"; };
		E88D5A3C25AE9D8E00070477 /* FlagTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FlagTableViewCell.swift; sourceTree = "<group>"; };
		E88D5A4125AE9D9F00070477 /* FlagTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = FlagTableViewCell.xib; sourceTree = "<group>"; };
		E88FB2422510E3E800F57306 /* AgeViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AgeViewController.swift; sourceTree = "<group>"; };
		E88FB2442510E52E00F57306 /* MonthYearCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MonthYearCollectionViewCell.swift; sourceTree = "<group>"; };
		E88FB2462510E5A400F57306 /* MonthYearCollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = MonthYearCollectionViewCell.xib; sourceTree = "<group>"; };
		E890294D24B9213500C83F72 /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		E890296F24BCE72400C83F72 /* CameraViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CameraViewModel.swift; sourceTree = "<group>"; };
		E890297124BCE9B400C83F72 /* CameraControlsCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CameraControlsCollectionViewCell.swift; sourceTree = "<group>"; };
		E890297324BCE9C700C83F72 /* CameraControlsCollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = CameraControlsCollectionViewCell.xib; sourceTree = "<group>"; };
		E890297524BD13F600C83F72 /* FilterCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FilterCollectionViewCell.swift; sourceTree = "<group>"; };
		E890297724BD140B00C83F72 /* FilterCollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = FilterCollectionViewCell.xib; sourceTree = "<group>"; };
		E890297A24BD3A2700C83F72 /* SelectedFilterView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SelectedFilterView.swift; sourceTree = "<group>"; };
		E890297C24BE908C00C83F72 /* ImageEnhancementControlsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageEnhancementControlsView.swift; sourceTree = "<group>"; };
		E890297E24BE909D00C83F72 /* ImageEnhancementControlsView.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = ImageEnhancementControlsView.xib; sourceTree = "<group>"; };
		E890298124BFE1EF00C83F72 /* EditCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EditCollectionViewCell.swift; sourceTree = "<group>"; };
		E8916AF024D0390700A32DAE /* PressStart2P-Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "PressStart2P-Regular.ttf"; sourceTree = "<group>"; };
		E892A01824CB201A00037057 /* Flex.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Flex.swift; sourceTree = "<group>"; };
		E892A01A24CB206A00037057 /* User.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = User.swift; sourceTree = "<group>"; };
		E892A01D24CB20F300037057 /* StickerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StickerView.swift; sourceTree = "<group>"; };
		E892A02824CDFA9B00037057 /* StickerView.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = StickerView.xib; sourceTree = "<group>"; };
		E892A02B24CF0FB100037057 /* ColorCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ColorCollectionViewCell.swift; sourceTree = "<group>"; };
		E892A02D24CF0FC000037057 /* ColorCollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = ColorCollectionViewCell.xib; sourceTree = "<group>"; };
		E892A12324CF71DB00037057 /* StickerCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StickerCollectionViewCell.swift; sourceTree = "<group>"; };
		E892A12524CF752E00037057 /* StickerCollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = StickerCollectionViewCell.xib; sourceTree = "<group>"; };
		E893723924DB8AC10070CECA /* BugViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BugViewController.swift; sourceTree = "<group>"; };
		E893723C24DC1F670070CECA /* GFNetworkServices.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GFNetworkServices.swift; sourceTree = "<group>"; };
		E89FB4C82569FB1D0082925B /* FavoriteChannelTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FavoriteChannelTableViewCell.swift; sourceTree = "<group>"; };
		E89FB4ED2569FBCC0082925B /* FavoriteChannelTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = FavoriteChannelTableViewCell.xib; sourceTree = "<group>"; };
		E89FB4F2256A02EB0082925B /* FavoriteChannelCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FavoriteChannelCollectionViewCell.swift; sourceTree = "<group>"; };
		E89FB4F7256A03EF0082925B /* FavoriteChannelCollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = FavoriteChannelCollectionViewCell.xib; sourceTree = "<group>"; };
		E89FB4FF256B0D0E0082925B /* CreateChannelViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CreateChannelViewController.swift; sourceTree = "<group>"; };
		E8A2F36B25B61A9B0092BC8C /* ChannelDirectoryViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChannelDirectoryViewController.swift; sourceTree = "<group>"; };
		E8A2F39325B6F9310092BC8C /* ChannelDirectoryHeaderLabelCollectionViewCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ChannelDirectoryHeaderLabelCollectionViewCell.swift; sourceTree = "<group>"; };
		E8A2F39425B6F9310092BC8C /* ChannelDirectoryHeaderLabelCollectionViewCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = ChannelDirectoryHeaderLabelCollectionViewCell.xib; sourceTree = "<group>"; };
		E8A8D45F2575F87D00126FC7 /* FlexChannelTableViewCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FlexChannelTableViewCell.swift; sourceTree = "<group>"; };
		E8A8D4602575F87D00126FC7 /* FlexChannelTableViewCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = FlexChannelTableViewCell.xib; sourceTree = "<group>"; };
		E8A8D4AC2579188400126FC7 /* AlertButtonTableViewCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AlertButtonTableViewCell.swift; sourceTree = "<group>"; };
		E8A8D4AD2579188500126FC7 /* AlertButtonTableViewCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = AlertButtonTableViewCell.xib; sourceTree = "<group>"; };
		E8A8D6202587051F00126FC7 /* FeedObject.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FeedObject.swift; sourceTree = "<group>"; };
		E8B18D2B253BE1B4001DF5C3 /* FinalizeViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FinalizeViewController.swift; sourceTree = "<group>"; };
		E8B18D30253BE515001DF5C3 /* FinalizeTopTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FinalizeTopTableViewCell.swift; sourceTree = "<group>"; };
		E8B18D35253BE636001DF5C3 /* FinalizeTopTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = FinalizeTopTableViewCell.xib; sourceTree = "<group>"; };
		E8B18D63253C65FF001DF5C3 /* HashTags.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HashTags.swift; sourceTree = "<group>"; };
		E8B18D89253CF2F2001DF5C3 /* HashTagsTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HashTagsTableViewCell.swift; sourceTree = "<group>"; };
		E8B18DAC253CF432001DF5C3 /* HashTagsTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = HashTagsTableViewCell.xib; sourceTree = "<group>"; };
		E8B18DD2253D1C0F001DF5C3 /* FinalizeViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FinalizeViewModel.swift; sourceTree = "<group>"; };
		E8B8526724F17829006E9530 /* ProfileViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProfileViewController.swift; sourceTree = "<group>"; };
		E8BF8A2A25B0679000897F74 /* BreadcrumbTableViewCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BreadcrumbTableViewCell.swift; sourceTree = "<group>"; };
		E8BF8A2B25B0679000897F74 /* BreadcrumbTableViewCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = BreadcrumbTableViewCell.xib; sourceTree = "<group>"; };
		E8BF8A3125B080AE00897F74 /* CounterTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CounterTableViewCell.swift; sourceTree = "<group>"; };
		E8BF8A3625B0811B00897F74 /* CounterTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = CounterTableViewCell.xib; sourceTree = "<group>"; };
		E8BF8A3B25B0860E00897F74 /* CounterCollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = CounterCollectionViewCell.xib; sourceTree = "<group>"; };
		E8BF8A4025B0861D00897F74 /* CounterCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CounterCollectionViewCell.swift; sourceTree = "<group>"; };
		E8C2C5D6253003F300462ED7 /* SearchResult.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SearchResult.swift; sourceTree = "<group>"; };
		E8C2C5F92530866800462ED7 /* SearchResultTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SearchResultTableViewCell.swift; sourceTree = "<group>"; };
		E8C2C5FE253086BF00462ED7 /* SearchResultTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = SearchResultTableViewCell.xib; sourceTree = "<group>"; };
		E8C2C60325308BD800462ED7 /* GFSproketView.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = GFSproketView.xib; sourceTree = "<group>"; };
		E8C2C60425308BD800462ED7 /* GFSproketView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GFSproketView.swift; sourceTree = "<group>"; };
		E8C4257C24B2B48D00958ED7 /* GameFlex.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = GameFlex.app; sourceTree = BUILT_PRODUCTS_DIR; };
		E8C4257F24B2B48D00958ED7 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		E8C4258324B2B48D00958ED7 /* TryingToFlexViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TryingToFlexViewController.swift; sourceTree = "<group>"; };
		E8C4258624B2B48D00958ED7 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		E8C4258824B2B48E00958ED7 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		E8C4258B24B2B48E00958ED7 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Launch.storyboard; sourceTree = "<group>"; };
		E8C4258D24B2B48E00958ED7 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		E8C4259224B2B48E00958ED7 /* GameFlexTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = GameFlexTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		E8C4259624B2B48E00958ED7 /* GameFlexTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GameFlexTests.swift; sourceTree = "<group>"; };
		E8C4259824B2B48E00958ED7 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		E8C4259D24B2B48E00958ED7 /* GameFlexUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = GameFlexUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		E8C425A124B2B48E00958ED7 /* GameFlexUITests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GameFlexUITests.swift; sourceTree = "<group>"; };
		E8C425A324B2B48E00958ED7 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		E8C425AF24B2B7F600958ED7 /* Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Extensions.swift; sourceTree = "<group>"; };
		E8C425B124B2B83400958ED7 /* GFViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GFViewController.swift; sourceTree = "<group>"; };
		E8C425B324B2BFB200958ED7 /* Localizable.strings */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; path = Localizable.strings; sourceTree = "<group>"; };
		E8C425B524B2C62000958ED7 /* GFDefaults.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GFDefaults.swift; sourceTree = "<group>"; };
		E8C425B724B2D0B600958ED7 /* CameraViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CameraViewController.swift; sourceTree = "<group>"; };
		E8C425BE24B3FD2500958ED7 /* PreCameraViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PreCameraViewController.swift; sourceTree = "<group>"; };
		E8C425C124B6A8AF00958ED7 /* FlexManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FlexManager.swift; sourceTree = "<group>"; };
		E8C63B2125605EF5007112E9 /* GetFlexterNameViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GetFlexterNameViewController.swift; sourceTree = "<group>"; };
		E8C63B2925606C77007112E9 /* EditProfilePictureTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EditProfilePictureTableViewCell.swift; sourceTree = "<group>"; };
		E8C63B2E25606C90007112E9 /* EditProfilePictureTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = EditProfilePictureTableViewCell.xib; sourceTree = "<group>"; };
		E8C63B332560ABE3007112E9 /* PortraitViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PortraitViewController.swift; sourceTree = "<group>"; };
		E8CB9BED24C681C9000085EA /* FontManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FontManager.swift; sourceTree = "<group>"; };
		E8CB9BF324C9C4B3000085EA /* FontCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FontCollectionViewCell.swift; sourceTree = "<group>"; };
		E8CB9BF524C9C55C000085EA /* FontCollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = FontCollectionViewCell.xib; sourceTree = "<group>"; };
		E8D8B132251FD4F6006E2067 /* QuickIntroViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QuickIntroViewController.swift; sourceTree = "<group>"; };
		E8D8B13A251FDABF006E2067 /* PageViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PageViewController.swift; sourceTree = "<group>"; };
		E8D8B13F2520B042006E2067 /* swords.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = swords.mp4; sourceTree = "<group>"; };
		E8D8B1622520B170006E2067 /* FirstViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FirstViewController.swift; sourceTree = "<group>"; };
		E8E3018524D382CA007B5CF7 /* FilterView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FilterView.swift; sourceTree = "<group>"; };
		E8E3019624D64E9B007B5CF7 /* Roboto-Medium.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Roboto-Medium.ttf"; sourceTree = "<group>"; };
		E8E301A024D64E9D007B5CF7 /* IndieFlower-Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "IndieFlower-Regular.ttf"; sourceTree = "<group>"; };
		E8E301A124D64E9D007B5CF7 /* IMFellDoublePica-Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "IMFellDoublePica-Regular.ttf"; sourceTree = "<group>"; };
		E8E301A624D64E9E007B5CF7 /* PermanentMarker-Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "PermanentMarker-Regular.ttf"; sourceTree = "<group>"; };
		E8E301ED24D739E1007B5CF7 /* FavsHeaderCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FavsHeaderCollectionViewCell.swift; sourceTree = "<group>"; };
		E8E301EF24D73AB9007B5CF7 /* FavsHeaderCollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = FavsHeaderCollectionViewCell.xib; sourceTree = "<group>"; };
		E8EE8DF924F511F6003C79BB /* LoginViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoginViewController.swift; sourceTree = "<group>"; };
		E8EE8DFD24FB0B4F003C79BB /* StoreKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = StoreKit.framework; path = System/Library/Frameworks/StoreKit.framework; sourceTree = SDKROOT; };
		E8F91160250421FF002C7394 /* GFNavigationController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GFNavigationController.swift; sourceTree = "<group>"; };
		E8F911622504450A002C7394 /* Temp2ViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Temp2ViewController.swift; sourceTree = "<group>"; };
		E8F91164250488A8002C7394 /* DrawView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DrawView.swift; sourceTree = "<group>"; };
		E8F911662505AB7F002C7394 /* GFFirebaseNetworking.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GFFirebaseNetworking.swift; sourceTree = "<group>"; };
		E8F911682505BE1E002C7394 /* AppInfo.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppInfo.swift; sourceTree = "<group>"; };
		E8F9116A250695FD002C7394 /* GFError.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GFError.swift; sourceTree = "<group>"; };
		E8F9116C2507112A002C7394 /* GFSpinnerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GFSpinnerView.swift; sourceTree = "<group>"; };
		E8F9116E25086B5B002C7394 /* MainViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MainViewController.swift; sourceTree = "<group>"; };
		E8F9117125086BE4002C7394 /* Base */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; name = Base; path = Base.lproj/GFChannelViewController.swift; sourceTree = "<group>"; };
		E8F9117425094D1D002C7394 /* LandingPageViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LandingPageViewController.swift; sourceTree = "<group>"; };
		E8F9117625096A46002C7394 /* AlertViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AlertViewController.swift; sourceTree = "<group>"; };
		E8F9117825096AAA002C7394 /* SearchViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SearchViewController.swift; sourceTree = "<group>"; };
		E8F9117A2509BEFF002C7394 /* SafeAreaFixTabBar.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SafeAreaFixTabBar.swift; sourceTree = "<group>"; };
		E8F91180250AF5B2002C7394 /* FlexTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FlexTableViewCell.swift; sourceTree = "<group>"; };
		E8F91182250AF5C4002C7394 /* FlexTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = FlexTableViewCell.xib; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		E8C4257924B2B48D00958ED7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E8EE8DFE24FB0B4F003C79BB /* StoreKit.framework in Frameworks */,
				597B3284347EDC1BAE84344D /* Pods_GameFlex.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E8C4258F24B2B48E00958ED7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E8C4259A24B2B48E00958ED7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		8F15E448612BD579C95EBE40 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				E8EE8DFD24FB0B4F003C79BB /* StoreKit.framework */,
				6E8D4AB8A42E0FBBA4512BE3 /* Pods_GameFlex.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		E85ACC332533AAF300C8C9D0 /* Channels */ = {
			isa = PBXGroup;
			children = (
				E862E8222540AC460054E94D /* HomeViewController.swift */,
				E8F9116E25086B5B002C7394 /* MainViewController.swift */,
				E8A2F36B25B61A9B0092BC8C /* ChannelDirectoryViewController.swift */,
				E86AFBD025BCBA5400013CA4 /* ChannelViewController.swift */,
				E823BA322591044000C09166 /* ChezLuiViewController.swift */,
				E82EC2992546F8C60061AF32 /* CommentViewController.swift */,
				E82E0B1425AA2C2A00A32223 /* FeedViewModel.swift */,
				E85ACC0D2533A60400C8C9D0 /* MyFeedViewController.swift */,
				E80D3323254DBCB1005B40E8 /* ReflexViewController.swift */,
				E85ACCCD2535E00B00C8C9D0 /* WhatsNewViewController.swift */,
			);
			path = Channels;
			sourceTree = "<group>";
		};
		E890298024BFDB0300C83F72 /* Cells & Views */ = {
			isa = PBXGroup;
			children = (
				E8F9117F250AF40F002C7394 /* Views */,
				E8F9117E250AF392002C7394 /* CreateFlex */,
				E880B741255A39F300CF5D83 /* AlertTableViewCell.swift */,
				E880B766255A3A0000CF5D83 /* AlertTableViewCell.xib */,
				E8A8D4AC2579188400126FC7 /* AlertButtonTableViewCell.swift */,
				E8A8D4AD2579188500126FC7 /* AlertButtonTableViewCell.xib */,
				E86AFC6325BE7F0B00013CA4 /* BreadcrumbChannelTableViewCell.swift */,
				E86AFC6425BE7F0B00013CA4 /* BreadcrumbChannelTableViewCell.xib */,
				E8BF8A2A25B0679000897F74 /* BreadcrumbTableViewCell.swift */,
				E8BF8A2B25B0679000897F74 /* BreadcrumbTableViewCell.xib */,
				E8A2F39325B6F9310092BC8C /* ChannelDirectoryHeaderLabelCollectionViewCell.swift */,
				E8A2F39425B6F9310092BC8C /* ChannelDirectoryHeaderLabelCollectionViewCell.xib */,
				E86FBEE5256E12E7004A18D6 /* ChannelTableViewCell.swift */,
				E86FBF0A256E12FD004A18D6 /* ChannelTableViewCell.xib */,
				E82EC22625452E130061AF32 /* CommentTableViewCell.swift */,
				E82EC24A25452EE70061AF32 /* CommentTableViewCell.xib */,
				E8BF8A3125B080AE00897F74 /* CounterTableViewCell.swift */,
				E8BF8A3B25B0860E00897F74 /* CounterCollectionViewCell.xib */,
				E8BF8A4025B0861D00897F74 /* CounterCollectionViewCell.swift */,
				E8BF8A3625B0811B00897F74 /* CounterTableViewCell.xib */,
				E86FBEB8256B3D7D004A18D6 /* CreateChannelPictureTableViewCell.swift */,
				E86FBEB7256B3D7D004A18D6 /* CreateChannelPictureTableViewCell.xib */,
				E86D8712252D12FD00257956 /* EditProfileButtonTableViewCell.swift */,
				E86D870D252D12E700257956 /* EditProfileButtonTableViewCell.xib */,
				E8C63B2925606C77007112E9 /* EditProfilePictureTableViewCell.swift */,
				E8C63B2E25606C90007112E9 /* EditProfilePictureTableViewCell.xib */,
				E86D86CB252CB8F200257956 /* EditProfileTextFieldTableViewCell.swift */,
				E86D86FB252CB95D00257956 /* EditProfileTextFieldTableViewCell.xib */,
				E86D86F3252CB93D00257956 /* EditProfileTextViewTableViewCell.swift */,
				E86D8708252CB9A800257956 /* EditProfileTextViewTableViewCell.xib */,
				E89FB4F2256A02EB0082925B /* FavoriteChannelCollectionViewCell.swift */,
				E89FB4F7256A03EF0082925B /* FavoriteChannelCollectionViewCell.xib */,
				E89FB4C82569FB1D0082925B /* FavoriteChannelTableViewCell.swift */,
				E89FB4ED2569FBCC0082925B /* FavoriteChannelTableViewCell.xib */,
				E8B18D30253BE515001DF5C3 /* FinalizeTopTableViewCell.swift */,
				E8B18D35253BE636001DF5C3 /* FinalizeTopTableViewCell.xib */,
				E88D5A3C25AE9D8E00070477 /* FlagTableViewCell.swift */,
				E88D5A4125AE9D9F00070477 /* FlagTableViewCell.xib */,
				E85ACB962530960000C8C9D0 /* FlexCollectionViewCell.swift */,
				E85ACB9B2530967000C8C9D0 /* FlexCollectionViewCell.xib */,
				E8F91180250AF5B2002C7394 /* FlexTableViewCell.swift */,
				E8F91182250AF5C4002C7394 /* FlexTableViewCell.xib */,
				E8A8D45F2575F87D00126FC7 /* FlexChannelTableViewCell.swift */,
				E8A8D4602575F87D00126FC7 /* FlexChannelTableViewCell.xib */,
				E880B6B025571A2A00CF5D83 /* FollowingTableViewCell.swift */,
				E880B6B125571A2A00CF5D83 /* FollowingTableViewCell.xib */,
				E8B18D89253CF2F2001DF5C3 /* HashTagsTableViewCell.swift */,
				E8B18DAC253CF432001DF5C3 /* HashTagsTableViewCell.xib */,
				E86AFC3925BE71D500013CA4 /* ImageViewTableViewCell.swift */,
				E86AFC5E25BE729900013CA4 /* ImageViewTableViewCell.xib */,
				E88FB2442510E52E00F57306 /* MonthYearCollectionViewCell.swift */,
				E88FB2462510E5A400F57306 /* MonthYearCollectionViewCell.xib */,
				E86FA7A32523F13C00C8EA09 /* ProfileTopTableViewCell.swift */,
				E86FA7C62523F14C00C8EA09 /* ProfileTopTableViewCell.xib */,
				E85ACBA025329C6F00C8C9D0 /* ProfileMiddleTableViewCell.swift */,
				E85ACBA52532A19400C8C9D0 /* ProfileMiddleTableViewCell.xib */,
				E85ACBAA2532B13B00C8C9D0 /* ProfileBottomTableViewCell.swift */,
				E85ACBAF2532B14E00C8C9D0 /* ProfileBottomTableViewCell.xib */,
				E8C2C5F92530866800462ED7 /* SearchResultTableViewCell.swift */,
				E8C2C5FE253086BF00462ED7 /* SearchResultTableViewCell.xib */,
			);
			path = "Cells & Views";
			sourceTree = "<group>";
		};
		E892A01C24CB20D600037057 /* Models */ = {
			isa = PBXGroup;
			children = (
				E8F911682505BE1E002C7394 /* AppInfo.swift */,
				E86967662526C3DD003F5264 /* Channel.swift */,
				E86FBF8125732924004A18D6 /* ChannelDetail.swift */,
				E82EC27525466E5A0061AF32 /* Comment.swift */,
				E8A8D6202587051F00126FC7 /* FeedObject.swift */,
				E892A01824CB201A00037057 /* Flex.swift */,
				E869678925275A4B003F5264 /* Flexter.swift */,
				E8F9116A250695FD002C7394 /* GFError.swift */,
				E8B18D63253C65FF001DF5C3 /* HashTags.swift */,
				E880B7092558F8CD00CF5D83 /* GFNotification.swift */,
				E8344C242539CDC2008B1A5F /* RandomNames.swift */,
				E892A01A24CB206A00037057 /* User.swift */,
				E85ACCA12535371100C8C9D0 /* Reaction.swift */,
				E8C2C5D6253003F300462ED7 /* SearchResult.swift */,
				E82EC2212543CBD20061AF32 /* SideBarObject.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		E892A02724CD0C8600037057 /* Fonts Cells */ = {
			isa = PBXGroup;
			children = (
				E892A02B24CF0FB100037057 /* ColorCollectionViewCell.swift */,
				E892A02D24CF0FC000037057 /* ColorCollectionViewCell.xib */,
				E8CB9BF324C9C4B3000085EA /* FontCollectionViewCell.swift */,
				E8CB9BF524C9C55C000085EA /* FontCollectionViewCell.xib */,
			);
			path = "Fonts Cells";
			sourceTree = "<group>";
		};
		E892A12E24D0339200037057 /* Fonts */ = {
			isa = PBXGroup;
			children = (
				E8E301A124D64E9D007B5CF7 /* IMFellDoublePica-Regular.ttf */,
				E8E301A024D64E9D007B5CF7 /* IndieFlower-Regular.ttf */,
				E80267722551EF59005637D0 /* JollyLodger-Regular.ttf */,
				E80267712551EF58005637D0 /* LuckiestGuy-Regular.ttf */,
				E8E301A624D64E9E007B5CF7 /* PermanentMarker-Regular.ttf */,
				E8916AF024D0390700A32DAE /* PressStart2P-Regular.ttf */,
				E8E3019624D64E9B007B5CF7 /* Roboto-Medium.ttf */,
				E80267732551EF59005637D0 /* ZillaSlabHighlight-Bold.ttf */,
			);
			path = Fonts;
			sourceTree = "<group>";
		};
		E8C4257324B2B48D00958ED7 = {
			isa = PBXGroup;
			children = (
				E8C4257E24B2B48D00958ED7 /* GameFlex */,
				E8C4259524B2B48E00958ED7 /* GameFlexTests */,
				E8C425A024B2B48E00958ED7 /* GameFlexUITests */,
				E8C4257D24B2B48D00958ED7 /* Products */,
				FC68C9FD483EE67A59262BEA /* Pods */,
				8F15E448612BD579C95EBE40 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		E8C4257D24B2B48D00958ED7 /* Products */ = {
			isa = PBXGroup;
			children = (
				E8C4257C24B2B48D00958ED7 /* GameFlex.app */,
				E8C4259224B2B48E00958ED7 /* GameFlexTests.xctest */,
				E8C4259D24B2B48E00958ED7 /* GameFlexUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		E8C4257E24B2B48D00958ED7 /* GameFlex */ = {
			isa = PBXGroup;
			children = (
				E839E5B424EE0D81006E9D93 /* GameFlex.entitlements */,
				E8C4257F24B2B48D00958ED7 /* AppDelegate.swift */,
				E88D59EB25ADD64000070477 /* BackgroundTasks.swift */,
				E85ACC332533AAF300C8C9D0 /* Channels */,
				E8C425AF24B2B7F600958ED7 /* Extensions.swift */,
				E8C425B124B2B83400958ED7 /* GFViewController.swift */,
				E8F9117025086BE4002C7394 /* GFChannelViewController.swift */,
				E85ACC032533A56600C8C9D0 /* GFChannelNavigationController.swift */,
				E8C425B524B2C62000958ED7 /* GFDefaults.swift */,
				E8F91160250421FF002C7394 /* GFNavigationController.swift */,
				E893723C24DC1F670070CECA /* GFNetworkServices.swift */,
				E8F911662505AB7F002C7394 /* GFFirebaseNetworking.swift */,
				E8C425C124B6A8AF00958ED7 /* FlexManager.swift */,
				E8CB9BED24C681C9000085EA /* FontManager.swift */,
				E8F9117425094D1D002C7394 /* LandingPageViewController.swift */,
				E8C4258524B2B48D00958ED7 /* Main.storyboard */,
				E8F9117A2509BEFF002C7394 /* SafeAreaFixTabBar.swift */,
				E880B807255DF7C800CF5D83 /* OnboardingStateMachine.swift */,
				E8895E1B25675B4F000F5B7A /* RootViewController.swift */,
				E80FF70B24F2F96100599F9B /* Utilities.swift */,
				E892A01C24CB20D600037057 /* Models */,
				E88FB2422510E3E800F57306 /* AgeViewController.swift */,
				E8F9117625096A46002C7394 /* AlertViewController.swift */,
				E893723924DB8AC10070CECA /* BugViewController.swift */,
				E8C425B724B2D0B600958ED7 /* CameraViewController.swift */,
				E890296F24BCE72400C83F72 /* CameraViewModel.swift */,
				E86AFCAD25C11CFB00013CA4 /* ChannelReviewViewController.swift */,
				E89FB4FF256B0D0E0082925B /* CreateChannelViewController.swift */,
				E86FBEBE256B4033004A18D6 /* CreateChannelViewModel.swift */,
				E8C63B2125605EF5007112E9 /* GetFlexterNameViewController.swift */,
				E86D871A252D1EAD00257956 /* EditProfileViewController.swift */,
				E83D18F424F33AD0008A3B79 /* EmailViewController.swift */,
				E8D8B1622520B170006E2067 /* FirstViewController.swift */,
				E8B18D2B253BE1B4001DF5C3 /* FinalizeViewController.swift */,
				E8B18DD2253D1C0F001DF5C3 /* FinalizeViewModel.swift */,
				E86FBF5C25717C72004A18D6 /* InviteViewController.swift */,
				E8EE8DF924F511F6003C79BB /* LoginViewController.swift */,
				E8D8B13A251FDABF006E2067 /* PageViewController.swift */,
				E8C63B332560ABE3007112E9 /* PortraitViewController.swift */,
				E8C425BE24B3FD2500958ED7 /* PreCameraViewController.swift */,
				E8B8526724F17829006E9530 /* ProfileViewController.swift */,
				E8D8B132251FD4F6006E2067 /* QuickIntroViewController.swift */,
				E8F9117825096AAA002C7394 /* SearchViewController.swift */,
				E80FF70924F2DD2900599F9B /* SignUpViewController.swift */,
				E8C4258324B2B48D00958ED7 /* TryingToFlexViewController.swift */,
				E8F911622504450A002C7394 /* Temp2ViewController.swift */,
				E890298024BFDB0300C83F72 /* Cells & Views */,
				E8C4258824B2B48E00958ED7 /* Assets.xcassets */,
				E86D86A8252C9B4A00257956 /* PSA-Character_Flex-Smoothflex.png */,
				E86FBF0F2571762F004A18D6 /* PSA-GameOver.jpg */,
				E86FBF342571797A004A18D6 /* PSA-MakeMoreFlex.jpg */,
				E8C4258A24B2B48E00958ED7 /* Launch.storyboard */,
				E8C4258D24B2B48E00958ED7 /* Info.plist */,
				E8C425B324B2BFB200958ED7 /* Localizable.strings */,
				E8D8B13F2520B042006E2067 /* swords.mp4 */,
				E892A12E24D0339200037057 /* Fonts */,
				E890294D24B9213500C83F72 /* GoogleService-Info.plist */,
			);
			path = GameFlex;
			sourceTree = "<group>";
		};
		E8C4259524B2B48E00958ED7 /* GameFlexTests */ = {
			isa = PBXGroup;
			children = (
				E8C4259624B2B48E00958ED7 /* GameFlexTests.swift */,
				E8C4259824B2B48E00958ED7 /* Info.plist */,
			);
			path = GameFlexTests;
			sourceTree = "<group>";
		};
		E8C425A024B2B48E00958ED7 /* GameFlexUITests */ = {
			isa = PBXGroup;
			children = (
				E8C425A124B2B48E00958ED7 /* GameFlexUITests.swift */,
				E8C425A324B2B48E00958ED7 /* Info.plist */,
			);
			path = GameFlexUITests;
			sourceTree = "<group>";
		};
		E8F9117E250AF392002C7394 /* CreateFlex */ = {
			isa = PBXGroup;
			children = (
				E892A02724CD0C8600037057 /* Fonts Cells */,
				E890297124BCE9B400C83F72 /* CameraControlsCollectionViewCell.swift */,
				E890297324BCE9C700C83F72 /* CameraControlsCollectionViewCell.xib */,
				E890298124BFE1EF00C83F72 /* EditCollectionViewCell.swift */,
				E8020D0024BFF50800C434B4 /* EditCollectionViewCell.xib */,
				E8E301ED24D739E1007B5CF7 /* FavsHeaderCollectionViewCell.swift */,
				E8E301EF24D73AB9007B5CF7 /* FavsHeaderCollectionViewCell.xib */,
				E83EC6B324C26EC000B73083 /* FlareCategoryCollectionViewCell.swift */,
				E83EC6B524C26F4E00B73083 /* FlareCategoryCollectionViewCell.xib */,
				E890297524BD13F600C83F72 /* FilterCollectionViewCell.swift */,
				E890297724BD140B00C83F72 /* FilterCollectionViewCell.xib */,
				E83EC6C024C3CF6600B73083 /* FlareEmptyCollectionViewCell.swift */,
				E83EC6C224C3CF8A00B73083 /* FlareEmptyCollectionViewCell.xib */,
				E83EC6BD24C3778600B73083 /* FlareHeaderLabelCollectionViewCell.xib */,
				E83EC6BB24C3764200B73083 /* FlareHeaderLabelCollectionViewCell.swift */,
				E890297C24BE908C00C83F72 /* ImageEnhancementControlsView.swift */,
				E890297E24BE909D00C83F72 /* ImageEnhancementControlsView.xib */,
				E83EC6AF24C12BD000B73083 /* SliderCollectionViewCell.swift */,
				E83EC6B124C12CC700B73083 /* SliderCollectionViewCell.xib */,
				E892A12324CF71DB00037057 /* StickerCollectionViewCell.swift */,
				E892A12524CF752E00037057 /* StickerCollectionViewCell.xib */,
				E83D18FA24F3E96A008A3B79 /* TextFieldTableViewCell.swift */,
				E83D18FC24F3EC10008A3B79 /* TextFieldTableViewCell.xib */,
			);
			path = CreateFlex;
			sourceTree = "<group>";
		};
		E8F9117F250AF40F002C7394 /* Views */ = {
			isa = PBXGroup;
			children = (
				E8F91164250488A8002C7394 /* DrawView.swift */,
				E8E3018524D382CA007B5CF7 /* FilterView.swift */,
				E83EC6B724C340F500B73083 /* FlareHeaderView.swift */,
				E83EC6B924C342A400B73083 /* FlareHeaderView.xib */,
				E8F9116C2507112A002C7394 /* GFSpinnerView.swift */,
				E8C2C60425308BD800462ED7 /* GFSproketView.swift */,
				E8C2C60325308BD800462ED7 /* GFSproketView.xib */,
				E880B791255AFBB700CF5D83 /* GFSproketView2.swift */,
				E880B792255AFBB700CF5D83 /* GFSproketView2.xib */,
				E83D18F624F3500E008A3B79 /* LoginHeaderView.swift */,
				E83D18F824F3501F008A3B79 /* LoginHeaderView.xib */,
				E890297A24BD3A2700C83F72 /* SelectedFilterView.swift */,
				E85ACC372533D49B00C8C9D0 /* SideBarView.swift */,
				E85ACC5A2533D51800C8C9D0 /* SideBarView.xib */,
				E892A01D24CB20F300037057 /* StickerView.swift */,
				E892A02824CDFA9B00037057 /* StickerView.xib */,
				E85ACCFD25367A8200C8C9D0 /* TextStickerView.swift */,
				E831682A253697BD0086CA75 /* TextStickerView.xib */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		FC68C9FD483EE67A59262BEA /* Pods */ = {
			isa = PBXGroup;
			children = (
				9D3DDA13B2204D2426E1DD17 /* Pods-GameFlex.debug.xcconfig */,
				A94FBC2E7AE6237DAAD041A5 /* Pods-GameFlex.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		E8C4257B24B2B48D00958ED7 /* GameFlex */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E8C425A624B2B48E00958ED7 /* Build configuration list for PBXNativeTarget "GameFlex" */;
			buildPhases = (
				24291FDA60C8B67AE97E28C8 /* [CP] Check Pods Manifest.lock */,
				E8C4257824B2B48D00958ED7 /* Sources */,
				E8C4257924B2B48D00958ED7 /* Frameworks */,
				E8C4257A24B2B48D00958ED7 /* Resources */,
				B2998824B8FBCA14F9E61C99 /* [CP] Embed Pods Frameworks */,
				67315C5A3A652F9A05F00E25 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = GameFlex;
			productName = GameFlex;
			productReference = E8C4257C24B2B48D00958ED7 /* GameFlex.app */;
			productType = "com.apple.product-type.application";
		};
		E8C4259124B2B48E00958ED7 /* GameFlexTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E8C425A924B2B48E00958ED7 /* Build configuration list for PBXNativeTarget "GameFlexTests" */;
			buildPhases = (
				E8C4258E24B2B48E00958ED7 /* Sources */,
				E8C4258F24B2B48E00958ED7 /* Frameworks */,
				E8C4259024B2B48E00958ED7 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				E8C4259424B2B48E00958ED7 /* PBXTargetDependency */,
			);
			name = GameFlexTests;
			productName = GameFlexTests;
			productReference = E8C4259224B2B48E00958ED7 /* GameFlexTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		E8C4259C24B2B48E00958ED7 /* GameFlexUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E8C425AC24B2B48E00958ED7 /* Build configuration list for PBXNativeTarget "GameFlexUITests" */;
			buildPhases = (
				E8C4259924B2B48E00958ED7 /* Sources */,
				E8C4259A24B2B48E00958ED7 /* Frameworks */,
				E8C4259B24B2B48E00958ED7 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				E8C4259F24B2B48E00958ED7 /* PBXTargetDependency */,
			);
			name = GameFlexUITests;
			productName = GameFlexUITests;
			productReference = E8C4259D24B2B48E00958ED7 /* GameFlexUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		E8C4257424B2B48D00958ED7 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1150;
				LastUpgradeCheck = 1200;
				ORGANIZATIONNAME = GameFlex;
				TargetAttributes = {
					E8C4257B24B2B48D00958ED7 = {
						CreatedOnToolsVersion = 11.5;
					};
					E8C4259124B2B48E00958ED7 = {
						CreatedOnToolsVersion = 11.5;
						TestTargetID = E8C4257B24B2B48D00958ED7;
					};
					E8C4259C24B2B48E00958ED7 = {
						CreatedOnToolsVersion = 11.5;
						TestTargetID = E8C4257B24B2B48D00958ED7;
					};
				};
			};
			buildConfigurationList = E8C4257724B2B48D00958ED7 /* Build configuration list for PBXProject "GameFlex" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = E8C4257324B2B48D00958ED7;
			productRefGroup = E8C4257D24B2B48D00958ED7 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				E8C4257B24B2B48D00958ED7 /* GameFlex */,
				E8C4259124B2B48E00958ED7 /* GameFlexTests */,
				E8C4259C24B2B48E00958ED7 /* GameFlexUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		E8C4257A24B2B48D00958ED7 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E8C4258C24B2B48E00958ED7 /* Launch.storyboard in Resources */,
				E8E301F024D73AB9007B5CF7 /* FavsHeaderCollectionViewCell.xib in Resources */,
				E86D8709252CB9A800257956 /* EditProfileTextViewTableViewCell.xib in Resources */,
				E83D18FD24F3EC10008A3B79 /* TextFieldTableViewCell.xib in Resources */,
				E8C2C5FF253086BF00462ED7 /* SearchResultTableViewCell.xib in Resources */,
				E83EC6B224C12CC700B73083 /* SliderCollectionViewCell.xib in Resources */,
				E83EC6BA24C342A400B73083 /* FlareHeaderView.xib in Resources */,
				E88D5A4225AE9D9F00070477 /* FlagTableViewCell.xib in Resources */,
				E8B18DAD253CF432001DF5C3 /* HashTagsTableViewCell.xib in Resources */,
				E86D86A9252C9B4A00257956 /* PSA-Character_Flex-Smoothflex.png in Resources */,
				E8020D0124BFF50800C434B4 /* EditCollectionViewCell.xib in Resources */,
				E83EC6BE24C3778600B73083 /* FlareHeaderLabelCollectionViewCell.xib in Resources */,
				E831682B253697BD0086CA75 /* TextStickerView.xib in Resources */,
				E892A02E24CF0FC000037057 /* ColorCollectionViewCell.xib in Resources */,
				E86D870E252D12E700257956 /* EditProfileButtonTableViewCell.xib in Resources */,
				E890297424BCE9C700C83F72 /* CameraControlsCollectionViewCell.xib in Resources */,
				E8E301E424D65638007B5CF7 /* PressStart2P-Regular.ttf in Resources */,
				E890297F24BE909D00C83F72 /* ImageEnhancementControlsView.xib in Resources */,
				E86AFC5F25BE729900013CA4 /* ImageViewTableViewCell.xib in Resources */,
				E8E301D324D65638007B5CF7 /* IndieFlower-Regular.ttf in Resources */,
				E80267762551EF59005637D0 /* ZillaSlabHighlight-Bold.ttf in Resources */,
				E8C425B424B2BFB200958ED7 /* Localizable.strings in Resources */,
				E86FBEB9256B3D7E004A18D6 /* CreateChannelPictureTableViewCell.xib in Resources */,
				E83D18F924F3501F008A3B79 /* LoginHeaderView.xib in Resources */,
				E86FBF1025717630004A18D6 /* PSA-GameOver.jpg in Resources */,
				E86D86FC252CB95D00257956 /* EditProfileTextFieldTableViewCell.xib in Resources */,
				E8CB9BF624C9C55C000085EA /* FontCollectionViewCell.xib in Resources */,
				E880B794255AFBB700CF5D83 /* GFSproketView2.xib in Resources */,
				E88FB2472510E5A400F57306 /* MonthYearCollectionViewCell.xib in Resources */,
				E8A8D4AF2579188500126FC7 /* AlertButtonTableViewCell.xib in Resources */,
				E8BF8A3C25B0860E00897F74 /* CounterCollectionViewCell.xib in Resources */,
				E83EC6C324C3CF8A00B73083 /* FlareEmptyCollectionViewCell.xib in Resources */,
				E89FB4EE2569FBCC0082925B /* FavoriteChannelTableViewCell.xib in Resources */,
				E8A2F39625B6F9310092BC8C /* ChannelDirectoryHeaderLabelCollectionViewCell.xib in Resources */,
				E86AFC6625BE7F0B00013CA4 /* BreadcrumbChannelTableViewCell.xib in Resources */,
				E82EC24B25452EE70061AF32 /* CommentTableViewCell.xib in Resources */,
				E8BF8A3725B0811B00897F74 /* CounterTableViewCell.xib in Resources */,
				E892A12624CF752E00037057 /* StickerCollectionViewCell.xib in Resources */,
				E86FBF0B256E12FD004A18D6 /* ChannelTableViewCell.xib in Resources */,
				E85ACBB02532B14E00C8C9D0 /* ProfileBottomTableViewCell.xib in Resources */,
				E86FA7C72523F14C00C8EA09 /* ProfileTopTableViewCell.xib in Resources */,
				E83EC6B624C26F4E00B73083 /* FlareCategoryCollectionViewCell.xib in Resources */,
				E89FB4F8256A03EF0082925B /* FavoriteChannelCollectionViewCell.xib in Resources */,
				E86FBF352571797A004A18D6 /* PSA-MakeMoreFlex.jpg in Resources */,
				E8F91183250AF5C4002C7394 /* FlexTableViewCell.xib in Resources */,
				E880B6B325571A2A00CF5D83 /* FollowingTableViewCell.xib in Resources */,
				E890297824BD140B00C83F72 /* FilterCollectionViewCell.xib in Resources */,
				E8C2C60525308BD900462ED7 /* GFSproketView.xib in Resources */,
				E85ACC5B2533D51800C8C9D0 /* SideBarView.xib in Resources */,
				E8A8D4622575F87D00126FC7 /* FlexChannelTableViewCell.xib in Resources */,
				E8E301DE24D65638007B5CF7 /* Roboto-Medium.ttf in Resources */,
				E85ACBA62532A19400C8C9D0 /* ProfileMiddleTableViewCell.xib in Resources */,
				E8D8B1402520B042006E2067 /* swords.mp4 in Resources */,
				E8E301D224D65638007B5CF7 /* IMFellDoublePica-Regular.ttf in Resources */,
				E890294E24B9213600C83F72 /* GoogleService-Info.plist in Resources */,
				E8BF8A2D25B0679000897F74 /* BreadcrumbTableViewCell.xib in Resources */,
				E80267742551EF59005637D0 /* LuckiestGuy-Regular.ttf in Resources */,
				E80267752551EF59005637D0 /* JollyLodger-Regular.ttf in Resources */,
				E8E301D524D65638007B5CF7 /* PermanentMarker-Regular.ttf in Resources */,
				E880B767255A3A0000CF5D83 /* AlertTableViewCell.xib in Resources */,
				E85ACB9C2530967000C8C9D0 /* FlexCollectionViewCell.xib in Resources */,
				E8B18D36253BE636001DF5C3 /* FinalizeTopTableViewCell.xib in Resources */,
				E892A02924CDFA9B00037057 /* StickerView.xib in Resources */,
				E8C4258924B2B48E00958ED7 /* Assets.xcassets in Resources */,
				E8C63B2F25606C90007112E9 /* EditProfilePictureTableViewCell.xib in Resources */,
				E8C4258724B2B48D00958ED7 /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E8C4259024B2B48E00958ED7 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E8C4259B24B2B48E00958ED7 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		24291FDA60C8B67AE97E28C8 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-GameFlex-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		67315C5A3A652F9A05F00E25 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-GameFlex/Pods-GameFlex-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-GameFlex/Pods-GameFlex-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-GameFlex/Pods-GameFlex-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		B2998824B8FBCA14F9E61C99 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-GameFlex/Pods-GameFlex-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-GameFlex/Pods-GameFlex-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-GameFlex/Pods-GameFlex-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		E8C4257824B2B48D00958ED7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E8C425BF24B3FD2500958ED7 /* PreCameraViewController.swift in Sources */,
				E89FB4C92569FB1D0082925B /* FavoriteChannelTableViewCell.swift in Sources */,
				E86AFCAE25C11CFB00013CA4 /* ChannelReviewViewController.swift in Sources */,
				E8BF8A3225B080AE00897F74 /* CounterTableViewCell.swift in Sources */,
				E880B793255AFBB700CF5D83 /* GFSproketView2.swift in Sources */,
				E86967672526C3DD003F5264 /* Channel.swift in Sources */,
				E823BA332591044000C09166 /* ChezLuiViewController.swift in Sources */,
				E890297224BCE9B400C83F72 /* CameraControlsCollectionViewCell.swift in Sources */,
				E88FB2432510E3E800F57306 /* AgeViewController.swift in Sources */,
				E8CB9BF424C9C4B3000085EA /* FontCollectionViewCell.swift in Sources */,
				E86D8713252D12FD00257956 /* EditProfileButtonTableViewCell.swift in Sources */,
				E85ACCA22535371100C8C9D0 /* Reaction.swift in Sources */,
				E892A01B24CB206A00037057 /* User.swift in Sources */,
				E8B18D2C253BE1B4001DF5C3 /* FinalizeViewController.swift in Sources */,
				E83EC6B824C340F500B73083 /* FlareHeaderView.swift in Sources */,
				E892A01E24CB20F300037057 /* StickerView.swift in Sources */,
				E82E0B1525AA2C2A00A32223 /* FeedViewModel.swift in Sources */,
				E892A01924CB201A00037057 /* Flex.swift in Sources */,
				E8F9117225086BE4002C7394 /* GFChannelViewController.swift in Sources */,
				E8A8D4612575F87D00126FC7 /* FlexChannelTableViewCell.swift in Sources */,
				E85ACC0E2533A60400C8C9D0 /* MyFeedViewController.swift in Sources */,
				E890297D24BE908C00C83F72 /* ImageEnhancementControlsView.swift in Sources */,
				E83EC6B024C12BD000B73083 /* SliderCollectionViewCell.swift in Sources */,
				E8F9116D2507112A002C7394 /* GFSpinnerView.swift in Sources */,
				E8C2C5D7253003F300462ED7 /* SearchResult.swift in Sources */,
				E862E8232540AC460054E94D /* HomeViewController.swift in Sources */,
				E85ACBAB2532B13B00C8C9D0 /* ProfileBottomTableViewCell.swift in Sources */,
				E8C425B824B2D0B600958ED7 /* CameraViewController.swift in Sources */,
				E8C4258424B2B48D00958ED7 /* TryingToFlexViewController.swift in Sources */,
				E85ACCFF25367A8200C8C9D0 /* TextStickerView.swift in Sources */,
				E83D18F724F3500E008A3B79 /* LoginHeaderView.swift in Sources */,
				E8E3018624D382CA007B5CF7 /* FilterView.swift in Sources */,
				E880B742255A39F300CF5D83 /* AlertTableViewCell.swift in Sources */,
				E8BF8A2C25B0679000897F74 /* BreadcrumbTableViewCell.swift in Sources */,
				E80FF70A24F2DD2900599F9B /* SignUpViewController.swift in Sources */,
				E8A8D6212587051F00126FC7 /* FeedObject.swift in Sources */,
				E893723D24DC1F670070CECA /* GFNetworkServices.swift in Sources */,
				E8B18DD3253D1C0F001DF5C3 /* FinalizeViewModel.swift in Sources */,
				E88D5A3D25AE9D8E00070477 /* FlagTableViewCell.swift in Sources */,
				E8B18D31253BE515001DF5C3 /* FinalizeTopTableViewCell.swift in Sources */,
				E88D59EC25ADD64000070477 /* BackgroundTasks.swift in Sources */,
				E8BF8A4125B0861D00897F74 /* CounterCollectionViewCell.swift in Sources */,
				E83D18FB24F3E96A008A3B79 /* TextFieldTableViewCell.swift in Sources */,
				E8F911672505AB7F002C7394 /* GFFirebaseNetworking.swift in Sources */,
				E8F91181250AF5B2002C7394 /* FlexTableViewCell.swift in Sources */,
				E892A12424CF71DB00037057 /* StickerCollectionViewCell.swift in Sources */,
				E8F9117525094D1D002C7394 /* LandingPageViewController.swift in Sources */,
				E85ACBA125329C6F00C8C9D0 /* ProfileMiddleTableViewCell.swift in Sources */,
				E8A8D4AE2579188500126FC7 /* AlertButtonTableViewCell.swift in Sources */,
				E890297024BCE72400C83F72 /* CameraViewModel.swift in Sources */,
				E86D86F4252CB93D00257956 /* EditProfileTextViewTableViewCell.swift in Sources */,
				E86AFC3A25BE71D500013CA4 /* ImageViewTableViewCell.swift in Sources */,
				E85ACC042533A56600C8C9D0 /* GFChannelNavigationController.swift in Sources */,
				E869678A25275A4B003F5264 /* Flexter.swift in Sources */,
				E8C4258024B2B48D00958ED7 /* AppDelegate.swift in Sources */,
				E80D3324254DBCB1005B40E8 /* ReflexViewController.swift in Sources */,
				E893723A24DB8AC10070CECA /* BugViewController.swift in Sources */,
				E83EC6C124C3CF6600B73083 /* FlareEmptyCollectionViewCell.swift in Sources */,
				E8895E1C25675B50000F5B7A /* RootViewController.swift in Sources */,
				E8C2C60625308BD900462ED7 /* GFSproketView.swift in Sources */,
				E8E301EE24D739E1007B5CF7 /* FavsHeaderCollectionViewCell.swift in Sources */,
				E880B808255DF7C800CF5D83 /* OnboardingStateMachine.swift in Sources */,
				E8B18D64253C65FF001DF5C3 /* HashTags.swift in Sources */,
				E8C63B2225605EF5007112E9 /* GetFlexterNameViewController.swift in Sources */,
				E8B18D8A253CF2F2001DF5C3 /* HashTagsTableViewCell.swift in Sources */,
				E8F9116F25086B5B002C7394 /* MainViewController.swift in Sources */,
				E82EC22725452E130061AF32 /* CommentTableViewCell.swift in Sources */,
				E82EC2222543CBD20061AF32 /* SideBarObject.swift in Sources */,
				E8B8526824F17829006E9530 /* ProfileViewController.swift in Sources */,
				E86FBF5D25717C72004A18D6 /* InviteViewController.swift in Sources */,
				E86FBEE6256E12E7004A18D6 /* ChannelTableViewCell.swift in Sources */,
				E86FBF8225732924004A18D6 /* ChannelDetail.swift in Sources */,
				E86D86CC252CB8F200257956 /* EditProfileTextFieldTableViewCell.swift in Sources */,
				E83D18F524F33AD0008A3B79 /* EmailViewController.swift in Sources */,
				E8A2F39525B6F9310092BC8C /* ChannelDirectoryHeaderLabelCollectionViewCell.swift in Sources */,
				E86AFC6525BE7F0B00013CA4 /* BreadcrumbChannelTableViewCell.swift in Sources */,
				E85ACB972530960000C8C9D0 /* FlexCollectionViewCell.swift in Sources */,
				E8D8B13B251FDABF006E2067 /* PageViewController.swift in Sources */,
				E8D8B133251FD4F6006E2067 /* QuickIntroViewController.swift in Sources */,
				E8EE8DFA24F511F6003C79BB /* LoginViewController.swift in Sources */,
				E8A2F36C25B61A9B0092BC8C /* ChannelDirectoryViewController.swift in Sources */,
				E85ACC382533D49B00C8C9D0 /* SideBarView.swift in Sources */,
				E86FA7A42523F13C00C8EA09 /* ProfileTopTableViewCell.swift in Sources */,
				E8F9116B250695FD002C7394 /* GFError.swift in Sources */,
				E83EC6B424C26EC000B73083 /* FlareCategoryCollectionViewCell.swift in Sources */,
				E8C63B342560ABE3007112E9 /* PortraitViewController.swift in Sources */,
				E86D871B252D1EAD00257956 /* EditProfileViewController.swift in Sources */,
				E8F91165250488A8002C7394 /* DrawView.swift in Sources */,
				E8D8B1632520B170006E2067 /* FirstViewController.swift in Sources */,
				E890297B24BD3A2700C83F72 /* SelectedFilterView.swift in Sources */,
				E86FBEBF256B4033004A18D6 /* CreateChannelViewModel.swift in Sources */,
				E8C425C224B6A8AF00958ED7 /* FlexManager.swift in Sources */,
				E80FF70C24F2F96100599F9B /* Utilities.swift in Sources */,
				E880B6B225571A2A00CF5D83 /* FollowingTableViewCell.swift in Sources */,
				E890298324BFE1EF00C83F72 /* EditCollectionViewCell.swift in Sources */,
				E8F911632504450A002C7394 /* Temp2ViewController.swift in Sources */,
				E8F91161250421FF002C7394 /* GFNavigationController.swift in Sources */,
				E8C2C5FA2530866800462ED7 /* SearchResultTableViewCell.swift in Sources */,
				E83EC6BC24C3764200B73083 /* FlareHeaderLabelCollectionViewCell.swift in Sources */,
				E8344C252539CDC2008B1A5F /* RandomNames.swift in Sources */,
				E8CB9BEE24C681C9000085EA /* FontManager.swift in Sources */,
				E82EC27625466E5A0061AF32 /* Comment.swift in Sources */,
				E8F9117B2509BEFF002C7394 /* SafeAreaFixTabBar.swift in Sources */,
				E8C425B624B2C62000958ED7 /* GFDefaults.swift in Sources */,
				E89FB4F3256A02EB0082925B /* FavoriteChannelCollectionViewCell.swift in Sources */,
				E88FB2452510E52E00F57306 /* MonthYearCollectionViewCell.swift in Sources */,
				E86FBEBA256B3D7E004A18D6 /* CreateChannelPictureTableViewCell.swift in Sources */,
				E892A02C24CF0FB100037057 /* ColorCollectionViewCell.swift in Sources */,
				E8F9117925096AAA002C7394 /* SearchViewController.swift in Sources */,
				E82EC29A2546F8C60061AF32 /* CommentViewController.swift in Sources */,
				E89FB500256B0D0E0082925B /* CreateChannelViewController.swift in Sources */,
				E8F9117725096A46002C7394 /* AlertViewController.swift in Sources */,
				E8C425B024B2B7F600958ED7 /* Extensions.swift in Sources */,
				E85ACCCE2535E00B00C8C9D0 /* WhatsNewViewController.swift in Sources */,
				E86AFBD125BCBA5400013CA4 /* ChannelViewController.swift in Sources */,
				E880B70A2558F8CD00CF5D83 /* GFNotification.swift in Sources */,
				E8C63B2A25606C77007112E9 /* EditProfilePictureTableViewCell.swift in Sources */,
				E890297624BD13F600C83F72 /* FilterCollectionViewCell.swift in Sources */,
				E8C425B224B2B83400958ED7 /* GFViewController.swift in Sources */,
				E8F911692505BE1E002C7394 /* AppInfo.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E8C4258E24B2B48E00958ED7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E8C4259724B2B48E00958ED7 /* GameFlexTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E8C4259924B2B48E00958ED7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E8C425A224B2B48E00958ED7 /* GameFlexUITests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		E8C4259424B2B48E00958ED7 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = E8C4257B24B2B48D00958ED7 /* GameFlex */;
			targetProxy = E8C4259324B2B48E00958ED7 /* PBXContainerItemProxy */;
		};
		E8C4259F24B2B48E00958ED7 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = E8C4257B24B2B48D00958ED7 /* GameFlex */;
			targetProxy = E8C4259E24B2B48E00958ED7 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		E8C4258524B2B48D00958ED7 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				E8C4258624B2B48D00958ED7 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		E8C4258A24B2B48E00958ED7 /* Launch.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				E8C4258B24B2B48E00958ED7 /* Base */,
			);
			name = Launch.storyboard;
			sourceTree = "<group>";
		};
		E8F9117025086BE4002C7394 /* GFChannelViewController.swift */ = {
			isa = PBXVariantGroup;
			children = (
				E8F9117125086BE4002C7394 /* Base */,
			);
			name = GFChannelViewController.swift;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		E8C425A424B2B48E00958ED7 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		E8C425A524B2B48E00958ED7 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		E8C425A724B2B48E00958ED7 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9D3DDA13B2204D2426E1DD17 /* Pods-GameFlex.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_ENTITLEMENTS = GameFlex/GameFlex.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 57;
				DEVELOPMENT_TEAM = 4WFY73G73M;
				INFOPLIST_FILE = GameFlex/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = io.gameflex.gameflex;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		E8C425A824B2B48E00958ED7 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A94FBC2E7AE6237DAAD041A5 /* Pods-GameFlex.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_ENTITLEMENTS = GameFlex/GameFlex.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 57;
				DEVELOPMENT_TEAM = 4WFY73G73M;
				INFOPLIST_FILE = GameFlex/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = io.gameflex.gameflex;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		E8C425AA24B2B48E00958ED7 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = FDP2ZTXYFM;
				INFOPLIST_FILE = GameFlexTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.5;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.gameflex.GameFlexTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/GameFlex.app/Contents/MacOS/GameFlex";
			};
			name = Debug;
		};
		E8C425AB24B2B48E00958ED7 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = FDP2ZTXYFM;
				INFOPLIST_FILE = GameFlexTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.5;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.gameflex.GameFlexTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/GameFlex.app/Contents/MacOS/GameFlex";
			};
			name = Release;
		};
		E8C425AD24B2B48E00958ED7 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = FDP2ZTXYFM;
				INFOPLIST_FILE = GameFlexUITests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.gameflex.GameFlexUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = GameFlex;
			};
			name = Debug;
		};
		E8C425AE24B2B48E00958ED7 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = FDP2ZTXYFM;
				INFOPLIST_FILE = GameFlexUITests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.gameflex.GameFlexUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = GameFlex;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		E8C4257724B2B48D00958ED7 /* Build configuration list for PBXProject "GameFlex" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E8C425A424B2B48E00958ED7 /* Debug */,
				E8C425A524B2B48E00958ED7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E8C425A624B2B48E00958ED7 /* Build configuration list for PBXNativeTarget "GameFlex" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E8C425A724B2B48E00958ED7 /* Debug */,
				E8C425A824B2B48E00958ED7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E8C425A924B2B48E00958ED7 /* Build configuration list for PBXNativeTarget "GameFlexTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E8C425AA24B2B48E00958ED7 /* Debug */,
				E8C425AB24B2B48E00958ED7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E8C425AC24B2B48E00958ED7 /* Build configuration list for PBXNativeTarget "GameFlexUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E8C425AD24B2B48E00958ED7 /* Debug */,
				E8C425AE24B2B48E00958ED7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = E8C4257424B2B48D00958ED7 /* Project object */;
}
