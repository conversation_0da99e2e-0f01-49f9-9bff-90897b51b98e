//
//  FlexChannelTableViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 11/30/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import Photos

class FlexChannelTableViewCell: UITableViewCell {
    
    @IBOutlet weak var collectionView: UICollectionView!
    
    var currentRow = 0
    var direction: ScrollDirection = .right
    var flexArray: [Flex] = []
    var lagToRestartTimer: Timer?
    var lastScrollViewContentOffset: CGFloat = 0.0
    var psaIsPresent = false
    var timer: Timer?
    
//    weak var delegate: ChannelsDelegate?

    static var cellIdentifier = String(describing: FlexChannelTableViewCell.self)
    
    override func awakeFromNib() {
        super.awakeFromNib()
        selectionStyle = .none
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.gestureRecognizers = []
        collectionView.register(UINib(nibName: FlexCollectionViewCell.cellIdentifier, bundle: nil), forCellWithReuseIdentifier: FlexCollectionViewCell.cellIdentifier)
        configureCell()
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        currentRow = 0
    }
    
    func configureCell() {
        if collectionView.gestureRecognizers?.count == 0 {
            let swipeLeft = UISwipeGestureRecognizer(target: self, action: #selector(didSwipeLeft(_:)))
            swipeLeft.direction = .left
            collectionView.addGestureRecognizer(swipeLeft)
            let swipeRight = UISwipeGestureRecognizer(target: self, action: #selector(didSwipeRight(_:)))
            swipeRight.direction = .right
            collectionView.addGestureRecognizer(swipeRight)
        }
    }
    
    // MARK: - Gesture Recognizer processing
    
    // cell moves left... indexPath.row increases
    @objc func didSwipeLeft(_ sender: UIGestureRecognizer) {
        direction = .left
        guard !(flexArray.last?.flexId?.contains("PSA") ?? false) else { return }
        if let cell = self.collectionView.visibleCells.last {
            guard let row = self.collectionView.indexPath(for: cell)?.row else { return }
//            if row == FeedViewModel.channelsArray.count - 4 {
//                refreshChannel(top: false)
//            }
            currentRow += 1
            guard currentRow < collectionView.numberOfItems(inSection: 0) else {
                currentRow -= 1
                collectionView.contentInsetAdjustmentBehavior = .scrollableAxes
                let refresh = GFSproketView2(frame: CGRect(origin: CGPoint(x: 0.0, y: 0.0), size: CGSize(width: 200, height: 200)))
                refresh.center = CGPoint(x: self.frame.width-100, y: self.frame.height/2)
                addSubview(refresh)
                UIView.animate(withDuration: 1.5) {
                    self.collectionView.contentOffset.x += 200
                } completion: { (done) in
                    self.doThePullToRefreshEnding()
                }
                return
            }
            if row != currentRow {
                makeTheSideBarObjects(for: currentRow)
                collectionView.scrollToItem(at: IndexPath(row: currentRow, section: 0), at: .left, animated: true)
                FeedViewModel.updateChannelLastSeen(flex: flexArray[currentRow])
            }
        }
        if collectionView.visibleCells.count == 0 {
            collectionView.reloadData()
        }
    }
        
    // cell moves right... indexPath.row decreases
    @objc func didSwipeRight(_ sender: UIGestureRecognizer) {
        if let cell = self.collectionView.visibleCells.last {
            guard let row = self.collectionView.indexPath(for: cell)?.row else { return }
            currentRow -= 1
            guard currentRow > -1 else {
                currentRow = 0
                return
            }
            if row != currentRow, currentRow < collectionView.numberOfItems(inSection: 0) {
                makeTheSideBarObjects(for: currentRow)
                collectionView.scrollToItem(at: IndexPath(row: currentRow, section: 0), at: .left, animated: true)
            }
            self.direction = .right
        }
    }
    
    func doThePullToRefreshEnding() {
        UIView.animate(withDuration: 0.5) {
            self.collectionView.contentOffset.x -= 200
            self.collectionView.contentInsetAdjustmentBehavior = .never
        } completion: { (done) in
            self.subviews.filter({ $0.isKind(of: GFSproketView2.self) }).forEach({ $0.removeFromSuperview() })
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                self.refreshChannel(top: true)
            }
        }
    }

    @objc func refreshChannel(top: Bool) {
        // delegate: to ChannelsViewController
        }
    
    private func makeTheSideBarObjects(for row: Int) {
        guard row < flexArray.count else { return }
        let flex = flexArray[row]
        // update the sideBar
        if let flexId = flex.flexId, let userId = User.userId, userId != "" {
            var object = SideBarObject()
            object.buttonType = .like
            if let reaction = flex.reactions?.filter({ $0.type == .likes }).first {
                object.buttonCount = reaction.number ?? 0
            }
            if User.likedFlexes[userId]?.filter({ $0 == flexId }).count ?? 0 > 0 {
                object.isHighlighted = true
            } else {
                object.isHighlighted = false
            }
            
            // pass the caption to HomeViewController
            var object1 = SideBarObject()
            object1.buttonType = .caption
            var captionObject = CaptionObject()
            captionObject.caption = flex.caption
            captionObject.captionSourceId = flex.owner?.userId
            captionObject.profileImage = flex.owner?.profileImage
            captionObject.captionFlexterName = flex.owner?.flexterName
//            let reaction = flex.reactions?.filter({ $0.type == .likes }).first
            captionObject.stats = "\(Utilities.relativeTime(date: flex.createAtDate))" //• \(reaction?.number ?? 0) likes • \(flex.numberOfComments ?? 0) comments"
            object1.captionData = captionObject
            
            // pass the comment count to HomeViewController
            var object2 = SideBarObject()
            object2.buttonType = .comment
            object2.buttonCount = flex.numberOfComments ?? 0
            object2.commentData = nil
            
            // hide the play button if no reflexes
            var object3 = SideBarObject()
            object3.buttonType = .play
            object3.isHidden = !(flex.numberOfReflexes ?? 0 > 0)
            // show the play dot if reflexes
            if User.parentsWithReflexesSeen[userId] == nil {
                User.parentsWithReflexesSeen[userId] = []
            }
            if !object3.isHidden, (User.parentsWithReflexesSeen[userId]?.filter({ $0 == flexId }).count ?? 0 > 0) {
                object3.isHighlighted = false
            } else {
                object3.isHighlighted = true
            }
            var object4 = SideBarObject()
            object4.buttonType = .reflex
            object4.buttonCount = flex.numberOfReflexes ?? 0
            
            if flexId.contains("PSA") {
                updateTheSideBar(objects: [object, object2, object3, object4])
            } else {
                updateTheSideBar(objects: [object, object1, object2, object3, object4])
                // updates the lastSeenDictionary, prepares for delivery of details to server
            }
        }

    }
        
    private func updateTheSideBar(objects: [SideBarObject]) {
        DispatchQueue.main.async {
//            self.delegate?.updateTheSideBar(objects: objects)
        }
    }
    
}

extension FlexChannelTableViewCell: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        if flexArray.isEmpty {
            return 1
        }
        return flexArray.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: FlexCollectionViewCell.cellIdentifier, for: indexPath) as? FlexCollectionViewCell else { return FlexCollectionViewCell() }
        if flexArray.isEmpty {
            cell.flexImageView.image = FlexManager.placeholders[Int.random(in: 0..<FlexManager.placeholders.count)]
            cell.flexImageView.alpha = 0.6
            let label = UILabel(frame: CGRect(x: 0, y: 0, width: 100, height: 130))
            label.text = ".. needs content.. "
            label.textColor = .gfYellow_F2DE76
            label.numberOfLines = 0
            label.tag = 972231
            label.layer.shadowColor = UIColor.black.cgColor
            label.layer.shadowOffset = CGSize(width: 1.5, height: 1.5)
            label.font = .systemFont(ofSize: 19, weight: .heavy)
            label.textAlignment = .center
            cell.addSubview(label)
            return cell
        }
        cell.subviews.filter({$0.tag == 972231 }).forEach({ $0.removeFromSuperview() })
        cell.configureCell(flexArray[indexPath.row])
        cell.flexImageView.alpha = 1.0
        if indexPath.row == currentRow {
            makeTheSideBarObjects(for: indexPath.row)
        }
        
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: frame.width, height: frame.height)
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat {
        return 0.0
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, insetForSectionAt section: Int) -> UIEdgeInsets {
        return UIEdgeInsets(top: 0.0, left: 0.0, bottom: 0.0, right: 0.0)
    }
}
