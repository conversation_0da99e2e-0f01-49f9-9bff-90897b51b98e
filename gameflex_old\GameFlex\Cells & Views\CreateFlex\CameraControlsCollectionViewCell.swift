//
//  CameraControlsCollectionViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 7/13/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

class CameraControlsCollectionViewCell: UICollectionViewCell {
    
    @IBOutlet weak var swapCameraButton: UIButton!
    @IBOutlet weak var timerButton: UIButton!
    @IBOutlet weak var flashButton: UIButton!
    @IBOutlet weak var cameraButton: UIButton!
    @IBOutlet weak var timerBackgroundButton: UIButton!
    
    var arrayOfButtons: [UIButton] = []
    weak var delegate: CameraDelegate?
    
    static var cellIdentifier = String(describing: CameraControlsCollectionViewCell.self)
    
    override func awakeFromNib() {
        super.awakeFromNib()
        arrayOfButtons = [swapCameraButton, timerButton, flashButton, cameraButton]
        arrayOfButtons.forEach({self.bringSubviewToFront( $0 ) })
    }
    
    @IBAction func didTapButton(sender: UIButton) {
        var action = CameraActionType.snapPicture
              switch sender {
            case cameraButton: action = .snapPicture
            case timerButton: action = .runTimer
            case swapCameraButton: action = .swapCamera
            case flashButton: action = .toggleFlash
              default: break
        }
        delegate?.didTapForCameraAction(action)
    }
}
