//
//  StickerView.swift
//  GameFlex
//
//  Created by <PERSON> on 7/24/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//
// This textView is for emoji handling. The TextStickerView class is a subclass of StickerView.

import UIKit

enum StickerType {
    case text, sticker, emoji
}

class StickerView: UIView {
    
    @IBOutlet weak var textView: UITextView!
    @IBOutlet weak var imageView: UIImageView!
    
    var name: String?
    var viewType: StickerType = .sticker
    var originalFontSize: CGFloat = 36.0
    var buttons: [UIButton] = []
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        let nib = UINib(nibName: String(describing: type(of: self)), bundle: Bundle(for: type(of: self)))
        let view = nib.instantiate(withOwner: self, options: nil).first as! UIView
        view.frame = frame
        addSubview(view)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setupView(_ stickerType: StickerType) {
        textView.isSelectable = false
        textView.isEditable = false

        viewType = stickerType
        switch stickerType {
        case .sticker:
            imageView.isHidden = false
            textView.isHidden = true
        case .emoji:
            textView.isHidden = false
            imageView.isHidden = true
        case .text:
            imageView.removeFromSuperview()
            textView.removeFromSuperview()
        }
    }
    
}
