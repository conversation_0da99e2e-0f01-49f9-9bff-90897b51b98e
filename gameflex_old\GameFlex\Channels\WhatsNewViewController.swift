//
//  WhatsNewViewController.swift
//  GameFlex
//
//  Created by <PERSON> on 10/13/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

class WhatsNewViewController: GFViewController {
    
    @IBOutlet weak var textView: UITextView!
            
    static func storyboardInstance() -> WhatsNewViewController {
        let sb = UIStoryboard(name: "Main", bundle: nil)
        return sb.instantiateViewController(withIdentifier: String(describing: WhatsNewViewController.self)) as! WhatsNewViewController
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        addCancelToNavBar()
        title = "What's New?"
        let knownBugs = "\n-------------\n\nKNOWN BUGS:\n• Completely rotating makes some flare stop responding to touch?\n• Shrinking flare doesn't work after initial selection. \nTODOS:\n• Right edge tapping to see reflexes?\n• Firebase event instrumentation."
        let topLine = "Welcome to v.\(User.app ?? "30").\n-------------\n\n"
        switch Int(User.app ?? "30") {
        case 57: textView.text = "• Disabled channels.\n• Fixed pause in launch sequence\n• Flags are on 3 second timer." + knownBugs
        case 56:
            textView.text = topLine + "• New Channels Directory; new Channels experience.\n• Experimental 'toast' on Channels to remind users to swipe <> not v^.\n• Settings and more button taps are now action sheet styles.\n• Converted Bug message to 'Report' form.\n• Enables Follow channels in search.\n• Better reflex performance.\n• Corrected Profile details.\n• More stability improvements." + knownBugs
        case 55:
            textView.text = topLine + "• App remembers where you were before you left which feed.\n• Enables Follow channels in search.\n• Better reflex performance.\n• More stability improvements." + knownBugs

        case 54:
            textView.text = topLine + "• New architecture for more stable feed. Switching between feeds works as expected.\n• Reflexes return to parent flex. Reflex timer starts once reflex is completely present.\n• Better caption layout.\n• Long press hides the controls until ended (except during reflex streams where it pauses the timer)." + knownBugs
        case 53:
            textView.text = topLine + "HAPPY NEW YEAR!\n• Pic drift bug is history. BAM!\n• Avatar portrait camera can now rotate, zoom and be off-center!\n• Moving flare close to bottom doesn't make the tray jump.\n• Empty channels crash prevented.\n• Login and Email signup/in buttons are automatically enabled when required conditions met.\n• Follow button is removed everywhere except in profile.\n• Can see other people's flexes via their profile.\n• More functional invitation flow.\n• New delete channel button.\n• Previous caption doesn't linger." + knownBugs
        case 51:
            textView.text = topLine + "• Carriage return now allowed on textStickers.\n• Rearranged others' Profile - can see/comment/like others' flexes from their profile./n• Refreshed flare./n • Flexes are now shown in aspect-fit mode (was aspect-fill, which cut off corners and edges depending on the device you have and the flex was constructed on).\n• Your music plays while using GameFlex." + knownBugs
        case 50:
            textView.text = topLine + "• Fixed no channels crash.\n• Pagination comes to Prime channel.\n• Following is remove from home. Now available on Profile view of others.\n• Keyboard can't hide text input box in Comments." + knownBugs
        case 47,48:
            textView.text = topLine + "• Many stability improvements\n• Fixed hanging font selector on text dismissal.\n• Added Followers to profile.\n• Adjusted Notifications presentation.\n• Follower pagination enabled.\n• Last seen flex, channel, feed available and passed to server." + knownBugs
        case 46:
            textView.text = topLine + "• Home view buttons - like, comment, reflex, play - working on My Feed and Channels too.\n• Reorganized button layout.\n• Delete channels with swipe by owner.\n• New time details: m h d. \nFIXED MANY BUGS:\n• Improved Quick Intro animation on FaceID devices.\n• Fixed stuck spinner for Sign In With Apple users." + knownBugs
        case 45:
            textView.text = topLine + "NEW FEATURES:\n•• Main - changing to show only most liked flexes.\n•• Following - this stream shows all the flexes made by people you follow.\n•• Channels - Profile can create channels, invite friends and in 'Finalize' (post-flex) can flex to them.\n•• Accept invites to join a channel in Alerts.\n\n• Side bar controls in Following and Channels are hidden. Can't like/reflex there, yet. Coming...\n • Navigation is a work in progress. Doesn't remember where you were in Channel, or in Following. Coming...\n\nFIXES:\n• Fixed Main channel crashes.\n• Fixed Sign In with Apple forever spinning bug. \n\nThanks for reporting bugs!" + knownBugs
        case 44:
            textView.text = topLine + "• Instant update of channel when returning to channel.\n• Smoothed out transition from movie to Main.\n• Portrait camera improved.\n• Fixed top flex bug!\n• Corrected behavior of search results.\n• Wrestled the onboarding experience to the mat! - Simple, clean and smooth.\n• New Portrait camera experience.\n• Your total stats are counting!" + knownBugs
        case 42:
            textView.text = topLine + "• Fixed top flex bug!\n• Corrected behavior of search results.\n• Wrestled the onboarding experience to the mat! - Simple, clean and smooth.\n• New Portrait camera experience.\n• Your total stats are counting!" + knownBugs
        case 40, 41:
            textView.text = topLine + "Big bug cleanup and features extravaganza!\n• Following/unfollowing flexters.\n• Alerts working: notifications and Apple Push Notifications.\n• End of channel public service message works.\n • Corrected dates of flexes.\n• Tap profile icons and goes to Profile.\n• Make a reflex and parent flex details get updated.\n• Only one comment per tap of submit button.\n• Comment textView clears once submit is tapped.\n• TabBar (bottom controllers) are locked in place and opaque.\n• Changed sideBar select buttons.\n• Removed text cursor from flexes.\n• Enable case-indifferent search. " + knownBugs
        case 36:
            textView.text = topLine + "NOW WITH REFLEXES!\n\n• Removed 4 too-similar filters.\n• Changed caption background to gradient.\n• Implemented new comment count response on new comments." + knownBugs
        case 35:
            textView.text = topLine + "This is a fixes load.\n• Prevents spacebar as a flexter name.\n• Fixed some broken stuff (Finalize goes right back to Main Channel. Silenced a few GameFlex spinners. Rebuilt the channel surfing - should be way more consistent and stable. Upped the comments. Made side bar always appear for logged in users." + knownBugs
        case 34:
            textView.text = topLine + "• More stable Like control.\n• New caption feature. Tap the ^ at the bottom of the view to see more or less of the caption and the captioner.\n• New Comments feature. Needs thorough testing.\n• New More button connected.\n• Happy flexing!" + knownBugs
        case 33:
            textView.text = topLine + "• Updated refreshControl with pacMan.\n• New opacity icons (pies).\n• New 'Finalize' for finishing up your flex -> caption and add hashtags.\n• New HashTags system ready for server data - will provide recent and frequent.\n• Now smilies are consistently available as default imageProfile pic (if user hasn't uploaded pic).\n• Initial flows in the right sequence.\n• Accelerated channel navBar's swipe transitions.\n• New filters.\n• Now flexes are cropped and scaled to fit for better presentation of bottom-hugging art.\n• Added Crashlytics to get info about crashes." + knownBugs
        case 32:
            textView.text = topLine + "• Fast like updating.\n• New support for unlike.\n• Better navigation between Following / Main / Channels.\n• Flare shows up first then tray closes.\n• Edit profile is prepopulated with your previously uploaded content.\n• Flexter Name is checked, makes suggestions and requires uniqueness. Please test this." + knownBugs
        case 31:
            textView.text = topLine + "• This rev includes fixes for text sticker behavior. We need text stickers tested please." + knownBugs
        case 30:
            textView.text = topLine + "• Search is up to date (presents a random set of placeholder flexes, needs to be updated with list of users’ actual flexes… coming)\n• Profile is complete (similarly presents a random set of placeholder flexes, needs updating with list of users’ actual flexes… coming)\n• version number and Settings button is moved to Alert temporarily.\n• Main channel is complemented with Following and Channels options at top. Following and Channels are populated with a dummy flex. Sorry, these aren't likable (yet). Needs updating once we create channels.\n• Main channel sidebar buttons in place (for logged in users).\n• Like button works for flexes (you can like any flex only once). No unlike feature. Other buttons (share, reflex, etc) need to be connected up.\n-------------\n\nKNOWN BUGS:\n• pics drift to center.\n• text stickers are kinda flaky.\n• incomplete Main channel side buttons."
            
            
        default:
            textView.text = topLine + knownBugs
        }
        let tap = UITapGestureRecognizer(target: self, action: #selector(dismissMe))
        view.addGestureRecognizer(tap)
    }
    
    @objc @IBAction override func dismissMe() {
        if User.isLoggedIn {
            dismiss(animated: true, completion: nil)
        }
        OnboardingStateMachine.didComplete(this: .whatsnew)
    }
}
