//
//  FilterCollectionViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 7/13/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

class FilterCollectionViewCell: UICollectionViewCell {
    
    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var filterImageView: UIImageView!
    @IBOutlet weak var selectedFilterView: SelectedFilterView!
    @IBOutlet weak var selectFilterButton: UIButton!
            
    static var cellIdentifier = String(describing: FilterCollectionViewCell.self)
    
    override func awakeFromNib() {
        super.awakeFromNib()
        filterImageView.layer.cornerRadius = filterImageView.frame.size.width/2
        selectedFilterView.layer.cornerRadius = selectedFilterView.frame.size.width/2
        selectThisFilter(false)
        selectFilterButton.isUserInteractionEnabled = false
    }
    
    @IBAction func didTapButton(_ sender: UIButton) {

    }
    
    func selectThisFilter(_ selected: Bool = false) {
        if selected {
            selectedFilterView.isHidden = false
            titleLabel.textColor = .gfGreen
            titleLabel.font = .boldSystemFont(ofSize: titleLabel.font.pointSize)
        } else {
            selectedFilterView.isHidden = true
            titleLabel.textColor = .white
            titleLabel.font = .systemFont(ofSize: titleLabel.font.pointSize)
        }
    }
    
}
