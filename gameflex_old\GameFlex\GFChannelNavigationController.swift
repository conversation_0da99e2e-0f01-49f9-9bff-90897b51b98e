//
//  GFChannelNavigationController.swift
//  GameFlex
//
//  Created by <PERSON> on 10/11/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import QuartzCore

enum CustomViewAnimationTransition {
    case none, flipFromLeft, flipFromRight, curlUp, curlDown, fadeIn, moveIn, push, reveal
}

enum CustomViewAnimationSubType {
    case fromRight, fromLeft, fromTop, fromBottom
    
    func raw() -> CATransitionSubtype {
        switch self {
        case .fromRight: return CATransitionSubtype.fromRight
        case .fromLeft:  return CATransitionSubtype.fromLeft
        case .fromTop:  return CATransitionSubtype.fromTop
        case .fromBottom: return CATransitionSubtype.fromBottom
        }
    }
}

enum CustomViewAnimationType {
    case fade, moveIn, push, reveal
    
    func raw() -> CATransitionType { // relevant so only this class needs to import QuartzCore
        switch self {
        case .fade: return CATransitionType.fade
        case .moveIn: return CATransitionType.moveIn
        case .push: return CATransitionType.push
        case .reveal: return CATransitionType.reveal
        }
    }
}

class GFChannelNavigationController: UINavigationController {
    
    override func popViewController(animated: Bool) -> UIViewController? {
        if let home = viewControllers[0] as? HomeViewController {
            home.skipTheTripToMyFeed = true
        }
        return super.popViewController(animated: animated)
    }
    
    public func pushViewController(_ viewController: UIViewController, withCustomTransition transition: CustomViewAnimationTransition, subtype:CustomViewAnimationSubType) {
        switch transition {
        case .none:
            standardAnimation(viewController:viewController, duration:0.5, options: UIView.AnimationOptions.transitionCrossDissolve, closure: { () in
                self.pushViewController(viewController, animated: false)
            })
        case .flipFromLeft:
            standardAnimation(viewController:viewController, duration:0.5, options: UIView.AnimationOptions.transitionFlipFromLeft, closure: { () in
                self.pushViewController(viewController, animated: false)
            })
        case .flipFromRight:
            standardAnimation(viewController:viewController, duration:0.5, options:UIView.AnimationOptions.transitionFlipFromRight, closure: { () in
                self.pushViewController(viewController, animated: false)
            })
        case .curlUp:
            standardAnimation(viewController:viewController, duration:0.5, options:UIView.AnimationOptions.transitionCurlUp, closure: { () in
                self.pushViewController(viewController, animated: false)
            })
        case .curlDown:
            standardAnimation(viewController:viewController, duration:0.5, options:UIView.AnimationOptions.transitionCurlDown, closure: { () in
            self.pushViewController(viewController, animated: false)
            })
        // here are the standard transitions
        case .fadeIn:
            coreAnimation(viewController:viewController, duration:0.5, type: .fade, subType: nil, closure: { ()
                self.pushViewController(viewController, animated: false)
            })
        case .moveIn:
            coreAnimation(viewController:viewController, duration:0.5, type: .moveIn, subType: subtype, closure: { () in
                self.pushViewController(viewController, animated: false)
            })
        case .push:
            coreAnimation(viewController:viewController, duration:0.5, type: .push, subType: subtype, closure: { () in
                self.pushViewController(viewController, animated: false)
            })
        case .reveal:
            coreAnimation(viewController:viewController, duration:0.5, type: .reveal, subType: subtype, closure: { () in
                self.pushViewController(viewController, animated: false)
            })
        }
    }
    
    public func popViewController(customTransition: CustomViewAnimationTransition, subType:CustomViewAnimationSubType) {
        switch customTransition {
        case .none:
            standardAnimation(viewController:nil, duration:0.5, options:UIView.AnimationOptions.transitionCrossDissolve, closure: { () in
                _ = self.popViewController(animated: false)
                })
        case .flipFromLeft:
            standardAnimation(viewController:nil, duration:0.5, options:UIView.AnimationOptions.transitionFlipFromLeft, closure: { () in
                _ = self.popViewController(animated: false)
            })
        case .flipFromRight:
            standardAnimation(viewController:nil, duration:0.5, options:UIView.AnimationOptions.transitionFlipFromRight, closure: { () in
                _ = self.popViewController(animated: false)
            })
        case .curlUp:
            standardAnimation(viewController:nil, duration:0.5, options:UIView.AnimationOptions.transitionCurlUp, closure: { () in
                _ = self.popViewController(animated: false)
            })

        case .curlDown:
            standardAnimation(viewController:nil, duration:0.5, options:UIView.AnimationOptions.transitionCurlDown, closure: { () in
                _ = self.popViewController(animated: false)
            })
        
        case .fadeIn:
            coreAnimation(viewController:nil, duration:0.5, type: .fade, subType: subType, closure: { () in
                _ = self.popViewController(animated: false)
            })
        case .moveIn:
            coreAnimation(viewController:nil, duration:0.5, type: .moveIn, subType: subType, closure: { () in
                _ = self.popViewController(animated: false)
            })
        case .push:
            coreAnimation(viewController:nil, duration:0.5, type: .push, subType: subType, closure: { () in
                _ = self.popViewController(animated: false)
            })
        case .reveal:
            coreAnimation(viewController:nil, duration:0.5, type: .reveal, subType: subType, closure: { () in
                _ = self.popViewController(animated: false)
            })
        }

    }
    
    public func popToRootViewController(_ customTransition: CustomViewAnimationTransition, subType:CustomViewAnimationSubType) {
        switch customTransition {
        case .none:
            standardAnimation(viewController:nil, duration:0.5, options: UIView.AnimationOptions.transitionCrossDissolve, closure: { () in
                self.popToRootViewController(animated: false)
            })
        case .flipFromLeft:
            standardAnimation(viewController:nil, duration:0.5, options:UIView.AnimationOptions.transitionFlipFromLeft, closure: { () in
                self.popToRootViewController(animated: false)
            })
        case .flipFromRight:
            standardAnimation(viewController:nil, duration:0.5, options:UIView.AnimationOptions.transitionFlipFromRight, closure: { () in
                self.popToRootViewController(animated: false)
            })
        case .curlUp:
            standardAnimation(viewController:nil, duration:0.5, options:UIView.AnimationOptions.transitionCurlUp, closure: { () in
                self.popToRootViewController(animated: false)
            })
        case .curlDown:
            standardAnimation(viewController:nil, duration:0.5, options:UIView.AnimationOptions.transitionCurlDown, closure: { () in
                self.popToRootViewController(animated: false)
            })
        case .fadeIn:
            coreAnimation(viewController:nil, duration:0.5, type: .fade, subType: nil, closure: { () in
                self.popToRootViewController(animated: false)
            })
        case .moveIn:
            coreAnimation(viewController:nil, duration:0.5, type: .moveIn, subType: nil, closure: { () in
                self.popToRootViewController(animated: false)
            })
        case .push:
            coreAnimation(viewController:nil, duration:0.5, type: .push, subType: nil, closure: { () in
                self.popToRootViewController(animated: false)
            })
        case .reveal:
            coreAnimation(viewController:nil, duration:0.5, type: .reveal, subType: nil, closure: { () in
                self.popToRootViewController(animated: false)
            })
        }

    }
    
    public func popToViewController(_ viewController: UIViewController, withCustomTransition transition: CustomViewAnimationTransition, subType:CustomViewAnimationSubType) {
        switch transition {
        case .none:
            standardAnimation(viewController:nil, duration:0.5, options: UIView.AnimationOptions.transitionCrossDissolve, closure: { () in
                _ = self.popViewController(animated: true)
            })
        case .flipFromLeft:
            standardAnimation(viewController:nil, duration:0.5, options: UIView.AnimationOptions.transitionFlipFromLeft, closure: { () in
                _ = self.popViewController(animated: true)
            })
        case .flipFromRight:
            standardAnimation(viewController:nil, duration:0.5, options: UIView.AnimationOptions.transitionFlipFromLeft, closure: { () in
                _ = self.popViewController(animated: true)
            })
        case .curlUp:
            standardAnimation(viewController:nil, duration:0.5, options: UIView.AnimationOptions.transitionCurlUp, closure: { () in
                _ = self.popViewController(animated: true)
            })
        case .curlDown:
            standardAnimation(viewController:nil, duration:0.5, options: UIView.AnimationOptions.transitionCurlDown, closure: { () in
                _ = self.popViewController(animated: true)
            })
        case .fadeIn:
            self.coreAnimation(viewController:viewController, duration:0.5, type: .fade, subType: nil, closure: { () in
                _ = self.popViewController(animated: true)
            })
        case .moveIn:
            self.coreAnimation(viewController:viewController, duration:0.5, type: .moveIn, subType: nil, closure: { () in
                _ = self.popViewController(animated: true)
            })
        case .push:
            self.coreAnimation(viewController:viewController, duration:0.5, type: .push, subType: nil, closure: { () in
                _ = self.popViewController(animated: true)
            })
        case .reveal:
            self.coreAnimation(viewController:viewController, duration:0.5, type: .reveal, subType: nil, closure: { () in
                _ = self.popViewController(animated: true)
            })
        }

    }
    
    fileprivate func standardAnimation(viewController: UIViewController?, duration: TimeInterval, options: UIView.AnimationOptions?, closure: @escaping () -> Void) {
        UIView.transition(with: self.view, duration: duration, options: options ?? .transitionCrossDissolve, animations: closure, completion: nil)
    }
    
    fileprivate func coreAnimation(viewController: UIViewController?, duration: TimeInterval, type: CustomViewAnimationType, subType: CustomViewAnimationSubType?, closure: @escaping () -> Void) {
        let trans = CATransition()
        trans.duration = duration/2.0
        trans.type = type.raw()
        trans.subtype = subType?.raw()
        view.layer.add(trans, forKey: kCATransition)
        closure()
    }

}
