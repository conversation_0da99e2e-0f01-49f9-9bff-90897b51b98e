# GameFlex Backend Stop Script (PowerShell)
# This script stops the Supabase development environment

param(
    [switch]$Help,
    [switch]$RemoveData
)

if ($Help) {
    Write-Host "GameFlex Backend Stop Script" -ForegroundColor Green
    Write-Host "Usage: .\stop.ps1 [-RemoveData]" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Cyan
    Write-Host "  -RemoveData    Also remove all data volumes (WARNING: This will delete all data!)" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Cyan
    Write-Host "  .\stop.ps1                # Stop services but keep data"
    Write-Host "  .\stop.ps1 -RemoveData    # Stop services and remove all data"
    exit 0
}

# Set error action preference
$ErrorActionPreference = "Stop"

Write-Host "🛑 Stopping GameFlex Development Backend..." -ForegroundColor Red

# Check if Docker is running
try {
    docker info | Out-Null
} catch {
    Write-Host "❌ Docker is not running. Services may already be stopped." -ForegroundColor Yellow
    exit 0
}

# Check if docker-compose.yml exists
if (!(Test-Path "docker-compose.yml")) {
    Write-Host "❌ docker-compose.yml not found. Make sure you're in the backend directory." -ForegroundColor Red
    exit 1
}

try {
    if ($RemoveData) {
        Write-Host "⚠️  WARNING: This will remove all data including database, storage, and logs!" -ForegroundColor Yellow -BackgroundColor Red
        $confirmation = Read-Host "Are you sure you want to continue? Type 'yes' to confirm"
        
        if ($confirmation -eq "yes") {
            Write-Host "🗑️  Stopping containers and removing volumes..." -ForegroundColor Yellow
            docker-compose down -v
            Write-Host "✅ All services stopped and data removed!" -ForegroundColor Green
        } else {
            Write-Host "❌ Operation cancelled." -ForegroundColor Yellow
            exit 0
        }
    } else {
        Write-Host "🐳 Stopping Docker containers..." -ForegroundColor Yellow
        docker-compose down
        Write-Host "✅ All services stopped!" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Error stopping services: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "You may need to stop containers manually:" -ForegroundColor Yellow
    Write-Host "  docker-compose down" -ForegroundColor White
    exit 1
}

# Show status
Write-Host ""
Write-Host "📊 Current Status:" -ForegroundColor Cyan
try {
    $containers = docker-compose ps --services 2>$null
    if ($containers) {
        Write-Host "   Running containers: " -NoNewline -ForegroundColor Gray
        $runningContainers = docker-compose ps --filter "status=running" --services 2>$null
        if ($runningContainers) {
            Write-Host "$($runningContainers.Count)" -ForegroundColor Yellow
        } else {
            Write-Host "0" -ForegroundColor Green
        }
    }
} catch {
    Write-Host "   All containers stopped" -ForegroundColor Green
}

Write-Host ""
Write-Host "🔧 Next Steps:" -ForegroundColor Magenta
Write-Host "   To start again: " -NoNewline -ForegroundColor Gray
Write-Host ".\start.ps1" -ForegroundColor White

if (-not $RemoveData) {
    Write-Host "   To remove all data: " -NoNewline -ForegroundColor Gray
    Write-Host ".\stop.ps1 -RemoveData" -ForegroundColor White
}

Write-Host "   To view logs: " -NoNewline -ForegroundColor Gray
Write-Host "docker-compose logs" -ForegroundColor White
Write-Host ""
