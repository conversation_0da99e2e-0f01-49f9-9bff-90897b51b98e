//
//  PageViewController.swift
//  GameFlex
//
//  Created by <PERSON> on 9/26/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

class PageViewController: GFViewController {
    
    @IBOutlet weak var imageView: UIImageView!
    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var messageLabel: UILabel!
    
    var page: Pages = .pageZero
    
    static func storyboardInstance() -> PageViewController {
        let sb = UIStoryboard(name: "Main", bundle: nil)
        return sb.instantiateViewController(withIdentifier: String(describing: PageViewController.self)) as! PageViewController
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .clear
        imageView.image = page.image
        titleLabel.text = page.name
        messageLabel.text = page.message
        imageView.layer.borderWidth = 5
        imageView.layer.borderColor = UIColor.black.cgColor
        imageView.layer.cornerRadius = 125.0

    }
}
