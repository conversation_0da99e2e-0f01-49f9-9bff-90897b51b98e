//
//  GFError.swift
//  GameFlex
//
//  Created by <PERSON> on 9/7/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import Foundation

enum GFErrorType {
    case failedURLPut, failedToEncodeJSON, unknownError, rekognitionFailed, cantRegisterExistingAccount, contentNotFound, flawedURL
}

struct GFError: LocalizedError {
    
    var errorType: GFErrorType?
    var errorCode: Int? {
        switch self.errorType {
        case .failedURLPut:                 return 499
        case .failedToEncodeJSON:           return 599
        case .unknownError:                 return 999
        case .rekognitionFailed:            return 699
        case .cantRegisterExistingAccount:  return 799
        case .contentNotFound:              return 404
        case .flawedURL:                    return 498
        default:                            return 598
        }
    }
    
    var errorDescription: String? {
        switch self.errorType {
        case .failedURLPut: return "The url of this flex is invalid or is not available."
        case .failedToEncodeJSON: return "The data object seems to be malformed."
        case .unknownError: return "Check the code for this error."
        case .rekognitionFailed: return "We weren't able to quality check this flex."
        case .cantRegisterExistingAccount: return "This account already exists. Try login."
        case .contentNotFound: return "We have no record of that."
        case .flawedURL: return "Bad url"
        default: return "unknown"
        }
    }
    
    var failureReason: String? {
        switch self.errorType {
        case .failedURLPut: return "Bad media. Server issue. Bad network."
        case .failedToEncodeJSON: return "JSON serialization failed."
        case .unknownError: return "400 = malformed json."
        case .rekognitionFailed: return "Rekognition returned nil response."
        case .cantRegisterExistingAccount: return "Register endpoint is for first time accounts only."
        case .contentNotFound: return "There is no record of that."
        case .flawedURL: return "URL string was empty. Did not attempt call."
        default: return "unknown"
        }
    }
    
    var recoverySuggestion: String? {
        switch self.errorType {
        case .failedURLPut:
            return "Try again later. If error persists, reach out to Support for assistance."
        case .failedToEncodeJSON: return "Try again later."
        case .unknownError: return ""
        case .rekognitionFailed: return "Try again later."
        case .cantRegisterExistingAccount: return "Try login."
        case .contentNotFound: return "Add content, then try again."
        case .flawedURL: return "Add a real URL string and try again. This was a fail safe mechanism."
        default: return "unknown"
        }
    }
}
