//
//  AppDelegate.swift
//  GameFlex
//
//  Created by <PERSON> on 7/5/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import UserNotifications
import BackgroundTasks

import Firebase
import FirebaseMessaging
import GoogleSignIn
import AWSCore
import <PERSON>fisher

@UIApplicationMain
class AppDelegate: UIResponder, UIApplicationDelegate {
    
    var window: UIWindow?
    var instanceIDTokenMessage: String = ""
    
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        // Override point for customization after application launch.
        FirebaseApp.configure()
        
        Messaging.messaging().delegate = self

        GIDSignIn.sharedInstance().clientID = FirebaseApp.app()?.options.clientID
           
        UNUserNotificationCenter.current().delegate = self
        
        UIApplication.shared.applicationIconBadgeNumber = 0
        window = UIWindow()
        window?.frame = UIScreen.main.bounds
        let vc = OnboardingStateMachine.processOnboardState(at: nil)
        window?.rootViewController = vc
        vc.modalTransitionStyle = .crossDissolve
        window?.makeKeyAndVisible()
        SafeAreaFixTabBar.appearance().tintColor =  .gfGreen
        SafeAreaFixTabBar.appearance().barTintColor = .gfDarkBackground100
        SafeAreaFixTabBar.appearance().isTranslucent = false
        let barButtonItemAppearance = UIBarButtonItem.appearance()
        barButtonItemAppearance.setTitleTextAttributes([NSAttributedString.Key.foregroundColor: UIColor.clear], for: .normal)

        let credentialsProvider = AWSCognitoCredentialsProvider(regionType:.USEast2,
          identityPoolId:"us-east-2:5a21fb57-80cb-4a26-99bf-ac62e38b1f82")
        let configuration = AWSServiceConfiguration(region:.USEast2, credentialsProvider:credentialsProvider)
        AWSServiceManager.default().defaultServiceConfiguration = configuration

        updateTheUserSingleton()
        OnboardingStateMachine.didComplete(this: .none)

        ImageCache.default.memoryStorage.config.countLimit = 1000 //totalCostLimit = 300 * 1024 * 1024

        registerBackgroundTasks()
        return true
    }
    
    func registerBackgroundTasks() {
        BGTaskScheduler.shared.register(forTaskWithIdentifier: "io.gameflex.getflexes", using: nil) { task in
            BackgroundTasks.handleGetFlexes(task: task as! BGAppRefreshTask)
            print("Background Task registered: \(task.identifier)")
            task.expirationHandler = {
              task.setTaskCompleted(success: false)
            }
        }

    }
    
    func scheduleBackgroundTasks() {
        BackgroundTasks.isCancelled = false
        let request = BGAppRefreshTaskRequest(identifier: "io.gameflex.getflexes")
        request.earliestBeginDate = Date(timeIntervalSinceNow: 15 * 60)
        do {
            try BGTaskScheduler.shared.submit(request)
        } catch {
            print("Could not schedule image fetch: \(error)")
        }
    }

    func application(_ application: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey : Any])
        -> Bool {
            return GIDSignIn.sharedInstance().handle(url)
    }
    
    func applicationWillEnterForeground(_ application: UIApplication) {
        UIApplication.shared.applicationIconBadgeNumber = 0
        updateTheUserSingleton()
    }
    
    func applicationDidEnterBackground(_ application: UIApplication) {
        scheduleBackgroundTasks()
        FeedViewModel.makeTheLastSeenCall()
    }
    
    private func updateTheUserSingleton() {
        if User.isLoggedIn, let userId = User.userId, userId != "" {
            // TODO: organize as an operationsQueue instead of nested APIs?
            GFNetworkServices.getUserProfile(userId) { (success, flexter, error) in
                if let flexter = flexter {
                    User.updateTheUser(flexter)
                }
                GFNetworkServices.getUserReactions { (success, likes, error) in
                    if let likes = likes {
                        GFDefaults.likedFlexes[userId] = likes
                    }
                }
            }
        }
    }
}

// MARK: - Push Notification Support
extension AppDelegate {

    /// Entry Point: App is in foreground or background and a push notification is received
    func application(_ application: UIApplication, didReceiveRemoteNotification userInfo: [AnyHashable: Any], fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
        // If you are receiving a notification message while your app is in the background,
        // this callback will not be fired till the user taps on the notification launching the application.
        UIApplication.shared.applicationIconBadgeNumber += 1
        completionHandler(UIBackgroundFetchResult.newData)
    }

    func application(_ application: UIApplication, didFailToRegisterForRemoteNotificationsWithError error: Error) {
        print("Unable to register for remote notifications: \(error.localizedDescription)")
    }

    /// Register for remote notifications. This shows a permission dialog on first run, to
    /// show the dialog at a more appropriate time move this registration accordingly.
    ///
    /// - Parameter application: `UIApplication` used to register for push notifications
    func registerForPushNotification(_ application: UIApplication) {
        UNUserNotificationCenter.current().delegate = self
        let authOptions: UNAuthorizationOptions = [.alert, .badge, .sound]
        UNUserNotificationCenter.current().requestAuthorization(options: authOptions, completionHandler: {_, _ in })
        DispatchQueue.main.async {
            application.registerForRemoteNotifications()
        }
    }

    /// Handle all incoming remote notifications
    ///
    /// - Parameter userInfo: Details of the incoming remote notification
    fileprivate func handlePushNotificationInfo(_ userInfo: [AnyHashable: Any], fromUserAction: Bool = false) {
        // Print full message.
        print("PUSH-NOTIF = \(userInfo)")
/*        let nicoNotif = NicoNotification.fromUserInfo(userInfo)

        // If user is already on the Activity screen and a chat message notification comes in,
        // do nothing, otherwise always show in-app notification
        if let topVC = UIApplication.topViewController() as? ActivityViewController, nicoNotif.notifType == .chat, (topVC.profileFilter?.id == nicoNotif.childId || topVC.profileFilter == nil) && (topVC.activityTypeFilter == .allUpdates || topVC.activityTypeFilter == .messages) {
            return
        }

        // If user is in the login flow, do nothing
        if let topVC = UIApplication.topViewController(), topVC is LoginViewController || topVC is RoleSelectionViewController {
            remoteNotif = nicoNotif
            return
        }

        // If user is not in the activity VC set up the red dot on activity tab bar
        if nicoNotif.deeplink == NicoNotification.Deeplink.activity, let topVC = UIApplication.topViewController(), !(topVC is ActivityViewController) {
            NotificationCenter.default.post(name: .NicoNavigateActivityLinkNotification, object: nil, userInfo: nil)
        }

        if fromUserAction {
            // if user is in Settings/PlanInfo, and action is for settings -> get profile and update ui
            if let topVC = UIApplication.topViewController(), topVC is PlanInfoViewController, nicoNotif.deeplink == .settings {
                remoteNotif = nicoNotif
                (topVC as! PlanInfoViewController).refreshUI()
                return
            }
            // if user is in Settings, and action is for settings -> get profile and pop to PlanInfo
            if let topVC = UIApplication.topViewController(), topVC is SettingsViewController, nicoNotif.deeplink == .settings {
                remoteNotif = nicoNotif
                (topVC as! SettingsViewController).didTapForSettingsAction(.showMyPlan)
                return
            }
            // Navigate to deep link if user came from tapping on a push notification
            NotificationCenter.default.post(name: .NicoNavigateDeeplinkNotification,
                                            object: nil,
                                            userInfo: [NicoNotification.kDeeplinkKey: nicoNotif.deeplink ?? ""])
            return
        }
        // Show in app notification if user did not come from tapping on a push notification
        NicoNotification.presentInAppNotification(nicoNotif)
*/
    }

}

// MARK: - UNUserNotificationCenterDelegate
extension AppDelegate: UNUserNotificationCenterDelegate {

    // Entry point: App is in foreground and receives a remote notification
    func userNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        handlePushNotificationInfo(notification.request.content.userInfo, fromUserAction: false)
        completionHandler([.badge, .sound/*, .alert*/])
    }

    // Entry point: App is in background and user clicks on notification
    func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
        handlePushNotificationInfo(response.notification.request.content.userInfo, fromUserAction: true)
        completionHandler()
    }

    func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
        print("Device Token = \(deviceToken.map { String(format: "%02.2hhx", $0) }.joined())")
    }
}

// MARK: - MessagingDelegate
extension AppDelegate: MessagingDelegate {

    /// Handle device token registration/refresh with FCM
    ///
    /// - Parameters:
    ///   - messaging: FIRMessaging object
    ///   - fcmToken: New FCM token
    func messaging(_ messaging: Messaging, didReceiveRegistrationToken fcmToken: String) {
        // Note: This callback is fired at each app startup and whenever a new token is generated.
        print("Firebase registration token: \(fcmToken)")
        registerDevice()
    }

    func registerDevice() {
        guard let fcmToken = Messaging.messaging().fcmToken/*, NicoToken.shared.isLoggedIn()*/ else {
            print("Error: FCM Token or Access Token unavailable.")
            return
        }
        print("fcmToken = \(fcmToken)")
        User.fcmToken = fcmToken
    }
}

extension AppDelegate {
    
    static func disableIdleTimer(_ disable: Bool) {
        UIApplication.shared.isIdleTimerDisabled = disable
    }
}
