//
//  GFSpinnerView.swift
//  GameFlex
//
//  Created by <PERSON> on 9/7/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

/*USAGE:
 to Show the spinner:
 [GFSpinnerView showInView:[UIApplication sharedApplication].keyWindow];
 GFSpinnerView.showIn(view: UIViewController.keyWindow)
 
 to Hide the spinner:
 [GFSpinnerView hideInView:[UIApplication sharedApplication].keyWindow];
 SpinnerView.hideIn(view: UIViewController.keyWindow)
 
 */
import Foundation
import UIKit

enum GFSpinnerType {
    case message, flipper, box
}

class GFSpinnerView: UIView {
    
    static let kActivityIndTag = 54420
    var type: GFSpinnerType = .flipper
    var message: String?
    var time: TimeInterval?

    var isPresent: Bool = false
    
    /// presents a green box for 5 sec
    static func showAtTopOf(view: UIView) -> GFSpinnerView {
        let header = GFSpinnerView(frame: CGRect(x: 100.0, y: 100.0, width: 50.0, height: 50.0))
        header.layer.cornerRadius = 25.0
        header.backgroundColor = UIColor.gfGreen
        header.type = .box
        view.addSubview(header)
        return header

    }
    
    @objc static func showIn(view: UIView) {
        showIn(view: view, isRotating: false, tapToHide: false)
    }
    
    static func showIn(view: UIView, isRotating: Bool) {
        showIn(view: view, isRotating: isRotating, tapToHide: false)
    }
    
    /// it might be helpful to have a chance to tap out of the spinner - restore purchases for example
    static func showIn(view: UIView, isRotating: Bool, tapToHide:Bool) {
        DispatchQueue.main.async {
            if GFSpinnerView.hasSpinnerIn(view: view) && !isRotating {
                return
            }
            // rotate please
            let maxed = view.frame.size.height > view.frame.size.width ? view.frame.size.height : view.frame.size.width
            let maxFrame = CGRect(x: 0.0, y: 0.0, width: maxed, height: maxed)
            let spinnerView = GFSpinnerView(frame: maxFrame)
            spinnerView.type = .flipper
            if !UIAccessibility.isReduceTransparencyEnabled {
                let blurEffectView = UIVisualEffectView.init(effect: UIBlurEffect(style: .dark))
                blurEffectView.frame = spinnerView.frame //[UIApplication sharedApplication].keyWindow.bounds;
                blurEffectView.autoresizingMask = UIView.AutoresizingMask(rawValue: UIView.AutoresizingMask.flexibleWidth.rawValue | UIView.AutoresizingMask.flexibleHeight.rawValue)
                blurEffectView.alpha = 0.7
                blurEffectView.backgroundColor = UIColor.black
                spinnerView.addSubview(blurEffectView)
            } else {
                let background = UIView.init(frame: spinnerView.frame)
                background.backgroundColor = UIColor.black
                background.alpha = 0.7
                spinnerView.addSubview(background)
            }
            spinnerView.isPresent = true
            
            // add acitivityIndicator now
            let activityInd = UIImageView.init(frame: CGRect(x: 0.0, y: 0.0, width: 100.0, height: 100.0))
            activityInd.image =  #imageLiteral(resourceName: "appstore") //"icon_tpt")
            activityInd.layer.cornerRadius = 50
            spinnerView.addSubview(activityInd)
            activityInd.layer.masksToBounds = true
            activityInd.layer.cornerRadius = 10.0
            activityInd.center = (UIApplication.shared.keyWindow?.center)!
            activityInd.tag = GFSpinnerView.kActivityIndTag
            spinnerView.bringSubviewToFront(activityInd)
            DispatchQueue.main.async {
                self.flip(view: activityInd, spinner: spinnerView)
            }
            if tapToHide {
                let tap = UITapGestureRecognizer.init(target: spinnerView, action: #selector(hideIn(view:)))
                spinnerView.addGestureRecognizer(tap)
            }
            view.addSubview(spinnerView)
        }
    }
    
    static func flip(view: UIView, spinner: GFSpinnerView) {
        DispatchQueue.main.async {
            UIView.animate(withDuration: 1.0, animations: {
                view.transform = CGAffineTransform(scaleX: -1, y: 1)
            }, completion: { (_) in
                UIView.animate(withDuration: 1.0, animations: {
                    view.transform = CGAffineTransform.identity
                }, completion: { (_) in
                    if spinner.isPresent {
                        self.flip(view: view, spinner: spinner)
                    }
                })
            })
        }
    }
    
    static func show(navigationView: UIView) {
        showIn(view: navigationView)
    }
    
    @objc public static func hideIn(view: UIView?) {
        DispatchQueue.main.async {
            if let view = view {
                view.subviews.filter { ($0.isKind(of: GFSpinnerView.self)) }.forEach({$0.removeFromSuperview()})
            }
        }
    }
        
    
    static func hasSpinnerIn(view: UIView) -> Bool {
        let spinner = view.subviews.filter { ($0.isKind(of: GFSpinnerView.self)) }
        if spinner.count > 0 { return true }
        return false
    }
    
    fileprivate static func hide(spinner: GFSpinnerView) {
        DispatchQueue.main.async {
            UIView.animate(withDuration: 0.5,
                           delay: 0.0,
                           usingSpringWithDamping: 1.0,
                           initialSpringVelocity: 0.0,
                           options: [.curveEaseInOut, .beginFromCurrentState],
                           animations: {
                            spinner.alpha = 0.0
            }) { (_) in
                spinner.isPresent = false
                spinner.removeFromSuperview()
            }
        }
    }
    
    @objc fileprivate static func hideMe() {
        DispatchQueue.main.async {
            if let spinner = UIApplication.shared.keyWindow!.subviews.filter({ $0.isKind(of: GFSpinnerView.self )})[0] as? GFSpinnerView {
                UIView.animate(withDuration: 0.5,
                               delay: 0.0,
                               usingSpringWithDamping: 1.0,
                               initialSpringVelocity: 0.0,
                               options: [.curveEaseInOut, .beginFromCurrentState],
                               animations: {
                                spinner.alpha = 0.0
                               }) { (_) in
                    spinner.isPresent = false
                    spinner.removeFromSuperview()
                }
            }
        }
    }

    // In App Notification
    // used for processing Local and Remote Apple Push Notifications
    static func showIn(view: UIView, message: String, _ time: TimeInterval = 3.0) {
        if GFSpinnerView.hasSpinnerIn(view: view) {
            hideIn(view: view)
        }
        DispatchQueue.main.async {
            let spinnerView = GFSpinnerView(frame: CGRect(x: 20.0, y: view.frame.height/2 - 50.0, width: view.frame.size.width-40, height: 100.0))
            let blurEffect = UIBlurEffect(style: UIBlurEffect.Style.systemMaterialDark)
            let blurEffectView = UIVisualEffectView(effect: blurEffect)
            blurEffectView.frame = spinnerView.bounds
            blurEffectView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
            blurEffectView.tag = 9922
            spinnerView.addSubview(blurEffectView)
            spinnerView.sendSubviewToBack(blurEffectView)

            spinnerView.layer.cornerRadius = 4.0
            spinnerView.layer.borderColor = UIColor.gfGreen.cgColor
            spinnerView.layer.borderWidth = 2.0
            spinnerView.backgroundColor = UIColor.clear
            spinnerView.type = .message
            spinnerView.message = message
            let label = UILabel(frame: CGRect(x: 10.0, y: 10.0, width: spinnerView.frame.size.width-20, height: spinnerView.frame.size.height-20))
            if message.count > 0 {
                label.text = message
            } else {
                label.text = "It seems the Internet is not available at the moment.\nTry again later."
            }
            label.numberOfLines = 0
            label.textAlignment = .center
            label.textColor = .gfGreen
            if UIDevice.current.userInterfaceIdiom == .pad {
                label.font = .systemFont(ofSize: 28)
            } else {
                label.font = .systemFont(ofSize: 17)
            }
            spinnerView.addSubview(label)
            view.addSubview(spinnerView)
        perform(#selector(hideIn(view:)), with: view, afterDelay: time)
        }
    }
    
    /// triggered by changes in the orientation of the parent viewController class BeyondViewController
    static func orientationDidChange(view: UIView) {
        if GFSpinnerView.hasSpinnerIn(view: view) {
            if let spinner = view.subviews.filter({ $0.isKind(of: GFSpinnerView.self) })[0] as? GFSpinnerView {
                DispatchQueue.main.async {
                    switch spinner.type {
                    case .flipper:
                        self.showIn(view: view, isRotating: true, tapToHide: false)
                        spinner.removeFromSuperview()
                    case .box:
                        break
                    case .message:
                        self.showIn(view: UIApplication.shared.keyWindow!, message:spinner.message ?? "", spinner.time ?? 10.0)
                        spinner.removeFromSuperview()
                    }
                }
            }
        }
    }
}
