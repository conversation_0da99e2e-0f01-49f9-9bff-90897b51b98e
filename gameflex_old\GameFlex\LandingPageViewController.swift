//
//  LandingPageViewController.swift
//  GameFlex
//
//  Created by <PERSON> on 9/9/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

enum NextServiceType {
    case whatsNew, age, permissions, signup, intro, done
}

class LandingPageViewController: UITabBarController {

    var previousIndex = 0
    var flexIndex = 2
    var dob = false
    var isFromIntro = false

    // MARK: - Lifecycle

    static func storyboardInstance() -> LandingPageViewController {
        return UIStoryboard(name: "Main", bundle: nil).instantiateViewController(withIdentifier: String(describing: LandingPageViewController.self)) as! LandingPageViewController
    }

    override func present(_ viewControllerToPresent: UIViewController,
                          animated flag: Bool,
                          completion: (() -> Void)? = nil) {
        viewControllerToPresent.convertToFullScreen()
        super.present(viewControllerToPresent, animated: flag, completion: completion)
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        // Handle push notifications with a deeplink
//        NotificationCenter.default.addObserver(self,
//                                               selector: #selector(navigateWithDeeplink(_:)),
//                                               name: .NicoNavigateDeeplinkNotification,
//                                               object: nil)
        delegate = self
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        if isFromIntro {
            isFromIntro = false
            doTheSmoothScreen()
        }
        var controllers: [UIViewController] = []
        if let index = viewControllers?.firstIndex(where: { $0 is UINavigationController &&
            ($0 as! UINavigationController).viewControllers.contains(where: { $0 is HomeViewController }) }) {
            viewControllers![index].tabBarItem.image = #imageLiteral(resourceName: "navHome")
            viewControllers![index].tabBarItem.title = ""
            controllers.append(viewControllers?[index] ?? HomeViewController())
        }
        if let index = viewControllers?.firstIndex(where: { $0 is UINavigationController &&
            ($0 as! UINavigationController).viewControllers.contains(where: { $0 is AlertViewController }) }) {
            viewControllers![index].tabBarItem.image = #imageLiteral(resourceName: "navAlert")
            viewControllers![index].tabBarItem.title = ""
            controllers.append(viewControllers?[index] ?? AlertViewController())
        }
        if let index = viewControllers?.firstIndex(where: { $0 is UINavigationController &&
            ($0 as! UINavigationController).viewControllers.contains(where: { $0 is PreCameraViewController }) }) {
            flexIndex = controllers.count
            viewControllers![index].tabBarItem.title = ""
            let customTabBarItem:UITabBarItem = UITabBarItem(title: nil,
                                                             image: UIImage(named: "navCreate")?.withRenderingMode(UIImage.RenderingMode.alwaysOriginal),
                                                             selectedImage: UIImage(named: "navCreate_filled")?.withRenderingMode(UIImage.RenderingMode.alwaysOriginal))
            viewControllers![index].tabBarItem = customTabBarItem
            
            var im = viewControllers![index].tabBarItem.image
            im = im?.withTintColor(.gfGreen, renderingMode: .alwaysOriginal)

            viewControllers![index].tabBarItem.title = ""
            controllers.append(viewControllers?[index] ?? PreCameraViewController())
        }
        if let index = viewControllers?.firstIndex(where: { $0 is UINavigationController &&
            ($0 as! UINavigationController).viewControllers.contains(where: { $0 is SearchViewController }) }) {
            viewControllers![index].tabBarItem.image = #imageLiteral(resourceName: "navSearch")
            viewControllers![index].tabBarItem.title = ""
            controllers.append(viewControllers?[index] ?? SearchViewController())
        }
        if let index = viewControllers?.firstIndex(where: { $0 is UINavigationController &&
            ($0 as! UINavigationController).viewControllers.contains(where: { $0 is ProfileViewController }) }) {
            viewControllers![index].tabBarItem.image = #imageLiteral(resourceName: "navProfile")
            viewControllers![index].tabBarItem.title = ""
            controllers.append(viewControllers?[index] ?? ProfileViewController())
        }

        viewControllers = controllers
        selectedIndex = 0
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
    }
    
    private func doTheSmoothScreen() {
        let screen = UIView(frame: view.frame)
        screen.backgroundColor = .black
        screen.tag = 9912
        view.addSubview(screen)
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.25) {
            UIView.animate(withDuration: 0.5) {
                self.view.subviews.filter({$0.tag == 9912 }).forEach({ $0.alpha = 0.0})
            } completion: { (_) in
                self.view.subviews.filter({$0.tag == 9912 }).forEach({ $0.removeFromSuperview()})
            }

        }
    }

    // MARK: - Notification Handlers

    /// Update Tab bars for visual effect when a push notification and received and also handle any specific
    /// navigation that occurs resulting from an in-app notification
    ///
    /// - Parameter notification: `NSnotification` containing details of the push notification
    @objc func navigateWithDeeplink(_ notification: NSNotification) {
        // Handle any in-app navigation resulting from a push notification that has a deeplink
//        guard let deeplink = notification.userInfo?[NicoNotification.kDeeplinkKey] as? NicoNotification.Deeplink else {
//            print("Deeplink from push notification not available")
            return
        }

        // navigate to appropriate screen based on the deeplink received from push notification
//        switch deeplink {
//        case .activity:
//            if let index = viewControllers?.firstIndex(where: {
//                $0 is UINavigationController
//                    && ($0 as! UINavigationController).viewControllers.contains(where: { $0 is ActivityViewController }) }) {
//                selectedIndex = index
//                let activityVC = (viewControllers?[index] as? UINavigationController)?.topViewController as? ActivityViewController
//                activityVC?.tabBarController?.tabBar.isHidden = false
//            }
//
//        case .activityScheduled:
//            if let index = viewControllers?.firstIndex(where: {
//                $0 is UINavigationController
//                    && ($0 as! UINavigationController).viewControllers.contains(where: { $0 is ActivityViewController }) }) {
//                let activityVC = (viewControllers?[index] as? UINavigationController)?.topViewController as? ActivityViewController
//                activityVC?.tabBarController?.tabBar.isHidden = false
//                selectedIndex = index
//                // broadcast the associated action so interested `ViewController`s can take respective action
//                NotificationCenter.default.post(name: .NicoNavigateDeeplinkWithActionNotification,
//                                                object: nil,
//                                                userInfo: [NicoNotification.kDeeplinkKey: deeplink])
//            }
//
//        case .overview, .overviewWallet, .verifyBankAccount, .addMoney, .settings:
//            processOverviewCases(deeplink)
//        case .task:
//            if let index = viewControllers?.firstIndex(where: {
//                $0 is UINavigationController
//                    && ($0 as! UINavigationController).viewControllers.contains(where: { $0 is TasksTableViewController }) }) {
//                selectedIndex = index
//            }
//        }
    
}

extension LandingPageViewController: UITabBarControllerDelegate {
    func tabBarController(_ tabBarController: UITabBarController, didSelect viewController: UIViewController) {
        
        // tapped home button while in home to move to top of feed
        if selectedIndex == previousIndex, previousIndex == 0 {
            if let home = (tabBarController.viewControllers?[selectedIndex] as? UINavigationController)?.viewControllers[0] as? HomeViewController {
                home.didTapForChannelAction(.goToTop)
                return
            }
        }
        
        // tapped another tab while in home/channels -> make the lastSeen call
        if previousIndex == 0, selectedIndex != previousIndex {
            if let home = (tabBarController.viewControllers?[previousIndex] as? UINavigationController)?.viewControllers[0] as? HomeViewController {
                if let _ = home.children[0] as? ChannelViewController {
                    FeedViewModel.makeTheLastSeenCall()
                }
            }
        }
        
        // last one
        if selectedIndex == (viewControllers?.count ?? 5) - 1 {
            if !User.isLoggedIn {
                selectedIndex = 0
                return
            }
        }
        
        if selectedIndex == 0, User.isLoggedIn {
            if let home = (tabBarController.viewControllers?[selectedIndex] as? UINavigationController)?.viewControllers[0] as? HomeViewController {
                home.handleTuple(FeedViewModel.lastSeenTuple)
            }
        }
        
        if selectedIndex == flexIndex {
            if GFDefaults.shared.hasAskedCameraPermission && GFDefaults.shared.hasAskedLibraryPermission /*&& GFDefaults.shared.hasAskedMicrophonePermission*/ && GFDefaults.shared.hasAskedPushPermission {
                let cvc = CameraViewController.storyboardInstance()
                CameraViewModel.state = .buttons
                (tabBarController.viewControllers?[flexIndex] as? GFNavigationController)?.pushViewController(cvc, animated: true)
                return
            }
            let ttfvc = TryingToFlexViewController.storyboardInstance()
            ttfvc.delegate = self
            present(ttfvc, animated: true)
        }
            return
    }

    func tabBarController(_ tabBarController: UITabBarController, shouldSelect viewController: UIViewController) -> Bool {
        previousIndex = selectedIndex // set the existing index
        return true
    }
}

extension LandingPageViewController: PermissionsDelegate {
    func didFinishWithPermissions() {
        if GFDefaults.shared.hasAskedCameraPermission && GFDefaults.shared.hasAskedLibraryPermission && /*GFDefaults.shared.hasAskedMicrophonePermission &&*/ GFDefaults.shared.hasAskedPushPermission {
            OnboardingStateMachine.didComplete(this: .permissions)
        }
    }
}
