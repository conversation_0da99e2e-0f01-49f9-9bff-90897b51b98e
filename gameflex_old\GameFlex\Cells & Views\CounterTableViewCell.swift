//
//  CounterTableViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 1/14/21.
//  Copyright © 2021 GameFlex. All rights reserved.
//

import UIKit

class CounterTableViewCell: UITableViewCell {
    
    static var cellIdentifier = String(describing: CounterTableViewCell.self)
    
    @IBOutlet weak var collectionView: UICollectionView!
    var tableViewWidth: CGFloat = 325.0
    var thickLineMaxLength: CGFloat = 0
    var numberOfReflexes = 1
    
    override func awakeFromNib() {
        super.awakeFromNib()
        collectionView.dataSource = self
        collectionView.delegate = self
        collectionView.register(UINib(nibName: CounterCollectionViewCell.cellIdentifier, bundle: nil), forCellWithReuseIdentifier: CounterCollectionViewCell.cellIdentifier)
    }
}

extension CounterTableViewCell: UICollectionViewDataSource, UICollectionViewDelegate, UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return numberOfReflexes
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: CounterCollectionViewCell.cellIdentifier, for: indexPath) as? CounterCollectionViewCell else { return CounterCollectionViewCell() }
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, insetForSectionAt section: Int) -> UIEdgeInsets {
        return UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat {
        return 0.0
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let max = tableViewWidth/CGFloat(collectionView.numberOfItems(inSection: 0))
        thickLineMaxLength = max - 2.0
        return CGSize(width: max, height: 5.0)
    }
    
}
