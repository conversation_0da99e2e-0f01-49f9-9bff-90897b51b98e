//
//  BugViewController.swift
//  GameFlex
//
//  Created by <PERSON> on 8/5/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import Firebase
import SafariServices

class BugViewController: GFViewController {
    
    @IBOutlet weak var headerLabel: UILabel!
    @IBOutlet weak var textView: UITextView!
    @IBOutlet weak var submitButton: UIButton!
    @IBOutlet weak var attachButton: UIButton!
    @IBOutlet weak var progressView: UIView!
    @IBOutlet weak var progressBar: UIView!
    @IBOutlet weak var progressBarWidthConstraint: NSLayoutConstraint!
    @IBOutlet weak var screenshotButton: UIButton! // https://www.youtube.com/watch?v=yCHiLTB2y24
    @IBOutlet weak var screenMovieButton: UIButton! // https://www.youtube.com/watch?v=XJdZKSE4jX4
    
    var mediaData: Data?
    var ending: String = ".png"
    var preRecordedMessage: String?
    
    let storage = Storage.storage()
    
    static func storyboardInstance() -> BugViewController {
        let sb = UIStoryboard(name: "Main", bundle: nil)
        return sb.instantiateViewController(withIdentifier: String(describing: BugViewController.self)) as! BugViewController
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        progressView.isHidden = true
        progressView.layer.cornerRadius = 1
        progressBar.backgroundColor = .gfGreen
        headerLabel.text = "Be as descriptive as you can. Attach a screenShot or a screenMovie. Mark it up."
        headerLabel.font = .systemFont(ofSize: 14)
        textView.layer.cornerRadius = 8
        textView.text = "bugs.placeholder".localized
        textView.textColor = .lightGray
            attachButton.setTitleColor(.gfGreen, for: .normal)
        submitButton.setTitleColor(.black, for: .normal)
        submitButton.backgroundColor = .gfGreen
        textView.delegate = self
        hideKeyboardWhenTappedAround()
        textView.addDoneButtonToKeyboard()
        textView.inputAccessoryView?.subviews.forEach({ $0.tintColor = .gfGreen })
        submitButton.setTitle("Submit", for: .normal)
        submitButton.titleLabel?.font = .boldSystemFont(ofSize: 24)
        submitButton.layer.cornerRadius = 4
        submitButton.layer.borderColor = UIColor.gfGreen.cgColor
        submitButton.layer.borderWidth = 1
        screenshotButton.setTitle("ScreenShot", for: .normal)
        screenMovieButton.setTitle("ScreenMovie", for: .normal)
        screenMovieButton.setTitleColor(.gfGreen, for: .normal)
        screenshotButton.setTitleColor(.gfGreen, for: .normal)
        screenMovieButton.layer.borderColor = UIColor.gfGreen.cgColor
        screenMovieButton.layer.borderWidth = 0.25
        screenMovieButton.layer.cornerRadius = 4
        screenshotButton.layer.borderColor = UIColor.gfGreen.cgColor
        screenshotButton.layer.borderWidth = 0.25
        screenshotButton.layer.cornerRadius = 4
        attachButton.layer.borderColor = UIColor.gfGreen.cgColor
        attachButton.layer.borderWidth = 0.25
        attachButton.layer.cornerRadius = 4
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        title = "Report"
        if preRecordedMessage != nil, textView != nil {
            textView.text = preRecordedMessage
        }
        tabBarController?.tabBar.isHidden = true
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        tabBarController?.tabBar.isHidden = false
    }
    
    @IBAction func didTapButton(_ sender: UIButton)  {
        switch sender {
        case submitButton:
            if !textView.text.contains("bugs.placeholder".localized), !textView.text.isEmpty {
                sendIt()
            }
            
        case attachButton:
            getImage(fromSourceType: .photoLibrary)
        case screenshotButton:
            didTapMovie("https://www.youtube.com/watch?v=yCHiLTB2y24")
        case screenMovieButton:
            didTapMovie("https://www.youtube.com/watch?v=XJdZKSE4jX4")
        default: break
        }
    }
    
    func didTapMovie(_ movie: String) {
        if let movieURL = URL(string: movie) {
            let svc = SFSafariViewController(url: movieURL)
            svc.preferredBarTintColor = .black
            svc.preferredControlTintColor = .gfGreen
            navigationController?.pushViewController(svc, animated: true)
        }
    }
    
    private func sendIt() {
        if let data = self.mediaData {
            let storageRef = self.storage.reference()
            self.progressView.isHidden = false
            let dateString = String(format: "%.0f", Date().timeIntervalSince1970)
            let imRef = storageRef.child("flexerImages/\(dateString)\(ending)")
            let uploadTask = imRef.putData(data, metadata: nil) { (metadata, error) in
                guard metadata != nil else {
                    print(error?.localizedDescription as Any)
                    return
                }
            }
            
            uploadTask.observe(.progress) { snapshot in
                let complete = CGFloat(snapshot.progress!.completedUnitCount) / CGFloat(snapshot.progress!.totalUnitCount)
                print(complete)
                DispatchQueue.main.async {
                    self.progressBarWidthConstraint.constant = self.progressView.frame.width * (1 - complete)
                }
                if complete == 1.0 {
                    // Fetch the download URL
                    imRef.downloadURL { url, error in
                        if error != nil {
                            // Handle any errors
                        } else {
                            print(url?.absoluteString as Any)
                            self.sendToSlack(url: url?.absoluteString)
                        }
                    }
                    DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
                        self.progressView.isHidden = true
                    }
                }
            }
        } else {
            sendToSlack(url: nil)
        }

    }
    
    private func sendToSlack(url: String?) {
        GFFirebaseNetworking.sendFlexerFeedback(message: self.textView.text, mediaLink: url) { (success, error) in
            if success {
                let alert = UIAlertController(title: "gotit".localized, message: "bugs.alertMessage".localized, preferredStyle: .alert)
                let action = UIAlertAction(title: "OK", style: .default) { (ok) in
                    self.navigationController?.popViewController(animated: true)
                }
                alert.addAction(action)
                DispatchQueue.main.async {
                    self.present(alert, animated: true, completion: nil)
                    self.clearTheDeck()
                }
            }
        }
    }
    
    private func clearTheDeck() {
        mediaData = nil
        ending = ".png"
        textView.text = "bugs.placeholder".localized
        textView.textColor = .lightGray
    }
}

extension BugViewController: UITextViewDelegate {
    
    func textViewDidBeginEditing(_ textView: UITextView) {
        if textView.textColor == .lightGray {
            textView.text = nil
            textView.textColor = .white
        }
    }
    
    func textViewDidEndEditing(_ textView: UITextView) {
        if textView.text.isEmpty {
            textView.text = "bugs.placeholder".localized
            textView.textColor = .lightGray
        }
    }
}

// MARK: - ImagePickerControllerDelegate services

extension BugViewController: UIImagePickerControllerDelegate, UINavigationControllerDelegate {
    //get image from source type
    private func getImage(fromSourceType sourceType: UIImagePickerController.SourceType) {
        
        //Check is source type available
        if UIImagePickerController.isSourceTypeAvailable(sourceType) {
            let imagePickerController = UIImagePickerController()
            imagePickerController.delegate = self
            imagePickerController.sourceType = sourceType
            imagePickerController.mediaTypes = UIImagePickerController.availableMediaTypes(for: .photoLibrary)! // enables movies too
            self.present(imagePickerController, animated: true, completion: nil)
        }
    }
    
    //MARK:- UIImagePickerViewDelegate.
    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
        if info[UIImagePickerController.InfoKey.mediaURL] != nil {
            ending = ".\((info[UIImagePickerController.InfoKey.mediaURL] as! URL).pathExtension)"
        } else {
            ending = ".png"
        }
        self.dismiss(animated: true) { [weak self] in

            if let image = info[UIImagePickerController.InfoKey.originalImage] as? UIImage {
                self?.mediaData = image.pngData()
            } else {
                if let url = info[UIImagePickerController.InfoKey.mediaURL] as? URL {
                    self?.mediaData = try? Data(contentsOf: url, options: Data.ReadingOptions.alwaysMapped)
                }
            }
        }
    }
    
    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
        picker.dismiss(animated: true, completion: nil)
//        if photoButton != nil {
//            didTapButton(photoButton!)
//        }
    }
}
