//
//  CounterCollectionViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 1/14/21.
//  Copyright © 2021 GameFlex. All rights reserved.
//

import UIKit

class CounterCollectionViewCell: UICollectionViewCell {
    
    @IBOutlet weak var thinLine: UIView!
    @IBOutlet weak var thickLine: UIView!
    @IBOutlet weak var thickLineWidthConstraint: NSLayoutConstraint!
    
    static var cellIdentifier = String(describing: CounterCollectionViewCell.self)
    
    override func awakeFromNib() {
        super.awakeFromNib()
        thickLine.backgroundColor = .gfGreen
        thickLineWidthConstraint.constant = 0
    }
    
    func animateThis(_ max: CGFloat) {
        self.thickLineWidthConstraint.constant = max
        UIView.animate(withDuration: AppInfo.reflexTime) {
            self.layoutIfNeeded()
        }
    }
    
    func animated(_ max: CGFloat) {
        self.thickLineWidthConstraint.constant = max
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        thickLineWidthConstraint.constant = 0
    }
    
}
