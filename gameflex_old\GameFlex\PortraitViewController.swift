//
//  PortraitViewController.swift
//  GameFlex
//
//  Created by <PERSON> on 11/14/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import AVKit

enum FlashMode {
    case on, off, auto
}

class PortraitViewController: G<PERSON><PERSON>iewController, AVCapturePhotoCaptureDelegate {
    
    @IBOutlet weak var photoPreviewImageView: UIImageView!
    @IBOutlet weak var photoContainerView: UIView!
    @IBOutlet weak var flexBackgroundView: UIView!
    @IBOutlet weak var photoButton: UIButton!
    @IBOutlet weak var libraryButton: UIButton!
    @IBOutlet weak var bottomButtonView: UIView!
    @IBOutlet weak var previewView: UIView!
    @IBOutlet weak var collectionView: UICollectionView!
    
    @IBOutlet weak var undoButton: UIButton!
    @IBOutlet weak var doneButton: UIButton!

    var captureSession: AVCaptureSession!
    var stillImageOutput: AVCapturePhotoOutput!
    var videoPreviewLayer: AVCaptureVideoPreviewLayer!
    
    var hideableComponents: [UIView] = []
    
    var discoveredCameras: [CameraFaceOption: AVCaptureDevice] = [:]
    var currentCamera: CameraFaceOption = .back
    var currentInput: AVCaptureDeviceInput?
    var lastLocation: CGPoint = CGPoint.zero
    var previewCenterOffset: CGPoint = CGPoint.zero // add each of x and y this to get the center
    var settings: AVCapturePhotoSettings? = AVCapturePhotoSettings(format: [AVVideoCodecKey: AVVideoCodecType.jpeg])
    var transformAd: CGFloat = 1.0
    var flashMode: FlashMode = .off
    var isFromChannels = false // enables UI and image posting variation
    
    let maskTag = 8812345
    
    // MARK: - Lifecycle
    
    static func storyboardInstance() -> PortraitViewController {
        let sb = UIStoryboard(name: "Main", bundle: nil)
        return sb.instantiateViewController(withIdentifier: String(describing: PortraitViewController.self)) as! PortraitViewController
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        addNextToNavBar()
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(UINib(nibName: CameraControlsCollectionViewCell.cellIdentifier, bundle: nil), forCellWithReuseIdentifier: CameraControlsCollectionViewCell.cellIdentifier)
        self.photoButton.setTitle("camera.photo".localized, for: .normal)
        self.libraryButton.setTitle("camera.library".localized, for: .normal)
        self.photoButton.setTitleColor(.white, for: .normal)
        self.libraryButton.setTitleColor(.gfGrayText, for: .normal)
        doGestureSet(photoPreviewImageView)
        previewView.enableZoom()
        doneButton.isHidden = true
        undoButton.isHidden = true
        doneButton.setTitleColor(.gfGreen, for: .normal)
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
        title = isFromChannels ? "Channel Picture" : "Portrait"
    }
      
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        setupTheCamera()
        self.navigationController?.interactivePopGestureRecognizer?.isEnabled = false
        Utilities.hideSpinner()
        doTheMask()
        view.bringSubviewToFront(collectionView)
        view.bringSubviewToFront(bottomButtonView)
        self.navigationItem.rightBarButtonItems = []
    }
        
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        if photoPreviewImageView != nil {
            photoPreviewImageView.image = nil
        }
        if !UIDevice.isSimulator, self.captureSession != nil {
            self.captureSession.stopRunning()
        }
    }
    
    func setupTheCamera() {
        if !UIDevice.isSimulator {
            checkAuthorizationStatus(.camera) {success,_ in
                if success {
                    DispatchQueue.main.async {
                        self.captureSession = AVCaptureSession()
                        self.captureSession.sessionPreset = .high
                        self.discoveredCameras[CameraFaceOption.back] = AVCaptureDevice.DiscoverySession(deviceTypes: [AVCaptureDevice.DeviceType.builtInWideAngleCamera], mediaType: .video, position: .back).devices[0]
                        self.discoveredCameras[CameraFaceOption.front] = AVCaptureDevice.DiscoverySession(deviceTypes: [AVCaptureDevice.DeviceType.builtInWideAngleCamera], mediaType: .video, position: .front).devices[0]
                        
                        self.stillImageOutput = AVCapturePhotoOutput()
                        if self.discoveredCameras[.back] != nil {
                            if let input = try? AVCaptureDeviceInput(device: self.discoveredCameras[.back]!) {
                                if self.captureSession.canAddInput(input) && self.captureSession.canAddOutput(self.stillImageOutput) {
                                    self.currentCamera = .back
                                    self.currentInput = input
                                    self.captureSession.addInput(input)
                                    self.captureSession.addOutput(self.stillImageOutput)
                                    self.setupLivePreview()
                                }
                            }
                        } else {
                            // alert about permissions being in settings
                            self.showGoToSettingsAlert([.camera])
                        }
                    }
                } else {
                    DispatchQueue.main.async {
                        self.showGoToSettingsAlert([.camera])
                    }
                }
            }
        }
        if UIDevice.isSimulator {
            if let but = libraryButton {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    self.didTapButton(but)
                }
            }
        }
    }
    
    // MARK: - Camera operations

    func setupLivePreview() {
        videoPreviewLayer = AVCaptureVideoPreviewLayer(session: captureSession)
        videoPreviewLayer.videoGravity = .resizeAspect
        videoPreviewLayer.connection?.videoOrientation = .portrait
        if previewView.layer.sublayers?.count ?? 0 > 0 {
            previewView.layer.sublayers = []
        }
        previewView.layer.addSublayer(videoPreviewLayer)
        startTheCamera()
    }
    
    func startTheCamera() {
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            self?.captureSession.startRunning()
        }
        DispatchQueue.main.async {
            self.videoPreviewLayer.frame = self.previewView.bounds
            self.hideableComponents.forEach({ $0.isHidden = false })
        }
    }
    
    func stopTheCamera() {
        DispatchQueue.main.async {
            self.photoButton.setTitle("camera.photo".localized, for: .normal)
            self.hideableComponents.forEach({ $0.isHidden = true })
        }
    }
    
    
    @IBAction func didTapButton(_ sender: Any) {
        switch sender as? UIButton {
        case photoButton:
            photoButton.setTitleColor(.white, for: .normal)
            photoButton.titleLabel?.font = .boldSystemFont(ofSize: 15)
            libraryButton.titleLabel?.font = .systemFont(ofSize: 15)
            libraryButton.setTitleColor(.gfGrayText, for: .normal)
            
        case libraryButton:
            checkAuthorizationStatus(.library) {success,_ in
                if success {
                    DispatchQueue.main.async {
                        self.libraryButton.titleLabel?.font = .boldSystemFont(ofSize: 15)
                        self.photoButton.titleLabel?.font = .systemFont(ofSize: 15)
                        self.photoButton.setTitleColor(.gfGrayText, for: .normal)
                        self.libraryButton.setTitleColor(.white, for: .normal)
                        self.getImage(fromSourceType: .photoLibrary)
                    }
                } else {
                    DispatchQueue.main.async {
                        self.showGoToSettingsAlert([.library])
                    }
                }
            }
        case undoButton:
            doTheMask()
            photoPreviewImageView.isHidden = false
            view.sendSubviewToBack(photoPreviewImageView)
            undoButton.isHidden = true
            doneButton.isHidden = true
            view.bringSubviewToFront(collectionView)
            view.bringSubviewToFront(bottomButtonView)
            previewView.isHidden = false
        case doneButton:
            
            flexBackgroundView.clipsToBounds = true
            undoButton.isHidden = true
            doneButton.isHidden = true
            if let image = flexBackgroundView.capture() {
                
                Utilities.showSpinner()
                self.passToServer(image)
                return
                
            }

        default: break
        }
    }
        
    // MARK: - Gesture Recognizers
    // consolidates gesture assignment here
    func doGestureSet(_ object: UIView) {
        object.gestureRecognizers?.removeAll()
        object.enableZoom()
        let rotate = UIRotationGestureRecognizer(target: self, action: #selector(handleRotate(_:)))
        object.addGestureRecognizer(rotate)
        let panRecognizer = UIPanGestureRecognizer(target:self, action:#selector(detectPan(_:)))
        object.addGestureRecognizer(panRecognizer)
        object.gestureRecognizers?.forEach({ $0.delegate = self })
    }
         
    @objc func detectPan(_ recognizer:UIPanGestureRecognizer) {
        let translation  = recognizer.translation(in: view)
        photoPreviewImageView.center = CGPoint(x: lastLocation.x + translation.x + previewCenterOffset.x,
                                               y: lastLocation.y + translation.y + previewCenterOffset.y)
    }
    
    @objc private func handleRotate(_ sender: UIRotationGestureRecognizer) {
        if let view = sender.view {
            view.transform = view.transform.rotated(by: sender.rotation)
            sender.rotation = 0
        }
    }
        
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        if touches.first?.location(in: view) != nil {
            if let pointed = touches.first?.location(in: photoPreviewImageView) {
                let offsetX = pointed.x - photoPreviewImageView.center.x
                let offsetY = pointed.y - photoPreviewImageView.center.y
                previewCenterOffset = CGPoint(x: -offsetX, y: -offsetY)
                lastLocation = pointed
            }
        }
    }
    
    func doTheMask() {
        view.subviews.filter({ $0.tag == maskTag }).forEach({ $0.removeFromSuperview()})
        let fr = CGRect(origin: CGPoint.zero, size: flexBackgroundView.frame.size)
        let blackView = UIView(frame: fr)
        let maskLayer = CAShapeLayer() //create the mask layer

        // Set the radius to 1/3 of the screen width
        let radius : CGFloat = fr.width/2

        // Create a path with the rectangle in it.
        let path = UIBezierPath(rect: fr)
        // Put a circle path in the middle
        path.addArc(withCenter: photoPreviewImageView.center, radius: radius, startAngle: 0.0, endAngle: 2 * .pi, clockwise: true)

        // Give the mask layer the path you just drew
        maskLayer.path = path.cgPath
        // Fill rule set to exclude intersected paths
        maskLayer.fillRule = CAShapeLayerFillRule.evenOdd

        // By now the mask is a rectangle with a circle cut out of it. Set the mask to the view and clip.
        blackView.layer.mask = maskLayer
        blackView.clipsToBounds = true

        blackView.alpha = 0.8
        blackView.backgroundColor = UIColor.black
        doGestureSet(photoPreviewImageView)
        blackView.isUserInteractionEnabled = false
        blackView.tag = maskTag
        photoContainerView.addSubview(blackView)
        collectionView.isHidden = true
        photoButton.isHidden = true
        libraryButton.isHidden = true
    }
    
    // finish the photo
    func photoOutput(_ output: AVCapturePhotoOutput, didFinishProcessingPhoto photo: AVCapturePhoto, error: Error?) {
        
        guard let imageData = photo.fileDataRepresentation()
            else { return }
        
        let image = UIImage(data: imageData)
        
        photoPreviewImageView.image = image
        photoPreviewImageView.transform = CGAffineTransform.init(a: transformAd, b: 0.0, c: 0.0, d: transformAd, tx: 0.0, ty: 0.0)
        doGestureSet(photoPreviewImageView)
        transformAd = 1.0
        stopTheCamera()
        doGestureSet(photoPreviewImageView)
        view.bringSubviewToFront(photoPreviewImageView)
        view.subviews.filter({ $0.tag == maskTag }).forEach({ self.view.bringSubviewToFront($0)})
        undoButton.isHidden = false
        doneButton.isHidden = false
        view.bringSubviewToFront(undoButton)
        view.bringSubviewToFront(doneButton)
    }

    
    func passToServer(_ image: UIImage) {// proves photo worked
        if isFromChannels {
            CreateChannelViewModel.profileImage = image
            Utilities.hideSpinner()
            navigationController?.popViewController(animated: true)
        } else {
            GFNetworkServices.uploadMediaToUserProfile(image: image) { (success, error) in
                DispatchQueue.main.async {
                    Utilities.hideSpinner()
                    if success {
                        self.successAlert()
                    } else {
                        self.successAlert(error)
                    }
                }
            }
        }
    }
    
    private func successAlert(_ error: Error? = nil) {
        if error != nil {
            DispatchQueue.main.async {
                let alert = UIAlertController(title: "Error", message: "\((error as? GFError)?.errorCode ?? 499) - \((error as? GFError)?.errorDescription ?? "unknown")", preferredStyle: .alert)
                let ok = UIAlertAction(title: "OK", style: .default)
                alert.addAction(ok)
            self.present(alert, animated: true)
            }
        }
        DispatchQueue.main.async {
            self.navigationController?.popViewController(animated: true)
        }
    }
}

// MARK: -
// MARK: - CameraDelegate service
extension PortraitViewController: CameraDelegate {
    func didTapForCameraAction(_ action: CameraActionType) {
        switch action {
        case .snapPicture:
            transformAd = previewView.transform.a
            if let avDevice = AVCaptureDevice.default(for: AVMediaType.video) {
                settings = Utilities.getSettings(camera: avDevice, flashMode: flashMode)
                if let settings = settings {
                    stillImageOutput.capturePhoto(with: settings, delegate: self)
                    view.subviews.filter({ $0.tag == self.maskTag}).forEach({ $0.alpha = 1.0})
                    previewView.isHidden = true
                }
            }
            
        case .toggleFlash:
            if let avDevice = AVCaptureDevice.default(for: AVMediaType.video) {
                settings = nil
                settings = AVCapturePhotoSettings(format: [AVVideoCodecKey: AVVideoCodecType.jpeg])
                if avDevice.hasFlash {
                    if flashMode == .auto {
                        settings?.flashMode = .on
                        flashMode = .on
                    } else if flashMode == .on {
                        settings?.flashMode = .off
                        flashMode = .off
                    } else if flashMode == .off {
                        settings?.flashMode = .auto
                        flashMode = .auto
                    }
                }
            }
            

        case .swapCamera:
            captureSession.stopRunning()
            captureSession.beginConfiguration()
            defer {captureSession.commitConfiguration()}
            
            switch currentCamera {
            case .front:
                if discoveredCameras[.back] != nil {
                    if let input = try? AVCaptureDeviceInput(device: discoveredCameras[.back]!) {
                        captureSession.removeInput(currentInput!)
                        captureSession.removeOutput(stillImageOutput)
                        if captureSession.canAddInput(input) {
                            self.currentCamera = .back
                            if currentInput != nil {
                                captureSession.addInput(input)
                                currentInput = input
                                captureSession.addOutput(stillImageOutput)
                                setupLivePreview()
                            }
                        }
                    }
                } else {
                    // alert about permissions being in settings
                }
            case .back:
                if discoveredCameras[.front] != nil {
                    if let input = try? AVCaptureDeviceInput(device: discoveredCameras[.front]!) {
                        captureSession.removeInput(currentInput!)
                        captureSession.removeOutput(stillImageOutput)
                        if captureSession.canAddInput(input) {
                            self.currentCamera = .front
                            if currentInput != nil {
                                captureSession.addInput(input)
                                currentInput = input
                                captureSession.addOutput(stillImageOutput)
                                setupLivePreview()
                            }
                        }
                    }
                } else {
                    // alert about permissions being in settings
                }
            }
        default: break
        }
        collectionView.reloadData()
    }
}

// MARK: - ImagePickerControllerDelegate services

extension PortraitViewController: UIImagePickerControllerDelegate, UINavigationControllerDelegate {
    //get image from source type
    private func getImage(fromSourceType sourceType: UIImagePickerController.SourceType) {
        
        //Check is source type available for photo Library
        if UIImagePickerController.isSourceTypeAvailable(sourceType) {
            let imagePickerController = UIImagePickerController()
            imagePickerController.delegate = self
            imagePickerController.sourceType = sourceType
            self.present(imagePickerController, animated: true, completion: nil)
        }
    }
    
    //MARK:- UIImagePickerViewDelegate - finish the photoLibrary-selected phone
    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
        
        self.dismiss(animated: true) { [weak self] in
            
            guard let image = info[UIImagePickerController.InfoKey.originalImage] as? UIImage else { return }
            //Setting image to your image view
            self?.photoPreviewImageView.image = image
            
            self?.transformAd = 1.0
            self?.stopTheCamera()
            if let preview = self?.photoPreviewImageView, let undo = self?.undoButton, let done = self?.doneButton {
                self?.view.bringSubviewToFront(preview)
                self?.view.subviews.filter({ $0.tag == self?.maskTag }).forEach({ self?.view.bringSubviewToFront($0)})
                self?.undoButton.isHidden = false
                self?.doneButton.isHidden = false
                self?.view.bringSubviewToFront(undo)
                self?.view.bringSubviewToFront(done)
                self?.view.subviews.filter({ $0.tag == self?.maskTag}).forEach({ $0.alpha = 1.0})
                self?.previewView.isHidden = true
            }
            self?.stopTheCamera()
        }
    }
    
    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
        picker.dismiss(animated: true, completion: nil)
        if photoButton != nil {
            didTapButton(photoButton!)
            view.subviews.filter({ $0.tag == maskTag}).forEach({ $0.alpha = 0.8})
            previewView.isHidden = false
        }
    }
}

//MARK:- UIGestureRecognizerDelegate Methods
extension PortraitViewController: UIGestureRecognizerDelegate {
    
    func gestureRecognizer(_: UIGestureRecognizer, shouldRecognizeSimultaneouslyWith
        shouldRecognizeSimultaneouslyWithGestureRecognizer:UIGestureRecognizer) -> Bool {
        return true
    }

}

// MARK: - UICollectionViewCellDelegate, DataSource, DelegateFlowLayout services
extension PortraitViewController: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    
    func numberOfSections(in collectionView: UICollectionView) -> Int {
        return 1
    }
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
            return 1
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: CameraControlsCollectionViewCell.cellIdentifier, for: indexPath) as? CameraControlsCollectionViewCell else { return CameraControlsCollectionViewCell() }
        CameraViewModel.configureCell(cell: cell, type: .buttons)
        
        if let avDevice = AVCaptureDevice.default(for: AVMediaType.video) {
            if avDevice.hasFlash {
                if flashMode == .auto {
                    cell.flashButton.setImage(#imageLiteral(resourceName: "flashAuto"), for: .normal)
                } else if flashMode == .on {
                    var img = #imageLiteral(resourceName: "flashAuto")
                    if #available(iOS 13.0, *) {
                        img = #imageLiteral(resourceName: "flashAuto").withTintColor(.gfGreen, renderingMode: .alwaysTemplate)
                    } else {
                        // Fallback on earlier versions
                        img = img.withRenderingMode(.alwaysTemplate)
                        cell.flashButton.tintColor = .gfGreen
                    }
                    cell.flashButton.setImage(img, for: .normal)
                    cell.flashButton.tintColor = .gfGreen
                } else if flashMode == .off {
                    cell.flashButton.setImage(#imageLiteral(resourceName: "flashOff"), for: .normal)
                }
            }
        }
        cell.timerButton.isHidden = true
        cell.timerBackgroundButton.isHidden = true
        
        cell.delegate = self
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView,
                        layout collectionViewLayout: UICollectionViewLayout,
                        sizeForItemAt indexPath: IndexPath) -> CGSize {
            return CGSize(width: view.frame.size.width, height: 222.0)
    }
    
    func collectionView(_ collectionView: UICollectionView,
                        layout collectionViewLayout: UICollectionViewLayout,
                        insetForSectionAt section: Int) -> UIEdgeInsets {
        return UIEdgeInsets(top: 0.0, left: 0.0, bottom: 0.0, right: 0.0)
    }
}
