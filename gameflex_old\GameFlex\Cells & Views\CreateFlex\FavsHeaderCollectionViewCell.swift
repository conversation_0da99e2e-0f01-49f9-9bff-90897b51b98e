//
//  FavsHeaderCollectionViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 8/2/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

class FavsHeaderCollectionViewCell: UICollectionViewCell {
    
    @IBOutlet weak var label: UILabel!
    
    static let cellIdentifier = String(describing: FavsHeaderCollectionViewCell.self)

    override func awakeFromNib() {
        super.awakeFromNib()
        label.font = .systemFont(ofSize: 12)
        label.textColor = .gfGrayText
        
    }
}
