//
//  CommentTableViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 10/24/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import Kingfisher

class CommentTableViewCell: UITableViewCell {
    
    @IBOutlet weak var profileImageviewWidthConstraint: NSLayoutConstraint!
    @IBOutlet weak var commentTextView: UITextView!
    @IBOutlet weak var flexterName: UILabel!
    @IBOutlet weak var statsLabel: UILabel!
    @IBOutlet weak var dateTimeLabel: UILabel!
    @IBOutlet var profileImageView: UIImageView!
        
    weak var delegate: HomeDelegate?
    
    static var cellIdentifier = String(describing: CommentTableViewCell.self)
    
    override func awakeFromNib() {
        super.awakeFromNib()
        dateTimeLabel.isHidden = true
        statsLabel.textColor = .gfGrayText
        profileImageView.layer.cornerRadius = profileImageView.frame.size.width/2
        profileImageView.layer.borderWidth = 1
        profileImageView.layer.borderColor = UIColor.gfYellow_F2DE76.cgColor
        let objects = [profileImageView, dateTimeLabel, statsLabel, flexterName, commentTextView]
        objects.forEach({
            $0?.layer.shadowColor = UIColor.black.cgColor
            $0?.layer.shadowOpacity = 1.0
            $0?.layer.shadowOffset = CGSize(width: 2.0, height: 2.0)
            $0?.layer.shadowRadius = 2.0
        })
    }
    
    func configureCell(following: Bool = false, urlString: String?, flexterId: String?) {
        if profileImageView != nil, let urlString = urlString, let flexterId = flexterId {
            profileImageView.clipsToBounds = true
            let image = UIImage(named: FlexManager.randomImagePlaceholder(flexterId))
            let url = URL(string: urlString)
            let processor = DownsamplingImageProcessor(size: profileImageView.bounds.size)
            profileImageView?.kf.setImage(
                with: url,
                placeholder: image,
                options: [
                    .processor(processor),
                    .scaleFactor(UIScreen.main.scale),
                    .transition(.fade(1)),
                    .cacheOriginalImage
                ], completionHandler:
                    {
                        result in
                        switch result {
                        case .success:
                            self.profileImageviewWidthConstraint.constant = 40
                        case .failure(let error):
                            print("Job failed: \(error.localizedDescription)")
                            self.profileImageView?.image = image
                        }
                    })
        } else {
            profileImageviewWidthConstraint.constant = 0
        }
        profileImageView.layer.cornerRadius = profileImageView.frame.size.width/2
        profileImageView.layer.borderWidth = 1
        profileImageView.layer.borderColor = UIColor.gfYellow_F2DE76.cgColor

    }
        
    override func prepareForReuse() {
        super.prepareForReuse()
        profileImageView.image = nil
        profileImageView.layer.cornerRadius = profileImageView.frame.size.width/2
        profileImageView.layer.borderWidth = 1
        profileImageView?.layer.borderColor = UIColor.clear.cgColor
    }
    
}
