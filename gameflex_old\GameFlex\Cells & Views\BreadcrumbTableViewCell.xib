<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17703"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="iN0-l3-epB" customClass="BreadcrumbTableViewCell" customModule="GameFlex" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="414" height="49"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ZoG-wD-gjb">
                    <rect key="frame" x="10" y="4" width="154" height="44"/>
                    <subviews>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" insetsLayoutMarginsFromSafeArea="NO" translatesAutoresizingMaskIntoConstraints="NO" id="GmR-sb-FjR">
                            <rect key="frame" x="2" y="2" width="40" height="40"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="40" id="09Y-vz-yv8"/>
                                <constraint firstAttribute="height" constant="40" id="sP9-Zo-WPi"/>
                            </constraints>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                    <integer key="value" value="20"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="boolean" keyPath="clipsToBounds" value="YES"/>
                            </userDefinedRuntimeAttributes>
                        </imageView>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="M5A-cO-fQh">
                            <rect key="frame" x="2" y="2" width="40" height="40"/>
                            <connections>
                                <action selector="didTap:" destination="iN0-l3-epB" eventType="touchUpInside" id="7Oi-bm-kGF"/>
                            </connections>
                        </button>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="GameFlexter" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Mzq-HE-kyv">
                            <rect key="frame" x="50" y="13.5" width="88" height="17"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="17" id="6xb-yO-nxe"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="boldSystem" pointSize="14"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="qb1-Xb-dfb">
                            <rect key="frame" x="50" y="29" width="29" height="14"/>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="14" id="2lw-Yh-Toq"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="11"/>
                            <color key="textColor" white="0.66666666666666663" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="GmR-sb-FjR" firstAttribute="centerY" secondItem="ZoG-wD-gjb" secondAttribute="centerY" id="0g2-s8-dR4"/>
                        <constraint firstItem="M5A-cO-fQh" firstAttribute="top" secondItem="GmR-sb-FjR" secondAttribute="top" id="1dh-AR-GFs"/>
                        <constraint firstItem="M5A-cO-fQh" firstAttribute="trailing" secondItem="GmR-sb-FjR" secondAttribute="trailing" id="IGW-EE-Xdo"/>
                        <constraint firstItem="Mzq-HE-kyv" firstAttribute="centerY" secondItem="ZoG-wD-gjb" secondAttribute="centerY" id="TTp-eK-n7U"/>
                        <constraint firstItem="Mzq-HE-kyv" firstAttribute="leading" secondItem="GmR-sb-FjR" secondAttribute="trailing" constant="8" id="f7C-Qv-jCJ"/>
                        <constraint firstItem="GmR-sb-FjR" firstAttribute="leading" secondItem="ZoG-wD-gjb" secondAttribute="leading" constant="2" id="jft-Bg-nMo"/>
                        <constraint firstItem="qb1-Xb-dfb" firstAttribute="leading" secondItem="GmR-sb-FjR" secondAttribute="trailing" constant="8" id="nxv-hw-RU3"/>
                        <constraint firstAttribute="height" constant="44" id="rDq-q0-fnA"/>
                        <constraint firstItem="M5A-cO-fQh" firstAttribute="bottom" secondItem="GmR-sb-FjR" secondAttribute="bottom" id="upR-Xf-4Ue"/>
                        <constraint firstItem="M5A-cO-fQh" firstAttribute="leading" secondItem="GmR-sb-FjR" secondAttribute="leading" id="wPQ-wd-ReY"/>
                        <constraint firstItem="qb1-Xb-dfb" firstAttribute="top" secondItem="Mzq-HE-kyv" secondAttribute="bottom" constant="-1.5" id="wju-RF-yk4"/>
                        <constraint firstAttribute="trailing" secondItem="Mzq-HE-kyv" secondAttribute="trailing" constant="16" id="zmv-mG-LrA"/>
                    </constraints>
                </view>
                <textView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" bounces="NO" scrollEnabled="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" bouncesZoom="NO" editable="NO" text="Lorem ipsum dolor sit er elit lamet, consectetaur cillium adipisicing pecu," selectable="NO" translatesAutoresizingMaskIntoConstraints="NO" id="6Yo-0B-bS1">
                    <rect key="frame" x="172" y="4" width="234" height="45"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="45" id="Ccr-jE-WgB"/>
                    </constraints>
                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                    <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                </textView>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="6Yo-0B-bS1" secondAttribute="trailing" constant="8" id="8ZI-3K-0CL"/>
                <constraint firstItem="ZoG-wD-gjb" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="10" id="GN9-ca-mox"/>
                <constraint firstItem="6Yo-0B-bS1" firstAttribute="leading" secondItem="ZoG-wD-gjb" secondAttribute="trailing" constant="8" id="HB4-qh-urP"/>
                <constraint firstItem="ZoG-wD-gjb" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="4" id="aXx-Jn-HlH"/>
                <constraint firstItem="6Yo-0B-bS1" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="4" id="e1z-H8-f06"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="blurView" destination="ZoG-wD-gjb" id="cjR-gH-Xlb"/>
                <outlet property="commentTextView" destination="6Yo-0B-bS1" id="5Hl-Bd-fdd"/>
                <outlet property="flexterName" destination="Mzq-HE-kyv" id="xlK-YM-6Ve"/>
                <outlet property="profileButton" destination="M5A-cO-fQh" id="tev-d2-mHD"/>
                <outlet property="profileImageView" destination="GmR-sb-FjR" id="pjW-ZC-x5h"/>
                <outlet property="profileImageViewWidthConstraint" destination="09Y-vz-yv8" id="zqY-hX-Tv7"/>
                <outlet property="statsLabel" destination="qb1-Xb-dfb" id="boD-sM-31P"/>
            </connections>
            <point key="canvasLocation" x="227.536231884058" y="-155.02232142857142"/>
        </view>
    </objects>
</document>
