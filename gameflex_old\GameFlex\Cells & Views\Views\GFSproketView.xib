<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17156" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17125"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="GFSproketView" customModule="navTest" customModuleProvider="target">
            <connections>
                <outlet property="contentView" destination="iN0-l3-epB" id="ekS-9z-kCQ"/>
                <outlet property="imageView" destination="KTo-sB-dUy" id="ASy-0q-BJQ"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB">
            <rect key="frame" x="0.0" y="0.0" width="200" height="200"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" insetsLayoutMarginsFromSafeArea="NO" translatesAutoresizingMaskIntoConstraints="NO" id="KTo-sB-dUy">
                    <rect key="frame" x="0.0" y="0.0" width="200" height="200"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                </imageView>
            </subviews>
            <viewLayoutGuide key="safeArea" id="vUN-kp-3ea"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="KTo-sB-dUy" firstAttribute="trailing" secondItem="iN0-l3-epB" secondAttribute="trailing" id="SsY-1h-Ynj"/>
                <constraint firstAttribute="bottom" secondItem="KTo-sB-dUy" secondAttribute="bottom" id="qMQ-fJ-gOP"/>
                <constraint firstItem="KTo-sB-dUy" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" id="y86-F2-gsH"/>
                <constraint firstItem="KTo-sB-dUy" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="yYr-s5-LaB"/>
            </constraints>
            <nil key="simulatedTopBarMetrics"/>
            <nil key="simulatedBottomBarMetrics"/>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <point key="canvasLocation" x="141" y="95"/>
        </view>
    </objects>
</document>
