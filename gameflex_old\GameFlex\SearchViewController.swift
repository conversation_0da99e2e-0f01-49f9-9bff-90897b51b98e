//
//  SearchViewController.swift
//  GameFlex
//
//  Created by <PERSON> on 9/9/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import Kingfisher

class SearchViewController: GFViewController {
        
    @IBOutlet weak var searchBar: UISearchBar!
    @IBOutlet weak var tableView: UITableView!
    
    var filteredResults: [SearchResult] = []
    var searchInProgress = false
    var resultsArray: [SearchResult] = []
    var isFiltered = false
    
    static func storyboardInstance() -> SearchViewController {
        let sb = UIStoryboard(name: "Main", bundle: nil)
        return sb.instantiateViewController(withIdentifier: String(describing: SearchViewController.self)) as! SearchViewController
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(UINib(nibName: SearchResultTableViewCell.cellIdentifier, bundle: nil), forCellReuseIdentifier: SearchResultTableViewCell.cellIdentifier)
        searchBar.delegate = self
        searchBar.tintColor = .gfGreen
        searchBar.addDoneButtonToKeyboard(myAction: #selector(searchBar.resignFirstResponder))
        tableView.tableHeaderView = searchBar
        let searchBarCancelButtonForegroundColor = UIColor.darkGray
        let attributes = [NSAttributedString.Key.foregroundColor: searchBarCancelButtonForegroundColor]
        UIBarButtonItem.appearance(whenContainedInInstancesOf: [UISearchBar.self]).setTitleTextAttributes(attributes, for: .normal)
        UILabel.appearance(whenContainedInInstancesOf: [UISearchBar.self]).font = UIFont.systemFont(ofSize: 13)
        if AppInfo.channelsAreActive {
            searchBar.placeholder = "search.placeholder".localized
        } else {
            searchBar.placeholder = "search.placeholder.channelsOff".localized
        }
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        title = "Search"
        searchBar.text = ""
        resultsArray = []
        filteredResults = []
        tableView.reloadData()
        searchBar.becomeFirstResponder()
        
    }
    
    func getTheSearchResults(_ searchText: String) {
        searchInProgress = true
        GFNetworkServices.getSearchResults(searchText) { (success, results, error) in
            self.searchInProgress = false
            guard error == nil else {
                return
            }
            if results.count > 95 {
                User.searchOnNumberOfLetters = User.searchOnNumberOfLetters + 1
            }
            if AppInfo.channelsAreActive {
                self.resultsArray.append(contentsOf: results)
            } else {
                let newResults = results.filter({$0.type == .user })
                self.resultsArray.append(contentsOf: newResults)
            }
            self.resultsArray.sort(by: { $0.searchName ?? "" < $1.searchName ?? "" })
            self.searchInProgress = false
            DispatchQueue.main.async {
                self.filteredResults = self.resultsArray.filter({ ($0.searchName?.hasPrefix(self.searchBar.text?.lowercased() ?? "") ?? false)})
                self.tableView.reloadData()
            }
        }

    }
    
}

extension SearchViewController: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        if isFiltered {
            return filteredResults.count
        } else {
            return resultsArray.count
        }
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: SearchResultTableViewCell.cellIdentifier, for: indexPath) as? SearchResultTableViewCell else { return SearchResultTableViewCell () }
        var result: SearchResult?
        
        if isFiltered {
            result = filteredResults[indexPath.row]
        } else {
            result = resultsArray[indexPath.row]
        }
        cell.titleLabel?.text = result?.name
        if let userId = result?.searchId, result?.type == .user {
            cell.profileImage.image = #imageLiteral(resourceName: FlexManager.randomImagePlaceholder(userId))
        }
        if let pic = result?.profileImage, pic != "", let objectId = result?.searchId {
            let url = URL(string: pic)
            let processor = DownsamplingImageProcessor(size: cell.profileImage?.bounds.size ?? CGSize(width: 40, height: 40))
            cell.profileImage?.kf.indicatorType = .activity
            cell.profileImage?.kf.setImage(
                with: url,
                placeholder: UIImage(named: FlexManager.randomImagePlaceholder(objectId)),
                options: [
                    .processor(processor),
                    .scaleFactor(UIScreen.main.scale),
                    .transition(.fade(1)),
                    .cacheOriginalImage
                ], completionHandler:
                    {
                        result in
                        switch result {
                        case .success: break
                        case .failure(let error):
                            print("Job failed: \(error.localizedDescription)")
                        }
                    })
        }
        cell.profileSuperView.layer.cornerRadius = cell.profileSuperView.frame.size.width/2
        if result?.type == .channel {
            cell.profileSuperView.layer.borderColor = UIColor.gfBlue_00D5FF.cgColor
            cell.profileSuperView.layer.borderWidth  = 1.0
            cell.refreshChannel(top: true)
            cell.subTitleLabel.text = "Channel"
            let channels = (User.flexter.channelsOwned ?? []) + (User.flexter.channelsParticipated ?? []) + (User.flexter.channelsFollowed ?? [])
            let isFollowing = channels.filter({$0.channelId == result?.searchId }).first != nil
            Utilities.configure(followButton: cell.followButton, following: isFollowing)
            cell.titleLabelTrailingConstraint.constant = 100
            cell.followButton.isHidden = false
        } else if result?.type == .user {
            cell.profileSuperView.layer.borderColor = UIColor.gfYellow_F2DE76.cgColor
            cell.profileSuperView.layer.borderWidth  = 1.0
            cell.subTitleLabel.text = "Flexter"
            if result?.searchId != User.userId {
                let isFollowing = User.flexter.following?.filter({ $0.channelId == result?.searchId }).first != nil
                Utilities.configure(followButton: cell.followButton, following: isFollowing)
                cell.titleLabelTrailingConstraint.constant = 100
                cell.followButton.isHidden = true
            } else {
                cell.titleLabelTrailingConstraint.constant = 10
                cell.followButton.isHidden = true
            }
        }

        cell.selectionStyle = .none
        if let channelId = result?.searchId, let type = result?.type {
            cell.configureCell(channelId, type)
        }
        
        cell.delegate = self
        return cell
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 194.0
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        
        let result = filteredResults[indexPath.row]
        
        if result.type == .user {
            let flexterId = result.searchId
            var flexter = Flexter()
            flexter.userId = flexterId
            let pvc = ProfileViewController.storyboardInstance()
            pvc.flexter = flexter
            navigationController?.pushViewController(pvc, animated: true)
        }
    }
    
}

extension SearchViewController: UISearchBarDelegate {
    
    func searchBar(_ searchBar: UISearchBar, textDidChange searchText: String) { // called when text changes (including clear)
    
    }
    
    func searchBarCancelButtonClicked(_ searchBar: UISearchBar) {
        resultsArray.removeAll()
        filteredResults.removeAll()
        tableView.reloadData()
        searchBar.resignFirstResponder()
    }
    
    func searchBar(_ searchBar: UISearchBar, shouldChangeTextIn range: NSRange, replacementText text: String) -> Bool {
        
        let result = (searchBar.text as NSString?)?.replacingCharacters(in: range, with: text) ?? text
        
        if result.count == 0 {
            isFiltered = false
            resultsArray.removeAll()
            filteredResults.removeAll()
            tableView.reloadData()

        } else {
            isFiltered = true
            resultsArray = Array(Set(resultsArray))
            filteredResults = resultsArray.filter({ ($0.searchName?.hasPrefix(result.lowercased()) ?? false)})
            filteredResults = filteredResults.sorted(by: { $0.searchName ?? "" < $1.searchName ?? "" })
            self.tableView.reloadData()

        }
        if result.count == User.searchOnNumberOfLetters {
            getTheSearchResults(result.lowercased())
        }
        return true
    }
}

extension SearchViewController: HomeDelegate {
    func didTapToFollow(_ sender: UIButton, _ tableViewCell: UITableViewCell?) {
                
        if let cell = sender.superview as? SearchResultTableViewCell, let row = tableView.indexPath(for: cell)?.row, let searchId = filteredResults[row].searchId {
            Utilities.showSpinner()
            if sender.backgroundColor == .gfGreen {
                GFNetworkServices.followThis([searchId]) { (success, error) in
                    DispatchQueue.main.async {
                        Utilities.hideSpinner()
                    }
                    if success {
                        DispatchQueue.main.async {
                            Utilities.configure(followButton: cell.followButton, following: true)
                        }
                    }
                }
            } else {
                GFNetworkServices.unFollowThis([searchId]) { (success, error) in
                    DispatchQueue.main.async {
                        Utilities.hideSpinner()
                    }
                    if success {
                        DispatchQueue.main.async {
                            Utilities.configure(followButton: cell.followButton, following: false)
                        }
                    }
                }
            }
        }
    }

}
