<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="16097.2" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="16087"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="ImageEnhancementControlsView" customModule="GameFlex" customModuleProvider="target"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <stackView opaque="NO" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" spacing="31" translatesAutoresizingMaskIntoConstraints="NO" id="3wA-GB-8PH">
            <rect key="frame" x="0.0" y="0.0" width="414" height="93"/>
            <subviews>
                <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" translatesAutoresizingMaskIntoConstraints="NO" id="UbU-ve-5Qi">
                    <rect key="frame" x="0.0" y="0.0" width="80.5" height="93"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Filter" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="M4t-zj-gR5">
                            <rect key="frame" x="25.5" y="59" width="29" height="14"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="14" id="QnS-CK-03a"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="12"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Vw7-br-dWR">
                            <rect key="frame" x="20" y="15" width="40" height="40"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="40" id="E3E-Ya-NON"/>
                                <constraint firstAttribute="width" constant="40" id="aOt-gr-2I8"/>
                            </constraints>
                            <state key="normal" image="filter"/>
                        </button>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="bottom" secondItem="M4t-zj-gR5" secondAttribute="bottom" constant="20" id="6JN-Xn-5pM"/>
                        <constraint firstItem="M4t-zj-gR5" firstAttribute="centerX" secondItem="Vw7-br-dWR" secondAttribute="centerX" id="Rel-vG-SuX"/>
                        <constraint firstItem="M4t-zj-gR5" firstAttribute="centerX" secondItem="UbU-ve-5Qi" secondAttribute="centerX" id="y8E-fp-V0Y"/>
                        <constraint firstItem="M4t-zj-gR5" firstAttribute="top" secondItem="Vw7-br-dWR" secondAttribute="bottom" constant="4" id="ypb-83-eeX"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Zp8-dt-PVh">
                    <rect key="frame" x="111.5" y="0.0" width="80" height="93"/>
                    <subviews>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="MTy-oH-ee9">
                            <rect key="frame" x="20" y="15" width="40" height="40"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="40" id="LWS-yL-KyV"/>
                                <constraint firstAttribute="height" constant="40" id="U0s-Az-lBL"/>
                            </constraints>
                            <state key="normal" image="draw"/>
                        </button>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Edit" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="0gz-AL-VP4">
                            <rect key="frame" x="29" y="59" width="22" height="14"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="14" id="eDk-Mb-7fe"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="12"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="bottom" secondItem="0gz-AL-VP4" secondAttribute="bottom" constant="20" id="29Z-Bf-waX"/>
                        <constraint firstItem="MTy-oH-ee9" firstAttribute="centerX" secondItem="Zp8-dt-PVh" secondAttribute="centerX" id="4QI-hS-RFu"/>
                        <constraint firstItem="0gz-AL-VP4" firstAttribute="top" secondItem="MTy-oH-ee9" secondAttribute="bottom" constant="4" id="f6x-n4-nHJ"/>
                        <constraint firstItem="0gz-AL-VP4" firstAttribute="centerX" secondItem="MTy-oH-ee9" secondAttribute="centerX" id="yie-t2-cFm"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1Ph-e9-4Gd">
                    <rect key="frame" x="222.5" y="0.0" width="80.5" height="93"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Flare" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="iqd-4S-RfV">
                            <rect key="frame" x="26" y="59" width="28" height="14"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="14" id="W63-zp-iIZ"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="12"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="3Ec-gX-5NB">
                            <rect key="frame" x="20" y="15" width="40" height="40"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="40" id="GdV-fF-Oz4"/>
                                <constraint firstAttribute="width" constant="40" id="xyl-AE-fCY"/>
                            </constraints>
                            <state key="normal" image="flare"/>
                        </button>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="iqd-4S-RfV" firstAttribute="top" secondItem="3Ec-gX-5NB" secondAttribute="bottom" constant="4" id="0FM-8m-uNF"/>
                        <constraint firstItem="iqd-4S-RfV" firstAttribute="centerX" secondItem="3Ec-gX-5NB" secondAttribute="centerX" id="msU-Rg-kUs"/>
                        <constraint firstAttribute="bottom" secondItem="iqd-4S-RfV" secondAttribute="bottom" constant="20" id="uOg-eS-D3q"/>
                        <constraint firstItem="3Ec-gX-5NB" firstAttribute="centerX" secondItem="1Ph-e9-4Gd" secondAttribute="centerX" id="wDV-4V-iU7"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Pcy-HP-jHL">
                    <rect key="frame" x="334" y="0.0" width="80" height="93"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Text" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="PZJ-oJ-7ez">
                            <rect key="frame" x="28" y="59" width="24" height="14"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="14" id="S5C-Q4-Up7"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="12"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <color key="highlightedColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        </label>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="BbN-ZA-fbe">
                            <rect key="frame" x="20" y="15" width="40" height="40"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="40" id="FO7-03-CDs"/>
                                <constraint firstAttribute="width" constant="40" id="cC4-Mq-6Zg"/>
                            </constraints>
                            <state key="normal" image="text"/>
                        </button>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="PZJ-oJ-7ez" firstAttribute="top" secondItem="BbN-ZA-fbe" secondAttribute="bottom" constant="4" id="5lm-bY-Wsx"/>
                        <constraint firstItem="PZJ-oJ-7ez" firstAttribute="centerX" secondItem="BbN-ZA-fbe" secondAttribute="centerX" id="7MT-2e-YCW"/>
                        <constraint firstItem="BbN-ZA-fbe" firstAttribute="centerX" secondItem="Pcy-HP-jHL" secondAttribute="centerX" id="NoI-fj-ylf"/>
                        <constraint firstAttribute="bottom" secondItem="PZJ-oJ-7ez" secondAttribute="bottom" constant="20" id="VH1-na-37M"/>
                    </constraints>
                </view>
            </subviews>
            <constraints>
                <constraint firstAttribute="height" constant="93" id="Art-oN-fsK"/>
                <constraint firstItem="1Ph-e9-4Gd" firstAttribute="width" secondItem="UbU-ve-5Qi" secondAttribute="width" id="DLg-Xm-yqm"/>
                <constraint firstItem="Pcy-HP-jHL" firstAttribute="width" secondItem="UbU-ve-5Qi" secondAttribute="width" id="oOP-ND-5TC"/>
                <constraint firstItem="Zp8-dt-PVh" firstAttribute="width" secondItem="UbU-ve-5Qi" secondAttribute="width" id="v0V-Zg-HeA"/>
            </constraints>
            <point key="canvasLocation" x="80" y="63"/>
        </stackView>
    </objects>
    <resources>
        <image name="draw" width="128" height="128"/>
        <image name="filter" width="40" height="40"/>
        <image name="flare" width="40" height="40"/>
        <image name="text" width="40" height="40"/>
    </resources>
</document>
