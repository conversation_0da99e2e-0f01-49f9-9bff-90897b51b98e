//
//  SignUpViewController.swift
//  GameFlex
//
//  Created by <PERSON> on 8/23/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import AuthenticationServices
import CryptoKit
import FirebaseAuth
import GoogleSignIn


class SignUpViewController: GFViewController {
    
    @IBOutlet weak var logoImageView: UIImageView!
    @IBOutlet weak var loginButton: UIButton!
    @IBOutlet weak var signInWithAppleView: UIView!
    @IBOutlet weak var signUpWithEmail: UIButton!
    @IBOutlet weak var signUpWithGoogleButton: GIDSignInButton!
    @IBOutlet weak var googleView: UIView!
            
    fileprivate var currentNonce: String?
    var isMFAEnabled = false
    
    weak var tab: LandingPageViewController?
    
    // MARK: - Life cycle funcs
    
    static func storyboardInstance() -> SignUpViewController {
        let sb = UIStoryboard(name: "Main", bundle: nil)
        return sb.instantiateViewController(withIdentifier: String(describing: SignUpViewController.self)) as! SignUpViewController
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setBackgroundGradient()
        navigationController?.navigationBar.tintColor = .black
        let textAttributes = [NSAttributedString.Key.foregroundColor: UIColor.black]
        navigationController?.navigationBar.titleTextAttributes = textAttributes
        // create sign in with Apple button
        if #available(iOS 13.0, *) {
            let authorizationButton = ASAuthorizationAppleIDButton()
            authorizationButton.addTarget(self, action: #selector(handleAuthorizationAppleIDButtonPress), for: .touchUpInside)
            signInWithAppleView.addSubview(authorizationButton)
            authorizationButton.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                authorizationButton.topAnchor.constraint(equalTo: signInWithAppleView.topAnchor, constant: 0.0),
                authorizationButton.leadingAnchor.constraint(equalTo: signInWithAppleView.leadingAnchor, constant: 0.0),
                authorizationButton.trailingAnchor.constraint(equalTo: signInWithAppleView.trailingAnchor, constant: 0.0),
                authorizationButton.bottomAnchor.constraint(equalTo: signInWithAppleView.bottomAnchor, constant: 0.0),
            ])
            signInWithAppleView.layer.cornerRadius = 8.0
            signInWithAppleView.clipsToBounds = true

        }
        // create sign up with Google button
        googleView.layer.cornerRadius = 8.0
        googleView.backgroundColor = .clear
        googleView.clipsToBounds = true
        GIDSignIn.sharedInstance()?.presentingViewController = self
        loginButton.setTitleColor(.gfGreen, for: .normal)
        GIDSignIn.sharedInstance().delegate = self
        loginButton.layer.cornerRadius = 8.0
        loginButton.layer.borderWidth = 1.0
        loginButton.layer.borderColor = UIColor.gfGreen.cgColor
        // email button
        signUpWithEmail.addTarget(self, action: #selector(didTapEmailButton), for: .touchUpInside)
        
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        Utilities.hideSpinner()
        title = "Sign up"
        addCancelToNavBar()
        tabBarController?.tabBar.isHidden = true
        if User.shouldDismissOnPop {
            User.shouldDismissOnPop = false
            dismissMe()
        } else if User.shouldGoToLoginOnPop {
            User.shouldGoToLoginOnPop = false
            let lvc = LoginViewController.storyboardInstance()
            navigationController?.pushViewController(lvc, animated: true)
        }
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        tabBarController?.tabBar.isHidden = false
    }
    
    // MARK: - Action functions
    
    @IBAction func didTapLoginButton() {
        let lvc = LoginViewController.storyboardInstance()
        navigationController?.pushViewController(lvc, animated: true)
    }
        
    @objc func handleAuthorizationAppleIDButtonPress() {
        if #available(iOS 13.0, *) {
            let nonce = Utilities.randomNonceString()
            currentNonce = nonce
            let appleIDProvider = ASAuthorizationAppleIDProvider()
            let request = appleIDProvider.createRequest()
            request.requestedScopes = [.fullName, .email]
            request.nonce = Utilities.sha256(nonce)
            
            let authorizationController = ASAuthorizationController(authorizationRequests: [request])
            authorizationController.delegate = self
            authorizationController.presentationContextProvider = self
            authorizationController.performRequests()
        }
    }
    
    @objc func didTapEmailButton() {
        let evc = EmailViewController.storyboardInstance()
        navigationController?.pushViewController(evc, animated: true)
    }
}

// MARK: - ASAuthorizationControllerDelegate and ASAuthorizationControllerPresentationContextProviding for signing in with Apple
extension SignUpViewController: ASAuthorizationControllerDelegate {
    /// - Tag: did_complete_authorization
    @available(iOS 13.0, *)
    func authorizationController(controller: ASAuthorizationController, didCompleteWithAuthorization authorization: ASAuthorization) {
        
        if let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential {
            guard let nonce = currentNonce else {
                fatalError("Invalid state: A login callback was received, but no login request was sent.")
            }
            guard let appleIDToken = appleIDCredential.identityToken else {
                print("Unable to fetch identity token")
                return
            }
            guard let idTokenString = String(data: appleIDToken, encoding: .utf8) else {
                print("Unable to serialize token string from data: \(appleIDToken.debugDescription)")
                return
            }
            Utilities.showSpinner()
            // Initialize a Firebase credential.
            let credential = OAuthProvider.credential(withProviderID: "apple.com",
                                                      idToken: idTokenString,
                                                      rawNonce: nonce)
            // Sign in with Firebase.
            Auth.auth().signIn(with: credential) { (authResult, error) in
                if error != nil {
                    DispatchQueue.main.async {
                        let alert = UIAlertController(title: "Error", message: "Report this bug:\n\n \(error.debugDescription)", preferredStyle: .alert)
                        let report = UIAlertAction(title: "Report", style: .default) { (action) in
                            let bvc = BugViewController.storyboardInstance()
                            bvc.preRecordedMessage = "LOGIN ERROR\n\(error.debugDescription)"
                            self.present(bvc, animated: true)
                        }
                        let not = UIAlertAction(title: "Cancel", style: .default, handler: nil)
                        alert.addAction(not)
                        alert.addAction(report)
                        self.present(alert, animated: true, completion: nil)
                    }
                    return
                }
                // User is signed in to Firebase with Apple.
                User.isEmailVerified = authResult?.user.isEmailVerified
                User.flexter.flexterName = authResult?.user.displayName ?? ""
                User.flexter.email = authResult?.user.email ?? ""
                if let avatar = authResult?.user.photoURL {
                    User.flexter.profileImage = avatar.absoluteString
                }
                User.authenticationService = .apple
                if let email = User.flexter.email, email != "" {
                    self.loginToGameFlex(email)
                }
            }
        }
    }
                    
    @available(iOS 13.0, *)
          func authorizationController(controller: ASAuthorizationController, didCompleteWithError error: Error) {
            // Handle error.
            print("Sign in with Apple errored: \(error)")

        }
}
    
extension SignUpViewController: ASAuthorizationControllerPresentationContextProviding {
        /// - Tag: provide_presentation_anchor
        @available(iOS 13.0, *)
        func presentationAnchor(for controller: ASAuthorizationController) -> ASPresentationAnchor {
            return self.view.window!
        }
}

// MARK: - GIDSignInDelegate for Signing in with Google
extension SignUpViewController: GIDSignInDelegate {
    
    func sign(_ signIn: GIDSignIn!, didSignInFor user: GIDGoogleUser!, withError error: Error?) {
        // ...
        if error != nil {
            // ...
            return
        }
        
        guard let authentication = user.authentication else { return }
        let credential = GoogleAuthProvider.credential(withIDToken: authentication.idToken, accessToken: authentication.accessToken)
        Auth.auth().signIn(with: credential) { (authResult, error) in
            if let error = error {
                let authError = error as NSError
                if (self.isMFAEnabled && authError.code == AuthErrorCode.secondFactorRequired.rawValue) {
                    // The user is a multi-factor user. Second factor challenge is required.
                    let resolver = authError.userInfo[AuthErrorUserInfoMultiFactorResolverKey] as! MultiFactorResolver
                    var displayNameString = ""
                    for tmpFactorInfo in (resolver.hints) {
                        displayNameString += tmpFactorInfo.displayName ?? ""
                        displayNameString += " "
                    }
                }
            }
            // User is signed in with google
            if let use = authResult?.user {
                User.flexter.flexterName = use.displayName ?? ""
                User.userId       = use.uid
                User.flexter.email    = use.email ?? ""
                User.isEmailVerified = use.isEmailVerified
                if use.photoURL?.absoluteString != nil {
                    User.flexter.profileImage   = use.photoURL?.absoluteString ?? ""
                }
                GFDefaults.pushAccessToken = authentication.accessToken
                GFDefaults.pushAccessTokenExpiryDate = authentication.accessTokenExpirationDate
                User.authenticationService = .google
                if let email = User.flexter.email, email != "" {
                    self.loginToGameFlex(email)
                }
            }
        }
    }
    
    // handles the google and apple id handling post-firebase authentication
    private func loginToGameFlex(_ email: String) {
        // validated user
        GFNetworkServices.registerAtGameFlexWithEmail(email) { (success, error) in
            guard error == nil else {
                if (error as? GFError)?.errorType == .cantRegisterExistingAccount {
                    OnboardingStateMachine.existingAccount = true
                    GFNetworkServices.loginAtGameFlexWithEmail(email) { (success, error) in
                        // attempt login
                        guard error == nil else {
                            DispatchQueue.main.async {
                                Utilities.hideSpinner()
                                let alert = UIAlertController(title: "Error",
                                                              message: "Something unexpected happened. Try again. Code = 400.",
                                                              preferredStyle: .alert)
                                let ok = UIAlertAction(title: "OK", style: .default, handler: nil)
                                alert.addAction(ok)
                                self.present(alert, animated: true, completion: nil)
                            }
                            return
                        }
                        if success, let userId = User.userId, userId != "" {
                            GFNetworkServices.getUserProfile(userId) { (success, flexter, error) in
                                if success {
                                    if let flexter = flexter {
                                        User.updateTheUser(flexter)
                                    }
                                    DispatchQueue.main.async {
                                        Utilities.hideSpinner()
                                        let alert = UIAlertController(title: "Success",
                                                                      message: "",
                                                                      preferredStyle: .alert)
                                        switch User.authenticationService {
                                        case .apple: alert.message = OnboardingStateMachine.existingAccount ? "login.successApple.existingAccount".localized : "login.successApple".localized
                                        case .google: alert.message = OnboardingStateMachine.existingAccount ? "login.successGoogle.existingAccount".localized : "login.successGoogle".localized
                                        case .email: alert.message = OnboardingStateMachine.existingAccount ? "login.successEmail.existingAccount".localized : "login.successEmail".localized
                                        case .none: alert.message = OnboardingStateMachine.existingAccount ? "login.successNone.existingAccount".localized : "login.successNone".localized
                                        }
                                        let ok = UIAlertAction(title: "OK", style: .default, handler: { _ in
                                            OnboardingStateMachine.didComplete(this: .login)
                                        })
                                        alert.addAction(ok)
                                        self.present(alert, animated: true, completion: nil)
                                    }
                                    return
                                }
                            }
                        }
                    }
                }
                return
            }
            GFNetworkServices.loginAtGameFlexWithEmail(email) { (success, error) in
                // attempt login
                guard error == nil else {
                    DispatchQueue.main.async {
                        Utilities.hideSpinner()
                        let alert = UIAlertController(title: "Error",
                                                      message: "Something unexpected happened. Try again. Code = 400.",
                                                      preferredStyle: .alert)
                        let ok = UIAlertAction(title: "OK", style: .default, handler: nil)
                        alert.addAction(ok)
                        self.present(alert, animated: true, completion: nil)
                    }
                    return
                }
                if success, let userId = User.userId {
                    GFNetworkServices.getUserProfile(userId) { (success, flexter, error) in
                        if success {
                            if let flexter = flexter {
                                User.updateTheUser(flexter)
                            }
                            DispatchQueue.main.async {
                                Utilities.hideSpinner()
                                let alert = UIAlertController(title: "Success",
                                                              message: "",
                                                              preferredStyle: .alert)
                                switch User.authenticationService {
                                case .apple: alert.message = OnboardingStateMachine.existingAccount ? "login.successApple.existingAccount".localized : "login.successApple".localized
                                case .google: alert.message = OnboardingStateMachine.existingAccount ? "login.successGoogle.existingAccount".localized : "login.successGoogle".localized
                                case .email: alert.message = OnboardingStateMachine.existingAccount ? "login.successEmail.existingAccount".localized : "login.successEmail".localized
                                case .none: alert.message = OnboardingStateMachine.existingAccount ? "login.successNone.existingAccount".localized : "login.successNone".localized
                                }
                                let ok = UIAlertAction(title: "OK", style: .default, handler: { _ in
                                    OnboardingStateMachine.didComplete(this: .login)
                                })
                                alert.addAction(ok)
                                self.present(alert, animated: true, completion: nil)
                            }
                            return
                        }
                    }
                }
            }
        }
    }
        
    func sign(_ signIn: GIDSignIn!, didDisconnectWith user: GIDGoogleUser!, withError error: Error!) {
        // Perform any operations when the user disconnects from app here.
        guard error == nil else {
            return
        }
        User.refreshToken = nil
    }
}
