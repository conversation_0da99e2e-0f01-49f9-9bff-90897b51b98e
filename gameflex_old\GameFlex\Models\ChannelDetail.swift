//
//  ChannelDetail.swift
//  GameFlex
//
//  Created by <PERSON> on 11/28/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import Foundation
import SwiftyJSON

struct ChannelDetail {
    
    var channelDescription: String?
    var channelId: String?
    var channelImage: String?
    var channelName: String?
    var channelOwner: Channel?
    var createAt: String?
    var createAtDate: Date?
    var flexCount: Int?
    var flexVelocity: Double?
    var followerCount: Int?
    var followers: [Channel]?
    var participants: [Channel]?
    var pendingInvitees: [Channel]?

    static func parseChannelDetail(json: JSON) -> ChannelDetail {
        let df = DateFormatter()
        df.dateFormat = "yyyy-MM-dd HH:mm:ss" //2020-09-16T16:34:57.843Z
        var detail = ChannelDetail()
        detail.channelDescription = json["description"].stringValue
        detail.channelId = json["channelId"].stringValue
        detail.channelImage = json["profileImage"].stringValue
        detail.channelName = json["name"].stringValue
        detail.channelOwner = Channel.parseFlexterAsChannel(json["channelOwner"])
        detail.createAt = json["createdAt"].string
        if let dates = detail.createAt, dates != "" {
            detail.createAtDate = Utilities.cleanUpServerDate(dates)
        } else {
            detail.createAtDate = Date()
        }
        detail.flexCount = json["flexCount"].intValue
        let daysOld = Date().timeIntervalSince(detail.createAtDate ?? Date())/(3600 * 24)
        if daysOld > 0 {
            detail.flexVelocity = Double(detail.flexCount ?? 0)/daysOld
        } else {
            detail.flexVelocity = 0.0
        }
        
        detail.followerCount = json["followerCount"].intValue
        
        var arr: [Channel] = []
        for jso in json["participants"].arrayValue {
            let fl = Channel.parseFlexterAsChannel(jso)
            arr.append(fl)
        }
        detail.participants = arr
        
        var barr: [Channel] = []
        for jso in json["pendingInvitees"].arrayValue {
            let fl = Channel.parseFlexterAsChannel(jso)
            barr.append(fl)
        }
        detail.pendingInvitees = barr
        
        var carr: [Channel] = []
        for jso in json["followers"].arrayValue {
            let fl = Channel.parseFlexterAsChannel(jso)
            carr.append(fl)
        }
        detail.followers = carr

        return detail
    }
}
