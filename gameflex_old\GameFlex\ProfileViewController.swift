//
//  ProfileViewController.swift
//  GameFlex
//
//  Created by <PERSON> on 8/5/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import Firebase
import SafariServices
import Kingfisher
import FirebaseAuth

protocol ProfileDelegate: AnyObject {
    func didTapForProfileAction(_ type: ProfileUpdateDataType, _ object: Any?)
    func updateTextFor(_ type: ProfileUpdateDataType, text: String)
    func didBeginEditingTextView()
}

extension ProfileDelegate {
    func updateTextFor(_ type: ProfileUpdateDataType, text: String) {/* leave nothing here*/}
    func didTapForProfileAction(_ type: ProfileUpdateDataType, _ object: Any?) {/* leave nothing here*/}
    func didBeginEditingTextView() { /* leave nothing here */}
}

enum ProfileUpdateDataType: String {
    case name, flexterName, userDescription, photo, goToEdit, goToProfile, description
    case flexes, channels
    case makeTheCall, checkForTaken
    case profileImage
    case caption // used in FinalizeViewController
    case fcmToken
    case following, followingX, follower
    case changeBackgroundChannelColor
    case goToInvites, goToChezLui
    case owner, contributor, subscribed, all, newChannel, favThisChannel // for ChannelDirectoryViewController
    case goToChannelReview, goToChannelDirectory // for ChannelViewController
}

class ProfileViewController: GFViewController {
    
    @IBOutlet weak var tableView: UITableView!
    var flexter: Flexter? // for showing profiles of others
    var collectionViewType: ProfileUpdateDataType = .flexes
    let channelCellTag = 455654
    var isModal = false
    var followers: [Channel] = []
            
    static func storyboardInstance() -> ProfileViewController {
        let sb = UIStoryboard(name: "Main", bundle: nil)
        return sb.instantiateViewController(withIdentifier: String(describing: ProfileViewController.self)) as! ProfileViewController
    }
    
    // MARK: - Lifecycle funcs
    
    override func viewDidLoad() {
        super.viewDidLoad()
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(UITableViewCell.self, forCellReuseIdentifier: String(describing: UITableViewCell.self))
        tableView.register(UINib(nibName: ProfileTopTableViewCell.cellIdentifier, bundle: nil), forCellReuseIdentifier: ProfileTopTableViewCell.cellIdentifier)
        tableView.register(UINib(nibName: ProfileMiddleTableViewCell.cellIdentifier, bundle: nil), forCellReuseIdentifier: ProfileMiddleTableViewCell.cellIdentifier)
        tableView.register(UINib(nibName: ProfileBottomTableViewCell.cellIdentifier, bundle: nil), forCellReuseIdentifier: ProfileBottomTableViewCell.cellIdentifier)
        tableView.register(UINib(nibName:  FollowingTableViewCell.cellIdentifier, bundle: nil), forCellReuseIdentifier: FollowingTableViewCell.cellIdentifier)
        tableView.register(UINib(nibName:  FavoriteChannelTableViewCell.cellIdentifier, bundle: nil), forCellReuseIdentifier: FavoriteChannelTableViewCell.cellIdentifier)
        tableView.register(UINib(nibName:  ChannelTableViewCell.cellIdentifier, bundle: nil), forCellReuseIdentifier: ChannelTableViewCell.cellIdentifier)

        tableView.tableFooterView = UIView()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        title = "Profile"
        if flexter != nil, let flexterId = flexter?.userId, flexterId != "", User.isLoggedIn {
            Utilities.showSpinner()
            GFNetworkServices.getUserProfile(flexterId) { (success, flexter, error) in
                guard error == nil else {
                    
                    return
                }
                if success {
                    if let flexter = flexter {
                        self.flexter = flexter
                    }
                    DispatchQueue.main.async {
                        Utilities.hideSpinner()
                        self.tableView.reloadData()
                    }
                }
            }
        } else if let userId = User.userId, userId != "" {
            Utilities.showSpinner()
            GFNetworkServices.getUserProfile(userId) { (success, flexter, error) in
                DispatchQueue.main.async {
                    Utilities.hideSpinner()
                }
                guard error == nil else {
                    return
                }
                if success {
                    if let flexter = flexter {
                        User.updateTheUser(flexter)
                    }
                    DispatchQueue.main.async {
                        Utilities.hideSpinner()
                        self.tableView.reloadData()
                    }
                }
            }
        }
        getFollowers()
        if isModal {
            addCancelToNavBar()
        } else {
            addSettingsToNavBar()
        }
        if !User.isLoggedIn {
            tabBarController?.selectedIndex = 0
        }
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        if isModal {
            tabBarController?.tabBar.isHidden = true
        } else {
            tabBarController?.tabBar.isHidden = false
        }
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        tabBarController?.tabBar.isHidden = false
    }
    
    // MARK: - action funcs
    @objc func createNewChannel() {
        let cc = CreateChannelViewController.storyboardInstance()
        navigationController?.pushViewController(cc, animated: true)
    }
    
    private func getFollowers() {
        if let flexterId = flexter != nil ? flexter?.userId : User.userId, flexterId != "", User.isLoggedIn {
            Utilities.showSpinner()
            GFNetworkServices.getFollowers(flexterId) { (success, followers, error) in
                guard error == nil else {
                    
                    return
                }
                if success {
                    if !followers.isEmpty {
                        self.followers = followers
                    }
                    DispatchQueue.main.async {
                        Utilities.hideSpinner()
                        self.tableView.reloadData()
                    }
                }
            }
        }
    }
    
    // MARK: - Settings actionSheet funcs
    
    override func didTapSettings() {
        let actionSheet = UIAlertController(title: "v\(User.app ?? "40")", message: "", preferredStyle: .actionSheet)
        actions.forEach({ actionSheet.addAction($0) })
        let cancel = UIAlertAction(title: "Cancel", style: .cancel)
        actionSheet.addAction(cancel)
        actionSheet.view.tintColor = .white
        self.present(actionSheet, animated: true)
    }
    
    var report: UIAlertAction {
        get { UIAlertAction(title: "Report", style: .destructive) { (report) in
            let bvc = BugViewController.storyboardInstance()
            self.navigationController?.pushViewController(bvc, animated: true)
            }
        }
    }
        
    var login: UIAlertAction {
        get { UIAlertAction(title: "Login", style: .default) { (login) in
            let svc = SignUpViewController.storyboardInstance()
            let nc = GFNavigationController(rootViewController: svc)
            nc.modalPresentationStyle = .overCurrentContext
            self.present(nc, animated: true)
            }
        }
    }
    
    var resetFavs: UIAlertAction {
        get { UIAlertAction(title: "Reset Favs", style: .default) { (reset) in
            UserDefaults.standard.removeObject(forKey: GFDefaults.kFavStickerFrequencyDictionary)
            UserDefaults.standard.removeObject(forKey: GFDefaults.kFavRecentStickers)
        }
        }
    }
    
    var other: UIAlertAction {
        get { UIAlertAction(title: "Other", style: .default) { (other) in
            let vc = Temp2ViewController.storyboardInstance()
            self.navigationController?.pushViewController(vc, animated: true)
        }
        }
    }
    
    var logout: UIAlertAction {
        get { UIAlertAction(title: "Logout", style: .default) { (logout) in
            let firebaseAuth = Auth.auth()
            if let _ = try? firebaseAuth.signOut() {
                GFDefaults.resetUserDetails()
                User.logout()
                let alert = UIAlertController(title: "Success", message: "You have successfully logged out.", preferredStyle: .alert)
                let ok = UIAlertAction(title: "OK", style: .default, handler: nil)
                alert.addAction(ok)
                self.present(alert, animated: true, completion: nil)
            }
        }
        }
    }
    
    var getUserDetails: UIAlertAction {
        get { UIAlertAction(title: "Get User Details", style: .default) { (details) in
            if let uid = User.userId, uid != "" {
                GFNetworkServices.getUserProfile(uid) { (success, flexter, error) in
                    if let flexter = flexter {
                        User.updateTheUser(flexter)
                    }
                }
            }
        }
        }
    }
    
    var quick: UIAlertAction {
        get { UIAlertAction(title: "Quick", style: .default) { (quick) in
            let qvc = QuickIntroViewController.storyboardInstance()
            self.present(qvc, animated: true, completion: nil)
        }
        }
    }
    
    var whatsNew: UIAlertAction {
        get { UIAlertAction(title: "What's New", style: .default) { (new) in
            let wvc = WhatsNewViewController.storyboardInstance()
            self.present(wvc, animated: true, completion: nil)
        }
        }
    }
    
    var fcmToken: UIAlertAction {
        get { UIAlertAction(title: "FCM Token", style: .default) { (token) in
            UIPasteboard.general.string = "\(User.fcmToken ?? "missing fcmToken")"
            let alert = UIAlertController(title: "FCM TOKEN", message: User.fcmToken ?? "missing fcmToken", preferredStyle: .alert)
            let ok = UIAlertAction(title: "OK", style: .default, handler: nil)
            alert.addAction(ok)
            self.present(alert, animated: true, completion: nil)
        }
        }
    }
        
    var actions: [UIAlertAction] {
        guard User.refreshToken != nil else {
            return [report, login, resetFavs, other, quick, whatsNew]
        }
        if User.authenticationService == .email {
            return [report, login, resetFavs, other, /*"Resend verification email", "Change Password", */ logout, quick, fcmToken, whatsNew]
        }
        return  [report, login, resetFavs, other, logout, getUserDetails, quick, fcmToken, whatsNew ]
    }
    
}

// MARK: - UITableViewDelegate, DataSource

extension ProfileViewController: UITableViewDelegate, UITableViewDataSource {
    
    func numberOfSections(in tableView: UITableView) -> Int {
        switch collectionViewType {
        case .channels:
            return 4
        default: return 1
        }
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        switch collectionViewType {
        case .channels:
            let flextr = flexter ?? User.flexter
            let follow = (flextr.channelsFollowed?.count ?? 0)
            let participate = (flextr.channelsParticipated?.count ?? 0)
            let owned = (flextr.channelsOwned?.count ?? 0)
            switch section {
            case 1: return follow
            case 2: return owned
            case 3: return participate
            case 0: return 2
            default: return 0
            }
        case .following:
            let flextr = flexter ?? User.flexter
            let counted = flextr.following?.count ?? 0
            return 2 + counted // top, middle
        case .follower:
            return 2 + (User.flexter.followerCount ?? 0) // top, middle
            
        default: return 3
        }
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let flextr = flexter ?? User.flexter
        if indexPath.section == 1 { // the favorite collection view
            guard let cell = tableView.dequeueReusableCell(withIdentifier: ChannelTableViewCell.cellIdentifier, for: indexPath) as? ChannelTableViewCell else { return ChannelTableViewCell() }
            cell.channel = flextr.channelsFollowed?[indexPath.row]
            cell.configureCell()
            cell.getlatestFlexFromChannel()
            cell.getChannelDetails()
            cell.favButtonWidthConstraint.constant = 0
            cell.moreButtonWidthConstraint.constant = 0
            cell.delegate = self
            cell.selectionStyle = .none
            cell.tag = channelCellTag
            if flextr != User.flexter {
                cell.moreButton.isHidden = true
            }
            return cell

        } else if indexPath.section == 2 { // the list of owned channels
            guard let cell = tableView.dequeueReusableCell(withIdentifier: ChannelTableViewCell.cellIdentifier, for: indexPath) as? ChannelTableViewCell else { return ChannelTableViewCell() }
            cell.channel = flextr.channelsOwned?[indexPath.row]
            cell.configureCell()
            cell.getlatestFlexFromChannel()
            cell.getChannelDetails()
            cell.favButtonWidthConstraint.constant = 0
            cell.moreButtonWidthConstraint.constant = 0
            cell.delegate = self
            cell.selectionStyle = .none
            cell.tag = channelCellTag
            if flextr != User.flexter {
                cell.moreButton.isHidden = true
            }
            return cell

        } else if indexPath.section == 3 { // the list of participating channels
            guard let cell = tableView.dequeueReusableCell(withIdentifier: ChannelTableViewCell.cellIdentifier, for: indexPath) as? ChannelTableViewCell else { return ChannelTableViewCell() }
            cell.channel = flextr.channelsParticipated?[indexPath.row]
            cell.configureCell()
            cell.getlatestFlexFromChannel()
            cell.getChannelDetails()
            cell.favButtonWidthConstraint.constant = 0
            cell.moreButtonWidthConstraint.constant = 0
            cell.delegate = self
            cell.selectionStyle = .none
            cell.tag = channelCellTag
            if flextr != User.flexter {
                cell.moreButton.isHidden = true
            }
            return cell

        }
        
        if indexPath.row == 0 {
            guard let cell = tableView.dequeueReusableCell(withIdentifier: ProfileTopTableViewCell.cellIdentifier, for: indexPath) as? ProfileTopTableViewCell else { return ProfileTopTableViewCell() }
            cell.delegate = self
            cell.homeDelegate = self
            cell.configureCell(flexter, isProfileViewController: true)
            return cell
        } else if indexPath.row == 1 {
            guard let cell = tableView.dequeueReusableCell(withIdentifier: ProfileMiddleTableViewCell.cellIdentifier, for: indexPath) as? ProfileMiddleTableViewCell else { return ProfileMiddleTableViewCell() }
            let flextr = flexter ?? User.flexter
            cell.tableViewWidth = view.frame.size.width
            cell.isProfile = true
            cell.configureCell(flexter: flextr)
            cell.delegate = self
            return cell
        } else if indexPath.row >= 2 {
            switch collectionViewType {
            case .flexes:
                guard let cell = tableView.dequeueReusableCell(withIdentifier: ProfileBottomTableViewCell.cellIdentifier, for: indexPath) as? ProfileBottomTableViewCell else { return ProfileBottomTableViewCell() }
                if let userId = flexter?.userId ?? User.userId {
                    cell.configureCell(userId)
                    cell.delegate = self
                }
                return cell
            case .following, .follower:
                guard let cell = tableView.dequeueReusableCell(withIdentifier: FollowingTableViewCell.cellIdentifier, for: indexPath) as? FollowingTableViewCell else { return FollowingTableViewCell() }
                cell.selectionStyle = .none
                let flexter = self.flexter ?? User.flexter
                let arr = collectionViewType == .following ? flexter.following : followers
                if let channel = arr?[indexPath.row - 2] {
                    if collectionViewType == .following {
                        cell.configureCell(following: true, channel: channel)
                    } else {
                        let isFollowing = !(flexter.following?.filter({ $0.channelId == channel.channelId }).isEmpty ?? true)
                        cell.configureCell(following: isFollowing, channel: channel)
                    }
                    if let pic = channel.channelImage, pic != "" {
                        let url = URL(string: pic)
                        let processor = DownsamplingImageProcessor(size: cell.profileImage?.bounds.size ?? CGSize(width: 40, height: 40))
                        cell.profileImage?.kf.indicatorType = .activity
                        cell.profileImage?.kf.setImage(
                            with: url,
                            placeholder: UIImage(named: FlexManager.randomImagePlaceholder(channel.channelId)),
                            options: [
                                .processor(processor),
                                .scaleFactor(UIScreen.main.scale),
                                .transition(.fade(1)),
                                .cacheOriginalImage
                            ], completionHandler:
                                {
                                    result in
                                    switch result {
                                    case .success: break
                                    case .failure(let error):
                                        print("Job failed: \(error.localizedDescription)")
                                    }
                                })
                    } else {
                        cell.profileImage.image = UIImage(named: FlexManager.randomImagePlaceholder(channel.channelId))
                        cell.profileImage.transform = CGAffineTransform(a: 0.5, b: 0, c: 0, d: 0.5, tx: 0, ty: 0)
                    }
                }
                cell.delegate = self
                cell.homeDelegate = self
                return cell
                 
            case .channels:
                let cell = tableView.dequeueReusableCell(withIdentifier: String(describing: UITableViewCell.self), for: indexPath)
                return cell
            default: break
            }
        }
        return UITableViewCell()
    }
    
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        let header = UIView(frame: CGRect(x: 0, y: 0, width: view.frame.size.width, height: 44))
        header.backgroundColor = .black
        header.subviews.filter({ $0.tag == 99421 }).forEach({ $0.removeFromSuperview() })
        let label = UILabel(frame: CGRect(x: 16.0, y: 0, width: header.frame.size.width - 26.0, height: header.frame.size.height))
        label.tag = 99421
        let flextr = flexter ?? User.flexter
        var subStringRange = " ".range(of: " ")
        switch section {
        case 1:
            let fav = 0
            label.text = "Favorite  (\(fav))"
            subStringRange = label.text?.range(of: "(\(fav))")
        case 2:
            label.text = "Owner  (\(flextr.channelsOwned?.count ?? 0))"
            subStringRange = label.text?.range(of: "(\(flextr.channelsOwned?.count ?? 0))")
        case 3:
            label.text = "Contributor  (\(flextr.channelsParticipated?.count ?? 0))"
            subStringRange = label.text?.range(of: "(\(flextr.channelsParticipated?.count ?? 0))")
        default: break
        }
        label.textColor = .gfOffWhite
        label.font = .boldSystemFont(ofSize: 17)
        
        let attr = NSMutableAttributedString(string: label.text ?? "")
        if let ssr = subStringRange {
            let nsRange = NSRange(ssr, in: label.text ?? "")
            attr.addAttributes([NSAttributedString.Key.foregroundColor: UIColor.gfGreen,
                                NSAttributedString.Key.font: UIFont.systemFont(ofSize: 17)],
                               range: nsRange)
            label.attributedText = attr
        }
        header.addSubview(label)
        return header

    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        if indexPath.section == 0 {
            if indexPath.row == 0 {
                return 202
            } else if indexPath.row == 1 {
                return 45
            }
        }
        switch collectionViewType {
        case .following: return 44.0
        case .channels: return 144
        case .follower: return 44.0
        default: return tableView.frame.size.height - 235.0
        }
    }
    
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        switch section {
        case 1, 2, 3: return 44
        default: return 0
        }
    }
    
    func tableView(_ tableView: UITableView, canEditRowAt indexPath: IndexPath) -> Bool {
        if indexPath.section == 2, collectionViewType == .channels , flexter == nil {
            return true
        }
        return false
    }
    
    func tableView(_ tableView: UITableView, commit editingStyle: UITableViewCell.EditingStyle, forRowAt indexPath: IndexPath) {
        if indexPath.section == 2, flexter == nil, let channelId = User.flexter.channelsOwned?[indexPath.row].channelId {
            if editingStyle == .delete {
                User.flexter.channelsOwned?.remove(at: indexPath.row)
                tableView.deleteRows(at: [indexPath], with: .fade)
                Utilities.showSpinner()
                GFNetworkServices.deleteChannel(channelId: channelId) { (success, error) in
                    DispatchQueue.main.async {
                        Utilities.hideSpinner()
                    }
                    guard error == nil else {
                        DispatchQueue.main.async {
                            let alert = UIAlertController(title: "Error",
                                                          message: "Something unexpected happened. Please try again later.",
                                                          preferredStyle: .alert)
                            let ok = UIAlertAction(title: "OK", style: .default, handler: nil)
                            alert.addAction(ok)
                            self.present(alert, animated: true)
                        }
                        return
                    }
                    if success {
                        if let userId = User.userId, userId != "" {
                            DispatchQueue.main.async {
                                Utilities.showSpinner()
                            }
                            GFNetworkServices.getUserProfile(userId) { (success, flexter, error) in
                                DispatchQueue.main.async {
                                    Utilities.hideSpinner()
                                }
                                guard error == nil else {
                                    return
                                }
                                if success {
                                    if let flexter = flexter {
                                        User.updateTheUser(flexter)
                                    }
                                    DispatchQueue.main.async {
                                        self.tableView.reloadData()
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

// MARK: - Profile Delegate

extension ProfileViewController: ProfileDelegate {
    
    func didTapForProfileAction(_ type: ProfileUpdateDataType, _ object: Any? = nil) {
        switch type {
        case .goToEdit:
            let gfvc = GetFlexterNameViewController.storyboardInstance()
            gfvc.isFromProfile = true
            DispatchQueue.main.async {
                self.navigationController?.pushViewController(gfvc, animated: true)
                return
            }
        case .photo:
            DispatchQueue.main.async {
                let pvc = PortraitViewController.storyboardInstance()
                self.navigationController?.pushViewController(pvc, animated: true)
                return
            }
        case .flexes:
            collectionViewType = .flexes
        case .following:
            collectionViewType = .following
        case .channels:
            if AppInfo.channelsAreActive {
                collectionViewType = .channels
            } else {
                GFSpinnerView.showIn(view: self.view, message: "channels.off".localized)
            }
        case .follower:
            collectionViewType = .follower
        case .goToInvites:
            DispatchQueue.main.async {
                if let channelDetail = object as? ChannelDetail {
                    let invite = InviteViewController.storyboardInstance()
                    invite.channelDetail = channelDetail
                    self.navigationController?.pushViewController(invite, animated: true)
                }
                return
            }
        case .goToChezLui: // object is integer row and flexArray
            if let chezLui = object as? ChezLuiDetail, let flexArray = chezLui.flexArray, let row = chezLui.row {
                DispatchQueue.main.async {
                    let home = HomeViewController.storyboardInstance()
                    home.chezLui = self.flexter?.flexterName ?? "Visiting"
                    home.showChezLui = true
                    let chez = ChezLuiViewController.storyboardInstance()
                    chez.flexArray = flexArray
                    chez.currentRow = row
                    self.title = ""
                    let _ = home.view
                    // it's the same as channels -> main
                    let oldVC = home.children[0]
                    home.addChild(chez)
                    oldVC.willMove(toParent: nil)
                    let width = self.view.frame.size.width
                    chez.view.frame = CGRect(x: -width, y: 0, width: width, height: self.view.frame.size.height)
                    let endFrame = CGRect(x: 2*width, y: 0, width: width, height: self.view.frame.size.height)
                    home.transition(from: oldVC, to: chez, duration: 0.25, options: .curveEaseOut) {
                        chez.view.frame = oldVC.view.frame
                        oldVC.view.frame = endFrame
                    } completion: { (ended) in
                        oldVC.removeFromParent()
                        chez.didMove(toParent: self)
                    }

                    home.addChild(chez)
                    home.mainContainerView.addSubview(chez.view)
                    chez.didMove(toParent: self)
                    chez.modalPresentationStyle = .overCurrentContext
                    self.navigationController?.pushViewController(home, animated: true)
                    
                }
            }
        case .goToProfile:
            if let flexterId = object as? String {
                var flextr = Flexter()
                flextr.userId = flexterId
                let pvc = ProfileViewController.storyboardInstance()
                pvc.flexter = flextr
                title = "" // hides the 'back'
                navigationController?.pushViewController(pvc, animated: true)
            }

        default: break
        }
        tableView.reloadData()
    }
}

// MARK: - Home Delegate

extension ProfileViewController: HomeDelegate {
    func updateSideBar(objects: [SideBarObject]) {
        /* */
    }
    
    func didTapToFollow(_ sender: UIButton, _ tableViewCell: UITableViewCell?) {
        Utilities.showSpinner()
        guard let cell = tableViewCell as? FollowingTableViewCell else {
            // not the user's profile, it's a potential following
            if let following = flexter?.userId {
                let isFollowing = User.flexter.following?.filter({$0.channelId == flexter?.userId}).first != nil
                if isFollowing {
                    self.unfollowThis([following]) { (_, _) in
                    }
                } else {
                    GFNetworkServices.followThis([following]) { (success, error) in
                        DispatchQueue.main.async {
                            Utilities.hideSpinner()
                        }
                        if success {
                            DispatchQueue.main.async {
                                self.tableView.reloadData()
                            }
                        }
                    }
                }
            }
            return
        }
        let row = (tableView.indexPath(for: cell)?.row ?? 2) - 2
        if let following = User.flexter.following?[row].channelId {
            self.unfollowThis([following]) { (_, _) in
            }
        }
    }
    
    private func unfollowThis(_ input: [String], _ closure: @escaping (_ success: Bool, _ error: Error?) -> Void) {
        GFNetworkServices.unFollowThis(input) { (success, error) in
            DispatchQueue.main.async {
                Utilities.hideSpinner()
            }
            if success {
                DispatchQueue.main.async {
                    self.tableView.reloadData()
                }
            }
        }
    }
}
