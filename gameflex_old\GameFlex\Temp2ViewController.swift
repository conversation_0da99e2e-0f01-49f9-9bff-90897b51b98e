//
//  Temp2ViewController.swift
//  GameFlex
//
//  Created by <PERSON> on 7/6/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import FirebaseAuth
import FirebaseInstanceID
import FirebaseMessaging

class Temp2ViewController: G<PERSON><PERSON>iewController {
    
    @IBOutlet weak var resetUserDetailsButton: UIButton!
    @IBOutlet weak var loginButton: UIButton!
    @IBOutlet weak var profileButton: UIButton!
    @IBOutlet weak var logoutButton: UIButton!
    
    @IBOutlet weak var subscribeToPushButton: UIButton!
    @IBOutlet weak var sendPush: UIButton!
    
    static func storyboardInstance() -> Temp2ViewController {
        let sb = UIStoryboard(name: "Main", bundle: nil)
        return sb.instantiateViewController(withIdentifier: String(describing: Temp2ViewController.self)) as! Temp2ViewController
    }
        
    override func viewDidLoad() {
        super.viewDidLoad()
        title = ""
        loginButton.setTitleColor(.gfGreen, for: .normal)
        profileButton.setTitleColor(.gfGreen, for: .normal)
        
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        // Make the navigation bar background clear
        navigationController?.navigationBar.setBackgroundImage(UIImage(), for: .default)
        navigationController?.navigationBar.shadowImage = UIImage()
        navigationController?.navigationBar.isTranslucent = true
        navigationController?.navigationBar.tintColor = .white
        // Make the navigation bar background clear
        navigationController?.navigationBar.setBackgroundImage(UIImage(), for: .default)
        navigationController?.navigationBar.shadowImage = UIImage()
        navigationController?.navigationBar.isTranslucent = true
        sendPush.isHidden = true
    }
    
    @IBAction func didTapButton(_ sender: Any) {
        switch sender as! UIButton {
        case loginButton:
            let svc = SignUpViewController.storyboardInstance()
            navigationController?.pushViewController(svc, animated: true)
        case profileButton:
            let pvc = ProfileViewController.storyboardInstance()
            navigationController?.pushViewController(pvc, animated: true)
        case resetUserDetailsButton:
            GFDefaults.resetUserDetails()
        case logoutButton:
            logout()
        case subscribeToPushButton:
            let current = UNUserNotificationCenter.current()
            current.getNotificationSettings(completionHandler: { (settings) in
                if settings.authorizationStatus == .notDetermined {
                    // Notification permission has not been asked yet, go for it!
                    UNUserNotificationCenter.current().requestAuthorization(options: [.badge, .sound, .alert]) { (result, error) in
                        if result {
                            GFDefaults.shared.hasAskedPushPermission = true
//                            self.sendPush.isHidden = false
                            self.getTheToken { (result) in
                                print("token = \(result)")
                            }
                        }
                    }
                } else if settings.authorizationStatus == .denied {
                    // Notification permission was previously denied, go to settings & privacy to re-enable
                    let alert = UIAlertController(title: "Go to Settings", message: "Push Notifications are Off. You can change this in Settings > GameFlex.", preferredStyle: .alert)
                    let cancel = UIAlertAction(title: "Cancel", style: .default, handler: nil)
                    let ok = UIAlertAction(title: "Goto Settings", style: .default) { (act) in
                        let settingsURL = URL(string: UIApplication.openSettingsURLString)
                        UIApplication.shared.open(settingsURL!, options: [:], completionHandler: { (_) in
                        })
                    }
                    alert.addAction(ok)
                    alert.addAction(cancel)
                    alert.preferredAction = ok
                    self.present(alert, animated: true, completion: nil)
                    GFDefaults.shared.hasAskedPushPermission = false
                } else if settings.authorizationStatus == .authorized {
                    // Notification permission was already granted
                    self.getTheToken { (result) in
                        print("token = \(result)")
                    }
                    DispatchQueue.main.async {
                        Messaging.messaging().subscribe(toTopic: "Main") { error in
                            
                            guard error == nil else {
                                let alert = UIAlertController(title: "Failed to Subscribe.", message: "Push Notifications are On.\nSubscription to the Main channel failed.\n\nTry again later.", preferredStyle: .alert)
                                let ok = UIAlertAction(title: "OK", style: .default, handler: nil)
                                alert.addAction(ok)
                                self.present(alert, animated: true, completion: nil)
                                return
                            }
                            let alert = UIAlertController(title: "Ready", message: "Push Notifications are On.\nYou are subscribed to Main channel.", preferredStyle: .alert)
                            let ok = UIAlertAction(title: "OK", style: .default, handler: nil)
                            alert.addAction(ok)
                            self.present(alert, animated: true, completion: nil)
                            
//                            self.sendPush.isHidden = false
                        }
                    }
                }
            })

        case sendPush:
            let alert = UIAlertController(title: "Not Working Yet", message: "This feature is not working (yet). It is engineering in progress.", preferredStyle: .alert)
            let ok = UIAlertAction(title: "OK", style: .default, handler: nil)
            alert.addAction(ok)
            self.present(alert, animated: true)
            
        default:
            UserDefaults.standard.removeObject(forKey: GFDefaults.kFavStickerFrequencyDictionary)
            UserDefaults.standard.removeObject(forKey: GFDefaults.kFavRecentStickers)
        }
    }
    
    private func getTheToken(_ closure: @escaping (_ result: String) -> Void) {
        InstanceID.instanceID().instanceID { (result, error) in
          if let error = error {
            print("Error fetching remote instance ID: \(error)")
          } else if let result = result {
            print("Remote InstanceID token: \(result.token)")
            closure(result.token)
          }
        }
    }
    
    private func logout() {
        let firebaseAuth = Auth.auth()
        if let _ = try? firebaseAuth.signOut() {
            GFDefaults.resetUserDetails()
            let alert = UIAlertController(title: "Success", message: "You have successfully logged out.", preferredStyle: .alert)
            let ok = UIAlertAction(title: "OK", style: .default, handler: nil)
            alert.addAction(ok)
            present(alert, animated: true, completion: nil)
        }
    }
}

extension Temp2ViewController: PermissionsDelegate {
    
    func didFinishWithPermissions() {
        navigationController?.popViewController(animated: true)
    }
}
