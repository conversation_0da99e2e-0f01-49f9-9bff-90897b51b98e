//
//  FlexManager.swift
//  GameFlex
//
//  Created by <PERSON> on 7/8/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import SwiftyJSON
import AWSRekognition

class FlexManager {
    
    static let shared = FlexManager()
    
    let context = CIContext(options: nil)

    static var flex: Flex?
    
    static var placeholders: [UIImage] = [#imageLiteral(resourceName: "loadingPlaceholder")] //[#imageLiteral(resourceName: "placeholderFlex1"),#imageLiteral(resourceName: "placeholderFlex2") ,#imageLiteral(resourceName: "placeholderFlex3") ,#imageLiteral(resourceName: "placeholderFlex4")  ]

    
    // initializes the new flex to collect image, tags, 
    static func newFlex() {
        var owner = Flexter()
        owner.flexterName = User.flexter.flexterName
        owner.userId = User.userId
        flex = Flex(owner: owner)
    }
    
    static func buildTheParameters(_ channel: String) -> [String: String] {
        var dict: [String: String] = [:]
//        dict["flexNumber"] = "\(GFDefaults.completedFlexCount + 1)"
//        dict["device"] = User.device ?? "unknown"
//        dict["appVersion"] = User.app
//        dict["osVersion"] = User.ios
        
//        if flex?.stickies?.contains(where: { $0.viewType == .text }) ?? false {
//            dict["textStickerUsed"] = true
//        }
//        if CameraViewModel.drawings.count > 0 {
//            dict["drawingsUsed"] = true
//        }
        if let isReflex = flex?.isReflex {
            dict["isReflex"] = "\(isReflex)"
        }
        if let parentFlexId = flex?.parentFlexId {
            dict["parentFlexId"] = parentFlexId
        }
        let score = FlexManager.flex?.safetyScore ?? 0.0
        dict = ["channelId": channel,
                "caption": "Words are useful in describing things, but flexes are better.",
                "safety": "\(score)"]
        if flex?.caption != nil {
            dict["caption"] = flex?.caption
        }
//        if !(flex?.hashtags?.isEmpty ?? true) {
//            let paramsJSON = JSON(flex?.hashtags! as Any)
//            let paramsString = paramsJSON.rawString(String.Encoding.utf8, options: JSONSerialization.WritingOptions.prettyPrinted)!
//
//            dict["hashtags"] = paramsString
//        }
        if User.flexter.flexterName != "" {
            dict["flexterName"] = User.flexter.flexterName
        }
        
        return dict
    }
    
    // delivers the same smilie for a given user tied to her userId
    static func randomImagePlaceholder(_ userId: String?) -> String {
        var result = "123"
        if userId != nil {
            result = userId?.trimmingCharacters(in: CharacterSet(charactersIn: "0123456789.").inverted) ?? "123"
        }
        let characterSet = CharacterSet(charactersIn: "0123456789").inverted
        let uId = result.components(separatedBy: characterSet).joined()
        let shorter = String(uId.prefix(Int(uId.count / 2)))
        let num = Int(shorter)?.quotientAndRemainder(dividingBy: 29).remainder

        return CameraViewModel.smelliesStickers[num ?? Int.random(in: 0..<CameraViewModel.smelliesStickers.count)]
    }
    
    static func safetyScore(_ results: [AWSRekognitionModerationLabel]) -> [Double: [String]] {
        var issues: [String] = []
        var count = 0
        var sum = 0.0
        for result in results {
            if let word = result.name, let num = result.confidence {
                if !AppInfo.overrideModerationLabels.contains(word) {
                    issues.append(word.lowercased())
                    count += 1
                    sum = sum + Double(truncating: num)/100.0
                }
            }
        }
        if count > 0 {
            return [sum/Double(count): issues]
        }
        return [0.0: []]
    }
    
    // if dateOfBirth is unknown, sets to AppInfo.childAge = 13 year old
    static func passesSafety(_ flex: Flex) -> Bool {
        if let birthdate = User.dateOfBirth ?? Calendar.current.date(byAdding: .year, value: -1 * Int(AppInfo.childAge), to: Date()) {
            let calcAge = Date().timeIntervalSince(birthdate)/(3600 * 365.25 * 24)
            if calcAge < AppInfo.teenAge {
                if flex.safetyScore >= AppInfo.safety {
                    return false
                }
            }
            return true
        }
        return true
    }
    
    static func safetyReasonString() -> String {
        var str = ""
        if let issues = FlexManager.flex?.safetyIssues {
            for issue in issues {
                if issue == issues.last {
                    str = "\(str) and \(issue)"
                } else {
                    str = "\(str), \(issue)"
                }
            }
        }
        return String(str.dropFirst(2))
    }
    
    static func psa() -> Flex {
        var flex = Flex()
        flex.createAtDate = Date()
        flex.flexterName = "GameFlex"
        if let urlString = Bundle.main.url(forResource: "PSA-Character_Flex-Smoothflex", withExtension: "png")?.absoluteString {
            flex.mediaUrl = [urlString]
        }
        flex.flexId = "PSA_001"

        return psaArray.randomElement() ?? flex
    }
    
    private static var psaArray: [Flex] {
        var arr: [Flex] = []
        var flex = Flex()
        flex.createAtDate = Date()
        flex.flexterName = "GameFlex"
        
        if let urlString = Bundle.main.url(forResource: "PSA-Character_Flex-Smoothflex", withExtension: "png")?.absoluteString {
            flex.mediaUrl = [urlString]
        }
        flex.flexId = "PSA_001"
        arr.append(flex)
        
        if let urlString = Bundle.main.url(forResource: "PSA-GameOver", withExtension: "jpg")?.absoluteString {
            flex.mediaUrl = [urlString]
        }
        flex.flexId = "PSA_002"
        arr.append(flex)
        
        if let urlString = Bundle.main.url(forResource: "PSA-MakeMoreFlex", withExtension: "jpg")?.absoluteString {
            flex.mediaUrl = [urlString]
        }
        flex.flexId = "PSA_003"
        arr.append(flex)

        
        return arr
    }
}
