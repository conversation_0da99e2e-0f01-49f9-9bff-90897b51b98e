<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17703"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="iN0-l3-epB" customClass="FollowingTableViewCell" customModule="GameFlex" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="414" height="66"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view clipsSubviews="YES" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bcm-7V-vnC">
                    <rect key="frame" x="20" y="10" width="32" height="32"/>
                    <subviews>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" insetsLayoutMarginsFromSafeArea="NO" translatesAutoresizingMaskIntoConstraints="NO" id="xd4-z3-6ka">
                            <rect key="frame" x="-8" y="-8" width="48" height="48"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="48" id="Gyf-WA-DXR"/>
                                <constraint firstAttribute="width" constant="48" id="lyn-Gt-qYv"/>
                            </constraints>
                        </imageView>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="xd4-z3-6ka" firstAttribute="centerY" secondItem="bcm-7V-vnC" secondAttribute="centerY" id="QDZ-hC-3BE"/>
                        <constraint firstAttribute="width" constant="32" id="Rvu-6g-XuN"/>
                        <constraint firstItem="xd4-z3-6ka" firstAttribute="centerX" secondItem="bcm-7V-vnC" secondAttribute="centerX" id="lDM-Tp-OW5"/>
                        <constraint firstAttribute="height" constant="32" id="lq4-s1-oXb"/>
                    </constraints>
                </view>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" insetsLayoutMarginsFromSafeArea="NO" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="SjI-Ld-wo9">
                    <rect key="frame" x="68" y="10" width="47" height="22"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="gCl-Za-4IE"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="18"/>
                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <nil key="highlightedColor"/>
                </label>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ZEq-GU-cNM">
                    <rect key="frame" x="68" y="31" width="31" height="15"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="15" id="F65-FT-QTY"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                    <color key="textColor" red="0.95686274510000002" green="0.95686274510000002" blue="0.95686274510000002" alpha="0.30356378420000002" colorSpace="calibratedRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Iag-To-Z0J">
                    <rect key="frame" x="12" y="2" width="48" height="48"/>
                    <connections>
                        <action selector="didTapButton:" destination="iN0-l3-epB" eventType="touchUpInside" id="Add-bP-Co2"/>
                    </connections>
                </button>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="DRC-Q3-AZQ">
                    <rect key="frame" x="318" y="21" width="80" height="24"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="24" id="bju-CB-ODN"/>
                        <constraint firstAttribute="width" constant="80" id="hNK-aI-Yn6"/>
                    </constraints>
                    <state key="normal" title="Following"/>
                    <connections>
                        <action selector="didTapButton:" destination="iN0-l3-epB" eventType="touchUpInside" id="hYi-dc-dq7"/>
                    </connections>
                </button>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="SjI-Ld-wo9" secondAttribute="trailing" constant="10" id="0pQ-BX-hV1"/>
                <constraint firstAttribute="trailing" secondItem="DRC-Q3-AZQ" secondAttribute="trailing" constant="16" id="1f1-YK-HBY"/>
                <constraint firstItem="Iag-To-Z0J" firstAttribute="bottom" secondItem="xd4-z3-6ka" secondAttribute="bottom" id="3dC-YT-hz4"/>
                <constraint firstItem="SjI-Ld-wo9" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="10" id="4lZ-Yk-n0C"/>
                <constraint firstItem="Iag-To-Z0J" firstAttribute="trailing" secondItem="xd4-z3-6ka" secondAttribute="trailing" id="FI8-PZ-ZQp"/>
                <constraint firstItem="Iag-To-Z0J" firstAttribute="top" secondItem="xd4-z3-6ka" secondAttribute="top" id="IgU-OT-vrV"/>
                <constraint firstItem="bcm-7V-vnC" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="10" id="VSh-ey-RUT"/>
                <constraint firstItem="DRC-Q3-AZQ" firstAttribute="centerY" secondItem="iN0-l3-epB" secondAttribute="centerY" id="dnc-WR-PsT"/>
                <constraint firstItem="ZEq-GU-cNM" firstAttribute="top" secondItem="SjI-Ld-wo9" secondAttribute="bottom" constant="-1" id="iWc-B5-YHa"/>
                <constraint firstItem="Iag-To-Z0J" firstAttribute="leading" secondItem="xd4-z3-6ka" secondAttribute="leading" id="lac-6j-lhD"/>
                <constraint firstItem="ZEq-GU-cNM" firstAttribute="leading" secondItem="bcm-7V-vnC" secondAttribute="trailing" constant="16" id="u13-uc-U3V"/>
                <constraint firstItem="SjI-Ld-wo9" firstAttribute="leading" secondItem="bcm-7V-vnC" secondAttribute="trailing" constant="16" id="xI9-2s-1gL"/>
                <constraint firstItem="bcm-7V-vnC" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="20" symbolic="YES" id="xIE-cW-Mha"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="followButton" destination="DRC-Q3-AZQ" id="3SJ-Bg-2pW"/>
                <outlet property="profileButton" destination="Iag-To-Z0J" id="00G-kc-0ai"/>
                <outlet property="profileImage" destination="xd4-z3-6ka" id="w5h-zZ-9z4"/>
                <outlet property="profileSuperView" destination="bcm-7V-vnC" id="ORj-LX-mnw"/>
                <outlet property="subTitleLabel" destination="ZEq-GU-cNM" id="kQR-19-b6C"/>
                <outlet property="titleLabel" destination="SjI-Ld-wo9" id="R43-eh-jI8"/>
            </connections>
            <point key="canvasLocation" x="40.579710144927539" y="-206.25"/>
        </view>
    </objects>
</document>
