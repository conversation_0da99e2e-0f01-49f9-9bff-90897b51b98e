<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17156" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17125"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="iN0-l3-epB" customClass="EditProfileTextViewTableViewCell" customModule="GameFlex" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="414" height="114"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gSL-jh-wSj">
                    <rect key="frame" x="16" y="8" width="45" height="21"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="21" id="o92-8f-OUy"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="17"/>
                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <nil key="highlightedColor"/>
                </label>
                <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="PiX-sw-43Q">
                    <rect key="frame" x="16" y="37" width="382" height="67"/>
                    <color key="backgroundColor" white="0.33333333333333331" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                    <textInputTraits key="textInputTraits" autocapitalizationType="sentences" autocorrectionType="yes" spellCheckingType="yes" keyboardAppearance="alert"/>
                </textView>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="pGG-tC-VEt">
                    <rect key="frame" x="356" y="8" width="42" height="21"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="21" id="boo-3B-Gkz"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                    <color key="textColor" red="0.95686274510000002" green="0.95686274510000002" blue="0.95686274510000002" alpha="0.30356378420000002" colorSpace="calibratedRGB"/>
                    <nil key="highlightedColor"/>
                </label>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="PiX-sw-43Q" firstAttribute="top" secondItem="gSL-jh-wSj" secondAttribute="bottom" constant="8" id="6xR-Wx-9At"/>
                <constraint firstItem="pGG-tC-VEt" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="8" id="AXN-YI-tT8"/>
                <constraint firstAttribute="bottom" secondItem="PiX-sw-43Q" secondAttribute="bottom" constant="10" id="Aat-nj-ohb"/>
                <constraint firstAttribute="trailing" secondItem="pGG-tC-VEt" secondAttribute="trailing" constant="16" id="Dzj-gs-Loi"/>
                <constraint firstItem="gSL-jh-wSj" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="16" id="FfC-Oc-exU"/>
                <constraint firstItem="PiX-sw-43Q" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="16" id="QV0-O4-QXj"/>
                <constraint firstAttribute="trailing" secondItem="PiX-sw-43Q" secondAttribute="trailing" constant="16" id="VeU-eo-H9q"/>
                <constraint firstItem="gSL-jh-wSj" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="8" id="beo-Bk-3jE"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="characterCountLabel" destination="pGG-tC-VEt" id="1Eh-L5-LCP"/>
                <outlet property="textView" destination="PiX-sw-43Q" id="nW7-W5-eaS"/>
                <outlet property="titleLabel" destination="gSL-jh-wSj" id="AMI-Qa-FDA"/>
            </connections>
            <point key="canvasLocation" x="92.753623188405811" y="-91.071428571428569"/>
        </view>
    </objects>
</document>
