//
//  StickerCollectionViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 7/27/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

class StickerCollectionViewCell: UICollectionViewCell {
    
    @IBOutlet weak var imageView: UIImageView!
    @IBOutlet weak var backgrounder: UIView!
    
    static var cellIdentifier = String(describing: StickerCollectionViewCell.self)
    
    override func awakeFromNib() {
        super.awakeFromNib()
        backgrounder.layer.cornerRadius = 8
        backgrounder.layer.borderColor = UIColor.gfOffWhite.cgColor
        backgrounder.layer.borderWidth = 1
        backgrounder.clipsToBounds = true
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        imageView.image = nil
        backgrounder.isHidden = false
        isUserInteractionEnabled = true
    }
}
