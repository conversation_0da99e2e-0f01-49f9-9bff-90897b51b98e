-- Create function to increment post likes
CREATE OR REPLACE FUNCTION increment_post_likes(post_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE gameflex.posts 
    SET like_count = like_count + 1 
    WHERE id = post_id;
END;
$$ language 'plpgsql';

-- Create function to decrement post likes
CREATE OR REPLACE FUNCTION decrement_post_likes(post_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE gameflex.posts 
    SET like_count = GREATEST(like_count - 1, 0)
    WHERE id = post_id;
END;
$$ language 'plpgsql';
