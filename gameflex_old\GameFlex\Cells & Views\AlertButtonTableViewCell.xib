<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17506" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17505"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clipsSubviews="YES" contentMode="scaleToFill" restorationIdentifier="AlertButtonTableViewCell" insetsLayoutMarginsFromSafeArea="NO" id="iN0-l3-epB" customClass="AlertButtonTableViewCell" customModule="GameFlex" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="414" height="72"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="K55-pp-GUl">
                    <rect key="frame" x="0.0" y="1.5" width="414" height="69"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="RHf-xj-es4">
                            <rect key="frame" x="14" y="12" width="45" height="45"/>
                            <subviews>
                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="9T4-3n-pTs">
                                    <rect key="frame" x="0.0" y="0.0" width="45" height="45"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="45" id="HT7-qx-rnu"/>
                                        <constraint firstAttribute="height" constant="45" id="Z4Z-Zd-2LN"/>
                                    </constraints>
                                </imageView>
                            </subviews>
                            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            <constraints>
                                <constraint firstItem="9T4-3n-pTs" firstAttribute="centerY" secondItem="RHf-xj-es4" secondAttribute="centerY" id="DdA-h9-apm"/>
                                <constraint firstAttribute="width" constant="45" id="QXO-yM-gAi"/>
                                <constraint firstItem="9T4-3n-pTs" firstAttribute="centerX" secondItem="RHf-xj-es4" secondAttribute="centerX" id="kvs-po-Sg6"/>
                                <constraint firstAttribute="height" constant="45" id="vOx-QE-Oav"/>
                            </constraints>
                        </view>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="4bg-Gr-SCL">
                            <rect key="frame" x="14" y="12" width="45" height="45"/>
                            <connections>
                                <action selector="didTapButton:" destination="iN0-l3-epB" eventType="touchUpInside" id="Buv-dL-7iR"/>
                            </connections>
                        </button>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="2bt-fe-Dlo">
                            <rect key="frame" x="69" y="1" width="255" height="67"/>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="DkY-Jy-8G7">
                            <rect key="frame" x="326" y="22.5" width="80" height="24"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="24" id="Dus-4u-mkb"/>
                                <constraint firstAttribute="width" constant="80" id="KNT-Jc-AlG"/>
                            </constraints>
                            <color key="tintColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <state key="normal" title="Following">
                                <color key="titleColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </state>
                            <connections>
                                <action selector="didTapButton:" destination="iN0-l3-epB" eventType="touchUpInside" id="ul6-aj-G9H"/>
                            </connections>
                        </button>
                    </subviews>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="bottom" secondItem="2bt-fe-Dlo" secondAttribute="bottom" constant="1" id="1ob-89-hKz"/>
                        <constraint firstItem="DkY-Jy-8G7" firstAttribute="centerY" secondItem="K55-pp-GUl" secondAttribute="centerY" id="5AD-iM-1QX"/>
                        <constraint firstItem="4bg-Gr-SCL" firstAttribute="trailing" secondItem="RHf-xj-es4" secondAttribute="trailing" id="KO6-R5-lrc"/>
                        <constraint firstItem="2bt-fe-Dlo" firstAttribute="top" secondItem="K55-pp-GUl" secondAttribute="top" constant="1" id="MMo-LM-Khh"/>
                        <constraint firstAttribute="trailing" secondItem="2bt-fe-Dlo" secondAttribute="trailing" constant="90" id="QO3-Ye-zPm"/>
                        <constraint firstItem="RHf-xj-es4" firstAttribute="leading" secondItem="K55-pp-GUl" secondAttribute="leading" constant="14" id="WIo-sT-phP"/>
                        <constraint firstItem="RHf-xj-es4" firstAttribute="centerY" secondItem="K55-pp-GUl" secondAttribute="centerY" id="dcp-Md-zZa"/>
                        <constraint firstItem="4bg-Gr-SCL" firstAttribute="top" secondItem="RHf-xj-es4" secondAttribute="top" id="kDw-dT-e7r"/>
                        <constraint firstAttribute="trailing" secondItem="DkY-Jy-8G7" secondAttribute="trailing" constant="8" id="kg9-JL-MiW"/>
                        <constraint firstItem="2bt-fe-Dlo" firstAttribute="leading" secondItem="RHf-xj-es4" secondAttribute="trailing" constant="10" id="o4O-oc-Vii"/>
                        <constraint firstItem="4bg-Gr-SCL" firstAttribute="leading" secondItem="RHf-xj-es4" secondAttribute="leading" id="ppl-UZ-NMt"/>
                        <constraint firstItem="4bg-Gr-SCL" firstAttribute="bottom" secondItem="RHf-xj-es4" secondAttribute="bottom" id="uUl-cG-YHS"/>
                    </constraints>
                </view>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="K55-pp-GUl" secondAttribute="trailing" id="09e-oj-WJe"/>
                <constraint firstAttribute="bottom" secondItem="K55-pp-GUl" secondAttribute="bottom" constant="1.5" id="55M-gb-5uS"/>
                <constraint firstItem="K55-pp-GUl" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" id="FxB-hF-stm"/>
                <constraint firstItem="K55-pp-GUl" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="1.5" id="wej-Ic-w0A"/>
            </constraints>
            <nil key="simulatedTopBarMetrics"/>
            <nil key="simulatedBottomBarMetrics"/>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="cellView" destination="K55-pp-GUl" id="oKE-wE-VlZ"/>
                <outlet property="followButton" destination="DkY-Jy-8G7" id="a0I-nt-PhP"/>
                <outlet property="profileButton" destination="4bg-Gr-SCL" id="JCt-4J-TjC"/>
                <outlet property="sourceImageView" destination="9T4-3n-pTs" id="aff-uw-biF"/>
                <outlet property="sourceSuperView" destination="RHf-xj-es4" id="k1W-JS-3DO"/>
                <outlet property="titleLabel" destination="2bt-fe-Dlo" id="kg6-NO-sZa"/>
            </connections>
            <point key="canvasLocation" x="175" y="-304"/>
        </view>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
