//
//  SafeAreaFixTabBar.swift
//  GameFlex
//
//  Created by <PERSON> on 9/9/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//
//  see: https://stackoverflow.com/questions/46214740/
//  see: https://stackoverflow.com/questions/26494130/remove-tab-bar-item-text-show-only-image

import UIKit

class SafeAreaFixTabBar: UITabBar {
    
    override func layoutSubviews() {
        super.layoutSubviews()
        let verticalOffset: CGFloat = 0.0

        let imageInset = UIEdgeInsets(
            top: verticalOffset,
            left: 0.0,
            bottom: -verticalOffset,
            right: 0.0
        )

        for tabBarItem in items ?? [] {
            tabBarItem.title = ""
            tabBarItem.imageInsets = imageInset
        }
        FeedViewModel.chezLuiDummyTabBarHeight = CGFloat(frame.size.height)
    }
}
