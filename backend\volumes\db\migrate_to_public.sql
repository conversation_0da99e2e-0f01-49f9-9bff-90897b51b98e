-- Move tables from gameflex schema to public schema for Supabase compatibility

-- Move users table
ALTER TABLE gameflex.users SET SCHEMA public;

-- Move user_profiles table
ALTER TABLE gameflex.user_profiles SET SCHEMA public;

-- Move channels table
ALTER TABLE gameflex.channels SET SCHEMA public;

-- Move channel_members table
ALTER TABLE gameflex.channel_members SET SCHEMA public;

-- Move posts table
ALTER TABLE gameflex.posts SET SCHEMA public;

-- Move comments table
ALTER TABLE gameflex.comments SET SCHEMA public;

-- Move likes table
ALTER TABLE gameflex.likes SET SCHEMA public;

-- Move follows table
ALTER TABLE gameflex.follows SET SCHEMA public;

-- Move notifications table
ALTER TABLE gameflex.notifications SET SCHEMA public;

-- Update functions to work with public schema
DROP FUNCTION IF EXISTS increment_post_likes(UUID);
DROP FUNCTION IF EXISTS decrement_post_likes(UUID);

-- Recreate functions in public schema
CREATE OR REPLACE FUNCTION increment_post_likes(post_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE public.posts 
    SET like_count = like_count + 1 
    WHERE id = post_id;
END;
$$ language 'plpgsql';

CREATE OR REPLACE FUNCTION decrement_post_likes(post_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE public.posts 
    SET like_count = GREATEST(like_count - 1, 0)
    WHERE id = post_id;
END;
$$ language 'plpgsql';

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO anon, authenticated;

-- Enable RLS (Row Level Security) for Supabase
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.channels ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.channel_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.follows ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- Create basic RLS policies for posts (allow read access)
CREATE POLICY "Posts are viewable by everyone" ON public.posts
    FOR SELECT USING (true);

CREATE POLICY "Users can insert their own posts" ON public.posts
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own posts" ON public.posts
    FOR UPDATE USING (auth.uid() = user_id);

-- Create basic RLS policies for likes
CREATE POLICY "Likes are viewable by everyone" ON public.likes
    FOR SELECT USING (true);

CREATE POLICY "Users can insert their own likes" ON public.likes
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own likes" ON public.likes
    FOR DELETE USING (auth.uid() = user_id);

-- Create basic RLS policies for users
CREATE POLICY "Users are viewable by everyone" ON public.users
    FOR SELECT USING (true);

CREATE POLICY "Users can update their own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);
