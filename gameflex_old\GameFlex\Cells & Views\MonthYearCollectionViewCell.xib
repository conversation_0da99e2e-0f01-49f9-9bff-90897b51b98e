<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="16097.3" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="16087"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="iN0-l3-epB" customClass="MonthYearCollectionViewCell" customModule="GameFlex" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="414" height="73"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="e3M-e0-J6k">
                    <rect key="frame" x="1" y="1" width="412" height="71"/>
                    <color key="backgroundColor" systemColor="systemBackgroundColor" cocoaTouchSystemColor="whiteColor"/>
                </view>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="nLu-gp-BgG">
                    <rect key="frame" x="157" y="22" width="100" height="29"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="100" id="Vbx-iB-hwC"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="24"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
            </subviews>
            <color key="backgroundColor" systemColor="systemBackgroundColor" cocoaTouchSystemColor="whiteColor"/>
            <constraints>
                <constraint firstItem="nLu-gp-BgG" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="6eO-wd-YZc"/>
                <constraint firstItem="nLu-gp-BgG" firstAttribute="centerY" secondItem="iN0-l3-epB" secondAttribute="centerY" id="Uox-wZ-CWc"/>
                <constraint firstAttribute="bottom" secondItem="e3M-e0-J6k" secondAttribute="bottom" constant="1" id="Ywu-oj-wfm"/>
                <constraint firstItem="e3M-e0-J6k" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="1" id="ga4-Sc-q9z"/>
                <constraint firstItem="e3M-e0-J6k" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="1" id="lhP-rl-h6x"/>
                <constraint firstAttribute="trailing" secondItem="e3M-e0-J6k" secondAttribute="trailing" constant="1" id="yfF-8l-ubl"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="cellBackground" destination="e3M-e0-J6k" id="HJR-xt-KwI"/>
                <outlet property="label" destination="nLu-gp-BgG" id="0Hf-Bs-4LO"/>
            </connections>
            <point key="canvasLocation" x="-94.20289855072464" y="-546.76339285714278"/>
        </view>
    </objects>
</document>
