<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17156" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17125"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="iN0-l3-epB" customClass="HashTagsTableViewCell" customModule="GameFlex" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="414" height="42"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Frequent" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fTv-bd-iqH">
                    <rect key="frame" x="16" y="10.5" width="74" height="21"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="21" id="T7g-2U-YaT"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="17"/>
                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <nil key="highlightedColor"/>
                </label>
                <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" dataMode="none" translatesAutoresizingMaskIntoConstraints="NO" id="JSf-e6-M2i">
                    <rect key="frame" x="105" y="0.0" width="309" height="42"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <collectionViewFlowLayout key="collectionViewLayout" scrollDirection="horizontal" minimumLineSpacing="10" minimumInteritemSpacing="10" id="e32-ms-5c7">
                        <size key="itemSize" width="128" height="128"/>
                        <size key="headerReferenceSize" width="0.0" height="0.0"/>
                        <size key="footerReferenceSize" width="0.0" height="0.0"/>
                        <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                    </collectionViewFlowLayout>
                </collectionView>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="fTv-bd-iqH" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="16" id="0nw-gI-xfN"/>
                <constraint firstAttribute="trailing" secondItem="JSf-e6-M2i" secondAttribute="trailing" id="Ijc-rY-bzi"/>
                <constraint firstItem="JSf-e6-M2i" firstAttribute="leading" secondItem="fTv-bd-iqH" secondAttribute="trailing" constant="15" id="TCq-sW-lnk"/>
                <constraint firstItem="JSf-e6-M2i" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="bTO-yU-2C3"/>
                <constraint firstAttribute="bottom" secondItem="JSf-e6-M2i" secondAttribute="bottom" id="ied-hh-m5o"/>
                <constraint firstItem="fTv-bd-iqH" firstAttribute="centerY" secondItem="iN0-l3-epB" secondAttribute="centerY" id="kn1-dp-UZm"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="collectionView" destination="JSf-e6-M2i" id="r8a-BR-Xl9"/>
                <outlet property="titleLabel" destination="fTv-bd-iqH" id="N9y-I8-YhQ"/>
            </connections>
            <point key="canvasLocation" x="101.44927536231884" y="-152.67857142857142"/>
        </view>
    </objects>
</document>
