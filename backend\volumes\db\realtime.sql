-- Realtime setup for Supabase
-- This script sets up the realtime functionality

-- Create realtime schema
CREATE SCHEMA IF NOT EXISTS _realtime;

-- Create realtime extension
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;

-- Create realtime publication
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_publication WHERE pubname = 'supabase_realtime'
  ) THEN
    CREATE PUBLICATION supabase_realtime;
  END IF;
END $$;

-- Add tables to realtime publication
ALTER PUBLICATION supabase_realtime ADD TABLE gameflex.posts;
ALTER PUBLICATION supabase_realtime ADD TABLE gameflex.comments;
ALTER PUBLICATION supabase_realtime ADD TABLE gameflex.likes;
ALTER PUBLICATION supabase_realtime ADD TABLE gameflex.notifications;
ALTER PUBLICATION supabase_realtime ADD TABLE gameflex.channel_members;

-- Create realtime functions
CREATE OR REPLACE FUNCTION _realtime.apply_rls(wal jsonb, max_record_bytes int = 1048576)
R<PERSON>URNS SETOF _realtime.wal_rls
LANGUAGE sql
STABLE
AS $$
    SELECT
        *
    FROM
        _realtime.wal_rls
    WHERE
        wal_rls.wal = apply_rls.wal
$$;

-- Grant permissions
GRANT USAGE ON SCHEMA _realtime TO anon, authenticated, service_role;
GRANT ALL ON ALL TABLES IN SCHEMA _realtime TO anon, authenticated, service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA _realtime TO anon, authenticated, service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA _realtime TO anon, authenticated, service_role;
