//
//  FirstViewController.swift
//  GameFlex
//
//  Created by <PERSON> on 9/27/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import AVKit

class FirstViewController: GFViewController {
    
    static func storyboardInstance() -> FirstViewController {
        let sb = UIStoryboard(name: "Main", bundle: nil)
        return sb.instantiateViewController(withIdentifier: String(describing: FirstViewController.self)) as! FirstViewController
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        view.alpha = 0.0
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(fileComplete),
            name: NSNotification.Name.AVPlayerItemDidPlayToEndTime,
            object: nil)
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            if User.shouldShowSwords {
                do{
                   try AVAudioSession.sharedInstance().setCategory(.ambient)
                   try AVAudioSession.sharedInstance().setActive(true, options: .notifyOthersOnDeactivation)
                } catch {
                   NSLog(error.localizedDescription)
                }
                User.shouldShowSwords = false
                if let movieURL = Bundle.main.url(forResource: "swords", withExtension: "mp4") {
                    let pvc = AVPlayerViewController()
                    pvc.player = AVPlayer(url: movieURL)
                    pvc.showsPlaybackControls = false
                    User.shouldShowSwords = false
                    self.present(pvc, animated: true) {
                        self.view.alpha = 1.0
                        pvc.player?.play()
                    }
                }
            }
        }
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
    }
    
    @objc private func fileComplete() {
        /* presentingViewController = LandingPageViewController
           presentedViewController  = AVPlayerViewController
           self                     = FirstViewController
         */
        if let pvc = self.presentedViewController as? AVPlayerViewController {
            pvc.player?.pause()
            pvc.player = nil
            pvc.dismiss(animated: false)
        }
        self.dismiss(animated: true) {
            OnboardingStateMachine.didComplete(this: .first)
        }
    }
}
