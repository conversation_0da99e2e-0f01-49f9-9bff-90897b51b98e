//
//  ReflexViewController.swift
//  GameFlex
//
//  Created by <PERSON> on 10/31/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import Kingfisher

class ReflexViewController: GFChannelViewController {
    
    @IBOutlet weak var collectionView: UICollectionView!
    
    var currentRow = 0
    var direction: ScrollDirection = .left
    var flexId: String?
    var lagToRestartTimer: Timer?
    var reflexArray: [Flex] = []
    var timer: Timer?
    var numberOfReflexes: Int = 1
    
    // MARK: - life cycle funcs
    
    static func storyboardInstance() -> ReflexViewController {
        let sb = UIStoryboard(name: "Main", bundle:     nil)
        return sb.instantiateViewController(withIdentifier: String(describing: ReflexViewController.self)) as! ReflexViewController
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(UICollectionViewCell.self, forCellWithReuseIdentifier: "cell")
        collectionView.register(UINib(nibName: FlexCollectionViewCell.cellIdentifier, bundle: nil), forCellWithReuseIdentifier: FlexCollectionViewCell.cellIdentifier)
        collectionView.gestureRecognizers = []
        collectionView.contentInsetAdjustmentBehavior = .never
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        Utilities.showSpinner()
        refreshReflexes(top: true)
        //
        if let butt = butt {
            didTap(butt)
        }
        if collectionView.gestureRecognizers?.count == 0 {
            let swipeRight = UISwipeGestureRecognizer(target: self, action: #selector(didSwipeRight(_:)))
            swipeRight.direction = .right
            collectionView.addGestureRecognizer(swipeRight)
            let swipeLeft = UISwipeGestureRecognizer(target: self, action: #selector(didSwipeLeft(_:)))
            swipeLeft.direction = .left
            collectionView.addGestureRecognizer(swipeLeft)
        }
    }
        
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        timer?.invalidate()
    }
    
    // MARK: - gesture recognizer handling
    
    // cell moves left... indexPath.row increases
    @objc func didSwipeLeft(_ sender: UIGestureRecognizer?) {
        direction = .left
        guard !(reflexArray.last?.flexId?.contains("PSA") ?? false) else { return }
        if let cell = self.collectionView.visibleCells.last {
            guard let row = self.collectionView.indexPath(for: cell)?.row else { return }
            if row == reflexArray.count - 2, reflexArray.count > 4 {
                refreshReflexes(top: false)
            }
            currentRow += 1
            guard currentRow < reflexArray.count else {
                if let home = parent as? HomeViewController {
                    home.returnToSourceViewController()
                    home.lightUpPlayButton(false)
                }
                return
            }
            if row != currentRow, currentRow < collectionView.numberOfItems(inSection: 0) {
                collectionView.scrollToItem(at: IndexPath(row: currentRow, section: 0), at: .left, animated: true)
                animateTheCounter(for: currentRow)
            }
        }
        if collectionView.visibleCells.count == 0 {
            collectionView.reloadData()
        }
    }
    
    private func animateTheCounter(for row: Int) {
        if let home = parent as? HomeViewController {
            home.doCounterTimers(for: row)
        }
    }
    
    // cell moves right... indexPath.row decreases
    @objc func didSwipeRight(_ sender: UIGestureRecognizer?) {
        if let cell = collectionView?.visibleCells.last {
            guard let row = self.collectionView.indexPath(for: cell)?.row else { return }
            currentRow -= 1
            guard currentRow > -1 else {
                currentRow = 0
                if let home = parent as? HomeViewController {
                    home.returnToSourceViewController()
                    home.lightUpPlayButton(false)
                }
                return
            }
            if row != currentRow {
                collectionView.scrollToItem(at: IndexPath(row: currentRow, section: 0), at: .left, animated: true)
            }
            self.direction = .right
        }
    }
    
    func refreshReflexes(top: Bool) {
        if let flexId = flexId {
            GFNetworkServices.getReflexes(top: top, for: flexId, ({ (success, result, error) in
                DispatchQueue.main.async {
                    Utilities.hideSpinner()
                }
                if !result.isEmpty {
                    self.reflexArray.append(contentsOf: result)
                    self.reflexArray = Array(Set(self.reflexArray))
                    self.reflexArray.sort(by: { $0.createAtDate?.timeIntervalSinceReferenceDate ?? 0.0 < $1.createAtDate?.timeIntervalSinceReferenceDate ?? 0.0 })
                    if let _ = GFDefaults.channelTimeStamps, let tim = self.reflexArray.last?.createAt {
                        GFDefaults.channelTimeStamps?[flexId] = tim
                    } else if let ti = self.reflexArray.last?.createAt {
                        GFDefaults.channelTimeStamps = [flexId: ti]
                    }

                    DispatchQueue.main.async {
                        self.collectionView.reloadData()
                        self.doTimer()
                    }
                } else {
                    if self.reflexArray.isEmpty { // nothing here, just move along
//                        DispatchQueue.main.async {
//                            if let home = self.parent as? HomeViewController {
//                                home.returnToSourceViewController()
//                                home.lightUpPlayButton(false)
//                            }
//                        }
                    }
                }
            }))
        }
    }
    
    // logged in user double taps the flex to like it
    @objc func likeThisFlex(_ gesture: UITapGestureRecognizer) {
//        self.didTapForChannelAction(.like)
    }
        
    func updateTheSideBar(objects: [SideBarObject]) {
        DispatchQueue.main.async {
            (self.parent as? HomeViewController)?.updateSideBar(objects: objects)
        }
    }

    @objc func doTimer() {
        if timer != nil {
            timer?.invalidate()
        }
        timer = Timer.scheduledTimer(timeInterval: AppInfo.reflexTime, target:self , selector: #selector(rotateCell), userInfo: nil, repeats: true)
        if currentRow == 0 {
            animateTheCounter(for: 0)
        }
    }
    
    func stopTimers() {
        timer?.invalidate()
        lagToRestartTimer?.invalidate()
    }
    
    @objc private func rotateCell() {
        didSwipeLeft(nil)
        
    }
    
    // suppresses the timers while being held
    @objc func didLongPress(_ sender: UILongPressGestureRecognizer) {
        if sender.state == .began {
            timer?.invalidate()
            lagToRestartTimer?.invalidate()
        }
        if sender.state == .ended {
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                self.doTimer()
                self.rotateCell()
            }
        }
    }
}

// MARK: - UITableViewDelegate and DataSource

extension ReflexViewController: UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return reflexArray.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {

        guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: FlexCollectionViewCell.cellIdentifier, for: indexPath) as? FlexCollectionViewCell else { return FlexCollectionViewCell() }
        cell.flexImageView.image = #imageLiteral(resourceName: "placeholderChannels")

        cell.gestureRecognizers = []
        let flex = reflexArray[indexPath.row]
        if let urlString = flex.mediaUrl?[0] {
        let url = URL(string: urlString)
        let processor = DownsamplingImageProcessor(size: cell.flexImageView.bounds.size)
        cell.flexImageView.kf.indicatorType = .activity
        cell.flexImageView.kf.setImage(
            with: url,
            placeholder: UIImage(named: "loadingPlaceholder"),//FlexManager.randomImagePlaceholder()),
                options: [
                    .processor(processor),
                    .scaleFactor(UIScreen.main.scale),
                    .transition(.fade(1)),
                    .cacheOriginalImage
                ], completionHandler:
                    {
                        result in
                        switch result {
                        case .success(let value):
                            print("Task done for: \(value.source.url?.absoluteString ?? "")")
                        case .failure(let error):
                            print("Job failed: \(error.localizedDescription)")
                        }
                    })
        }
        let long = UILongPressGestureRecognizer(target: self, action: #selector(didLongPress(_:)))
        cell.addGestureRecognizer(long)
        cell.tag = indexPath.row

        // update the sideBar
        var object = SideBarObject()
        object.buttonType = .like
        if let reaction = flex.reactions?.filter({ $0.type == .likes }).first {
            object.buttonCount = reaction.number ?? 0
        }
        if let flexId = flex.flexId, let userId = User.userId, userId != "" {
            if User.likedFlexes[userId]?.filter({ $0 == flexId }).count ?? 0 > 0 {
                object.isHighlighted = true
            } else {
                object.isHighlighted = false
            }
            
            // pass the caption to HomeViewController
            var object1 = SideBarObject()
            object1.buttonType = .caption
            var captionObject = CaptionObject()
            captionObject.caption = flex.caption
            captionObject.captionSourceId = flex.owner?.userId
            captionObject.captionFlexterName = flex.owner?.flexterName
            object1.captionData = captionObject
            
            // pass the comment count to HomeViewController
            var object2 = SideBarObject()
            object2.buttonType = .comment
            object2.buttonCount = flex.numberOfComments ?? 0
            object2.commentData = nil
            
            var object4 = SideBarObject()
            object4.buttonType = .reflex
            object4.isHidden = true
            object4.restartTimers = true
            updateTheSideBar(objects: [object, object1, object2, object4])
        }
        if User.isLoggedIn {
            let doubleTap = UITapGestureRecognizer(target: self, action: #selector(likeThisFlex(_:)))
            doubleTap.numberOfTapsRequired = 2
            cell.addGestureRecognizer(doubleTap)
        }
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: view.frame.size.width, height: view.frame.size.height)
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, insetForSectionAt section: Int) -> UIEdgeInsets {
        return UIEdgeInsets(top: 0.0, left: 0.0, bottom: 0.0, right: 0.0)
    }
}
