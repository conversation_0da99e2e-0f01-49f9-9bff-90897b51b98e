//
//  TextFieldTableViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 8/24/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

class TextFieldTableViewCell: UITableViewCell {
    
    @IBOutlet weak var textField: UITextField!
    @IBOutlet weak var grayBox: UIView!
    @IBOutlet weak var clearTextButton: UIButton!
    @IBOutlet weak var secureTextButton: UIButton!
    @IBOutlet weak var clearTextButtonWidthConstraint: NSLayoutConstraint!
    
    static var cellIdentifier = String(describing: TextFieldTableViewCell.self)
        
    override func awakeFromNib() {
        super.awakeFromNib()
        clearTextButton.isHidden = true
    }
    
    func configureCell(_ placeholder: String, password: Bool = false) {
        textField.tintColor = .gfGreen
        textField.textColor = .gfGreen
        textField.placeholder = placeholder
        grayBox.backgroundColor = .gfDarkBackground
        grayBox.layer.cornerRadius = 4.0
        grayBox.layer.borderColor = UIColor.black.cgColor
        grayBox.layer.borderWidth = 1.0
        if password {
            clearTextButtonWidthConstraint.constant = 0
            if textField.isFirstResponder {
                secureTextButton.isHidden = false
            } else {
                secureTextButton.isHidden = true
            }
        } else {
            clearTextButtonWidthConstraint.constant = 24
            secureTextButton.isHidden = true
        }
    }
    
    @IBAction func didTapClearTextButton() {
        textField.text = ""
    }
    
    func hideOrShowTheClearButton(tag: Int) {
        if textField.isFirstResponder {
            if textField.tag > 9 {
                secureTextButton.isHidden = false
            } else {
                clearTextButton.isHidden = false
            }
        } else {
            if textField.tag > 9 {
                secureTextButton.isHidden = true
                textField.isSecureTextEntry = true
            } else {
                clearTextButton.isHidden = true
            }
        }
    }
    
    @IBAction func hideOrShowTheEyeButton() {
        if secureTextButton.image(for: .normal) == #imageLiteral(resourceName: "eye") {
            secureTextButton.setImage(#imageLiteral(resourceName: "eyeOff"), for: .normal)
            textField.isSecureTextEntry = false
        } else {
            secureTextButton.setImage(#imageLiteral(resourceName: "eye"), for: .normal)
            textField.isSecureTextEntry = true
        }
    }
}
