//
//  GFDefaults.swift
//  GameFlex
//
//  Created by <PERSON> on 7/5/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import Foundation
import UIKit

enum StickersArrayType {
    case recently, frequently
}

class GFDefaults {
    
    static let shared = GFDefaults()
    
    let kAskedCameraPrivacyPermissions               = "io.gameflex.cameraPermission"
    let kAskedLibraryPrivacyPermissions              = "io.gameflex.libraryPermission"
    let kAskedMicrophonePrivacyPermissions           = "io.gameflex.microphonePermission"
    let kAskedPushNotificationsPermissions           = "io.gameflex.pushPermission"
    
    static let kFavRecentStickers               = "io.gameflex.favRecentStickerStrings"
    static let kFavStickerFrequencyDictionary   = "io.gameflex.favStickerFrequencyDictionary"
    static let kLastFontName                    = "io.gameflex.lastFontName"
    static let kFlexCompletedCountKey           = "io.gameflex.completedCount"
    static let kLastVersionPromptedForReviewKey = "io.gameflex.lastVersionPromptedForReview"
    
    static let kChannelTimeStamps               = "io.gameflex.channelTimeStamps_\(User.userId ?? "")"
    
    static let kAccessTokenKey                  = "io.gameflex.accessTokenKey"
    static let kRefreshTokenKey                 = "io.gameflex.refreshTokenKey"
    static let kAccessTokenExpiryKey            = "io.gameflex.accessTokenExpiryKey"
    
    static let kUserId                          = "io.gameflex.userId"
    static let kIsEmailVerified                 = "io.gameflex.isEmailVerified"
    static let kDateOfBirth                     = "io.gameflex.dateOfBirth"
    static let kAuthenticationService           = "io.gameflex.authenticationService"
    static let kSearchOnNumberOfLetters         = "io.gameflex.searchOnNumberOfLetters"
    
    static let kLikedFlexes                     = "io.gameflex.likedFlexesPerUserId"
    static let kLastVSeen                       = "io.gameflex.lastVSeen"
    static let kParentsWithReflexesSeen         = "io.gameflex.parentsWithReflexesSeen"
    static let kFCMToken                        = "io.gameflex.fcmToken"
    static let kOnboardState                    = "io.gameflex.onboardState"

    // MARK: - Channel Ops Details
    
    static var channelTimeStamps: [String: String]? {
        get {
            if let dict = UserDefaults.standard.dictionary(forKey: kChannelTimeStamps) {
                var dic: [String: String] = [:]
                for key in dict.keys {
                    dic[key] = dict[key] as? String
                }
                return dic
            }
            return nil
        }
        set {
            UserDefaults.standard.set(newValue, forKey: kChannelTimeStamps)
        }
    }
    
    static var fcmToken: String? {
        get { return UserDefaults.standard.string(forKey: kFCMToken) }
        set { UserDefaults.standard.setValue(newValue, forKey: kFCMToken) }
    }
    
    static var lastVSeen: Int {
        get {
            return UserDefaults.standard.integer(forKey: kLastVSeen)
        }
        set {
            UserDefaults.standard.setValue(newValue, forKey: kLastVSeen)
        }
    }
    
    static var likedFlexes: [String: [String]] {
        get {
            if let result = UserDefaults.standard.dictionary(forKey: kLikedFlexes) {
                return result as! [String: [String]]
            }
            return [:]
        }
        set {
            if newValue != [:] {
                UserDefaults.standard.setValue(newValue, forKey: kLikedFlexes)
            } else {
                UserDefaults.standard.removeObject(forKey: kLikedFlexes)
            }
        }
    }
    
    static var onboardState: OnboardState {
        get {
            let result = UserDefaults.standard.integer(forKey: kOnboardState)
            return OnboardState(rawValue: result) ?? .none
            
        }
        set {
            UserDefaults.standard.setValue(newValue.rawValue, forKey: kOnboardState)
        }
    }
    
    static var parentsWithReflexesSeen: [String: [String]] {
        get {
            if let result = UserDefaults.standard.dictionary(forKey: kParentsWithReflexesSeen) {
                return result as! [String: [String]]
            }
            return [:]
        }
        set {
            if newValue != [:] {
                UserDefaults.standard.setValue(newValue, forKey: kParentsWithReflexesSeen)
            } else {
                UserDefaults.standard.removeObject(forKey: kParentsWithReflexesSeen)
            }
        }
    }
    
    // MARK: - OAuth2 details
    
    static var pushAccessToken: String? {
        get {
            return UserDefaults.standard.string(forKey: kAccessTokenKey)
        }
        set {
            UserDefaults.standard.set(newValue, forKey: kAccessTokenKey)
        }

    }
    
    static var refreshToken: String? {
        get {
            return UserDefaults.standard.string(forKey: kRefreshTokenKey)
        }
        set {
            UserDefaults.standard.set(newValue, forKey: kRefreshTokenKey)
        }

    }
    
    static var pushAccessTokenExpiryDate: Date? {
        get {
            return Date(timeIntervalSinceReferenceDate: UserDefaults.standard.double(forKey: kAccessTokenExpiryKey))
        }
        set {
            if newValue != nil {
                let dated = newValue?.timeIntervalSinceReferenceDate
                UserDefaults.standard.set(dated, forKey: kAccessTokenExpiryKey)
            } else {
                UserDefaults.standard.removeObject(forKey: kAccessTokenExpiryKey)
            }
        }
    }
    
    
    // MARK: - User details
    
    static var authenticationService: GameFlexAuthenticationService {
        get {
            return GameFlexAuthenticationService(rawValue: UserDefaults.standard.string(forKey: kAuthenticationService) ?? "none") ?? .none
        }
        set {
            UserDefaults.standard.set(newValue.rawValue, forKey: kAuthenticationService)
        }
    }
    
    static var dateOfBirth: Date? {
        get {
            if UserDefaults.standard.double(forKey: kDateOfBirth) == 0.0 {
                return nil
            }
            return Date(timeIntervalSinceReferenceDate: UserDefaults.standard.double(forKey: kDateOfBirth))
        }
        set {
            UserDefaults.standard.set(newValue?.timeIntervalSinceReferenceDate, forKey: kDateOfBirth)
        }
    }
            
    static var isEmailVerified: Bool {
        get {
            return UserDefaults.standard.bool(forKey: kIsEmailVerified)
        }
        set {
            UserDefaults.standard.set(newValue, forKey: kIsEmailVerified)
        }
    }
    
    static var searchOnNumberOfLetters: Int {
        get { return UserDefaults.standard.integer(forKey: kSearchOnNumberOfLetters) }
        set { UserDefaults.standard.set(newValue, forKey: kSearchOnNumberOfLetters) }
    }
    
    static var userId: String {
        get {
            return UserDefaults.standard.string(forKey: kUserId) ?? ""
        }
        set {
            UserDefaults.standard.set(newValue, forKey: kUserId)
        }
    }
    
    static func resetUserDetails() {
        UserDefaults.standard.removeObject(forKey: kIsEmailVerified)
        UserDefaults.standard.removeObject(forKey: kRefreshTokenKey)
        UserDefaults.standard.removeObject(forKey: kAuthenticationService)
    }
    
    // MARK: -
    /// handles all fav tracking services.
    static func addStickerToFavs(name: String) {
        if favStickerFrequencyDictionary[name] == nil {
            favStickerFrequencyDictionary[name] = 1
        } else {
            favStickerFrequencyDictionary[name]! += 1
        }
        favRecentStickerStrings.insert(name, at: 0)
        if favRecentStickerStrings.count > 10 {
            favRecentStickerStrings.removeLast()
        }
    }
    
    static var completedFlexCount: Int {
        get {
            return UserDefaults.standard.integer(forKey: kFlexCompletedCountKey)
        }
        set {
            UserDefaults.standard.set(newValue, forKey: kFlexCompletedCountKey)
        }
    }
    
    /// get only.
    /// Use addStickerToFavs(name:) to add stickers to favs.
    private static var favRecentStickerStrings: [String] {
        get {
            if UserDefaults.standard.stringArray(forKey: kFavRecentStickers) != nil {
                return UserDefaults.standard.stringArray(forKey: kFavRecentStickers)!
            }
            return []
        }
        set {
            if !newValue.isEmpty {
                UserDefaults.standard.set(newValue, forKey: kFavRecentStickers)
            } else {
                UserDefaults.standard.removeObject(forKey: kFavRecentStickers)
            }
        }
    }
    
    /// get only.
    /// Use addStickerToFavs(name:) to add stickers to favs.
    private static var favStickerFrequencyDictionary: [String: Int] {
        get {
            if UserDefaults.standard.dictionary(forKey: kFavStickerFrequencyDictionary) != nil {
                return UserDefaults.standard.dictionary(forKey: kFavStickerFrequencyDictionary) as! [String: Int]
            }
            return [:]
        }
        set {
            if !newValue.isEmpty {
                UserDefaults.standard.set(newValue, forKey: kFavStickerFrequencyDictionary)
            } else {
                UserDefaults.standard.removeObject(forKey: kFavStickerFrequencyDictionary)
            }
        }
    }
    
    /// confirms that no more than 10 recoreds are returned
    static func getStickersArray(_ type: StickersArrayType) -> [String] {
        switch type {
        case .frequently:
            guard favStickerFrequencyDictionary.count > 0 else {
                return []
            }
            let sortedly = favStickerFrequencyDictionary.sorted{ $0 > $1 }
            let sortedByValueDictionary = sortedly.sorted{ $0.1 > $1.1 }
            var arr: [String] = []
            var i = 0
            while i < 10 {
                let input: [String] = sortedByValueDictionary.map{String($0.key) }
                arr.append(input[i])
                i += 1
                if arr.count == sortedByValueDictionary.count {
                    i = 10
                }
            }
            return arr
        case .recently:
            if favRecentStickerStrings.count > 10 {
                favRecentStickerStrings.removeLast()
            }
            return favRecentStickerStrings
        }
    }
    
    var hasAskedCameraPermission: Bool {
        get {
            if UserDefaults.standard.bool(forKey: kAskedCameraPrivacyPermissions) {
                return true
            }
            return false
        }
        set {
            UserDefaults.standard.set(newValue, forKey: kAskedCameraPrivacyPermissions)
        }
    }
    
    var hasAskedLibraryPermission: Bool {
        get {
            if UserDefaults.standard.bool(forKey: kAskedLibraryPrivacyPermissions) {
                return true
            }
            return false
        }
        set {
            UserDefaults.standard.set(newValue, forKey: kAskedLibraryPrivacyPermissions)
        }
    }
    
    var hasAskedMicrophonePermission: Bool {
        get {
            if UserDefaults.standard.bool(forKey: kAskedMicrophonePrivacyPermissions) {
                return true
            }
            return false
        }
        set {
            UserDefaults.standard.set(newValue, forKey: kAskedMicrophonePrivacyPermissions)
        }
    }
    
    var hasAskedPushPermission: Bool {
        get {
            if UserDefaults.standard.bool(forKey: kAskedPushNotificationsPermissions) {
                return true
            }
            return false
        }
        set {
            UserDefaults.standard.set(newValue, forKey: kAskedPushNotificationsPermissions)
        }
    }
            
    static var lastFont: UIFont? {
        get {
            if UserDefaults.standard.string(forKey: kLastFontName) != nil {
                return UIFont(name: UserDefaults.standard.string(forKey: kLastFontName)!, size: 24)
            }
            return nil
        }
        set {
            if newValue != nil {
                UserDefaults.standard.set(newValue?.fontName, forKey: kLastFontName)
            } else {
                UserDefaults.standard.removeObject(forKey: kLastFontName)
            }
        }
    }

    static var lastVersionPrompted: String {
        get {
            if UserDefaults.standard.string(forKey: kLastVersionPromptedForReviewKey) != nil {
                return UserDefaults.standard.string(forKey: kLastVersionPromptedForReviewKey) ?? "1.0"
            }
            return "1.0"
        }
        set {
            UserDefaults.standard.set(newValue, forKey: kLastVersionPromptedForReviewKey)
        }
    }
}
