/* 
  Localizable.strings
  GameFlex

  Created by <PERSON> on 7/5/20.
  Copyright © 2020 GameFlex. All rights reserved.
*/

"next" = "Next";
"flex" = "Flex";
"reflex" = "Reflex";
"done" = "Done";

"channels.off" = "Channels are under development.\nCome back soon.";
"search.placeholder" = "Search users & channels...";
"search.placeholder.channelsOff" = "Search users...";

"trying.navTitle" = "Flex";
"trying.title" = "Trying to Flex?";
"trying.subTitle" = "We need access to a few things first...";
"trying.photosButton" = "Allow access to your photos";
"trying.cameraButton" = "Allow access to your camera";
"trying.microphoneButton" = "Allow access to your microphone";
"trying.pushButton" = "Allow push notifications";

"trying.photosButtonPermission" = "     Allow access to your photos";
"trying.cameraButtonPermission" = "     Allow access to your camera";
"trying.microphoneButtonPermission" = "     Allow access to your microphone";
"trying.pushButtonPermission" = "     Allow push notifications";
"trying.goToSettings" = "     Go to Settings > GameFlex to change.";


"camera.navTitle" = "Flex";
"camera.photo" = "Photo";
"camera.library" = "Library";

"filter" = "Filter";
"edit" = "Edit";
"flare" = "Flare";
"text" = "Text";
"draw" = "Draw";
"invite" = "Update";

/* filters */
"None" = "None";
"Special" = "Special";
"Soupped" = "Soupped";
"Obnoxious" = "Obnoxious";
"Trashed" = "Trashed";

/* edits */
"Brightness" = "Brightness";
"Contrast" = "Contrast";
"Structure" = "Structure";
"Temperature" = "Temperature";
"Something Else" = "Something Else";

// FLARE CATEGORIES
"Flex Fun" = "Flex Fun";
"Emojis" = "Emojis";
"Smack" = "Smack";
"Smellies" = "Smellies";
"Memes" = "Memes";
"Bubbles" = "Bubbles";
"Arrows" = "Arrows";

"favs.note" = "*NOTE: Your recent and frequent flare\n are available here for quick and easy access.";
"others.note" = "goodies coming here soon...";

"textField.placeholder" = "Type something...";

"fontCell.example" = "Ff";
"fontsStart.label" = "Tap the screen above to insert your text.\nOnce entered, you can select your preferred font, style and color.";

"favs.recentlyUsedTitle" = "Recent Flare";
"favs.favsTitle" = "Frequent Flare";

"bugs.placeholder" = "Tap to tell us about your experience...";
"gotit" = "Got it!";
"bugs.alertMessage" = "Thanks for the feedback. We're improving the app every day!";

"email.alreadyAccount" = "Already have an account?\n Log in instead.";
"email.alreadyAccountBolded" = "Log in";
"email.legal" = "By tapping Sign up you acknowledge that you understand the Privacy Policy and agree to the Terms of Service.";
"email.signup" = "Sign up";
"name.missing" = "Name is missing.";
"email.inValidEmail" = "Not a valid email.";
"email.notMatching" = "The confirm info must match.";
"email.inValidPassword" = "Must be minimum 8 characters, only capitals, lower case and numbers.";
"email.validationTitle" = "Your Email is Being Verified";
"email.validationMessage" = "GameFlex has sent you an email with a link (check spam folder). Tap the link in the email and go directly to login.";
"email.loggedIn"    = "You've logged in to your GameFlex account.";

"login.signin" = "Log in";
"login.legal" = "By tapping Log in you acknowledge that you understand the Privacy Policy and agree to the Terms of Service.";
"login.new" = "New to the app?\n Sign up instead.";
"login.newBolded" = "Sign up";
"login.inValidPassword" = "Not a GameFlex password.";

"register.successApple" = "You've successfully registered your GameFlex account with your Apple ID.";
"register.successGoogle" = "You've successfully registered your GameFlex account with your Google ID.";
"register.successEmail" = "You've successfully registered your GameFlex account with your email and password.";
"register.successNone" = "You've successfully registered your GameFlex account.";

"login.successApple" = "Next, choose your Flexter Name and picture.";
"login.successGoogle" = "Next, choose your Flexter Name and picture.";
"login.successEmail" = "Next, choose your Flexter Name and picture.";
"login.successNone" = "Next, choose your Flexter Name and picture.";
"resetPassword" = "Reset Password";

"login.successApple.existingAccount" = "You are logged in.";
"login.successGoogle.existingAccount" = "You are logged in.";
"login.successEmail.existingAccount" = "You are logged in.";
"login.successNone.existingAccount" = "You are logged in.";



"quickIntro.0.title" = "1. Do Something EPIC!";
"quickIntro.0.message" = "Game it up, have fun and let that inner flexter reign havoc on everything and everyone...";
"quickIntro.1.title" = "2. Capture your moment";
"quickIntro.1.message" = "Capture those epic flexter moments of domination however you can...";
"quickIntro.2.title" = "3. Flex your moment";
"quickIntro.2.message" = "Enhance your moment as you see fit... Demand respect and gain your glory as you rise above those that you dominated in battle. Become the ultimate Flexter!";
"quickIntro.skipIntro" = "Skip Intro";

"editProfile.textView.placeholder" = "Why should someone follow you?";
"editProfile.flexterName.placeholder" = "What is your Flexter name?";
"editProfile.name.placeholder" = "What is your full name?";

"editChannel.textView.placeholder" = "Why should someone follow this channel?";
"editChannel.flexterName.placeholder" = "What is your channel's Flexter name?";
"editChannel.name.placeholder" = "What is this channel about?";

"error.houston" = "It seems we have a problem, Houston. Please try again later.";

"profile.flexes" = "Flexes";
"profile.following" = "Following";
"profile.channels" = "Channels";
"profile.follower" = "Followers";

"Finalize.textView.placeholder" = "Caption this flex.";
"hashTags.recentlyUsedTitle" = "Recent";
"hashTags.frequentlyUsedTitle" = "Frequent";
"finalize.textView.titleLabel" = "Caption";

"comment.followButton.follow" = "Follow";
"comment.followButton.following" = "Following";
"comment.followButton.accepted" = "Accepted";
"comment.followButton.invite" = "Accept";

/* Date Strings */
"date.aDayAgo"                                          = "1d";
"date.daysAgo"                                          = "d";
"date.hoursAgo"                                         = "h";
"date.hourAgo"                                          = "1h";
"date.anHourAgo"                                        = "1h";
"date.minutesAgo"                                       = "m";
"date.justNow"                                          = "Just now";
