import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/post_model.dart';
import '../services/posts_service.dart';

enum PostsStatus {
  initial,
  loading,
  loaded,
  error,
  refreshing,
}

class PostsProvider extends ChangeNotifier {
  PostsStatus _status = PostsStatus.initial;
  List<PostModel> _posts = [];
  String? _errorMessage;
  bool _hasMore = true;
  int _currentOffset = 0;
  static const int _pageSize = 20;

  PostsStatus get status => _status;
  List<PostModel> get posts => _posts;
  String? get errorMessage => _errorMessage;
  bool get hasMore => _hasMore;
  bool get isLoading => _status == PostsStatus.loading;
  bool get isRefreshing => _status == PostsStatus.refreshing;

  /// Load initial posts
  Future<void> loadPosts() async {
    if (_status == PostsStatus.loading) return;

    _setStatus(PostsStatus.loading);
    _currentOffset = 0;
    _hasMore = true;

    try {
      final posts = await PostsService.instance.getPosts(
        limit: _pageSize,
        offset: _currentOffset,
      );

      _posts = posts;
      _currentOffset = posts.length;
      _hasMore = posts.length == _pageSize;
      _setStatus(PostsStatus.loaded);
    } catch (e) {
      _setError('Failed to load posts: $e');
    }
  }

  /// Refresh posts (pull to refresh)
  Future<void> refreshPosts() async {
    if (_status == PostsStatus.refreshing) return;

    _setStatus(PostsStatus.refreshing);
    _currentOffset = 0;
    _hasMore = true;

    try {
      final posts = await PostsService.instance.getPosts(
        limit: _pageSize,
        offset: 0,
      );

      _posts = posts;
      _currentOffset = posts.length;
      _hasMore = posts.length == _pageSize;
      _setStatus(PostsStatus.loaded);
    } catch (e) {
      _setError('Failed to refresh posts: $e');
    }
  }

  /// Load more posts (pagination)
  Future<void> loadMorePosts() async {
    if (!_hasMore || _status == PostsStatus.loading) return;

    try {
      final morePosts = await PostsService.instance.getPosts(
        limit: _pageSize,
        offset: _currentOffset,
      );

      if (morePosts.isNotEmpty) {
        _posts.addAll(morePosts);
        _currentOffset += morePosts.length;
        _hasMore = morePosts.length == _pageSize;
        notifyListeners();
      } else {
        _hasMore = false;
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to load more posts: $e');
    }
  }

  /// Like/unlike a post
  Future<void> toggleLike(String postId) async {
    try {
      final isLiked = await PostsService.instance.likePost(postId);
      
      // Update the post in the local list
      final postIndex = _posts.indexWhere((post) => post.id == postId);
      if (postIndex != -1) {
        final post = _posts[postIndex];
        final updatedPost = post.copyWith(
          likeCount: isLiked ? post.likeCount + 1 : post.likeCount - 1,
        );
        _posts[postIndex] = updatedPost;
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to like post: $e');
    }
  }

  /// Add a new post to the beginning of the list
  void addPost(PostModel post) {
    _posts.insert(0, post);
    notifyListeners();
  }

  /// Remove a post from the list
  void removePost(String postId) {
    _posts.removeWhere((post) => post.id == postId);
    notifyListeners();
  }

  /// Update a post in the list
  void updatePost(PostModel updatedPost) {
    final index = _posts.indexWhere((post) => post.id == updatedPost.id);
    if (index != -1) {
      _posts[index] = updatedPost;
      notifyListeners();
    }
  }

  /// Clear error message
  void clearError() {
    _errorMessage = null;
    if (_status == PostsStatus.error) {
      _setStatus(PostsStatus.loaded);
    }
  }

  /// Set status and notify listeners
  void _setStatus(PostsStatus status) {
    _status = status;
    if (status != PostsStatus.error) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  /// Set error and notify listeners
  void _setError(String error) {
    _errorMessage = error;
    _status = PostsStatus.error;
    notifyListeners();
  }

  /// Reset the provider state
  void reset() {
    _status = PostsStatus.initial;
    _posts.clear();
    _errorMessage = null;
    _hasMore = true;
    _currentOffset = 0;
    notifyListeners();
  }
}
