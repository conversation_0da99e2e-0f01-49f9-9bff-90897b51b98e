<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17703"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="iN0-l3-epB" customClass="ChannelTableViewCell" customModule="GameFlex" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="414" height="144"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view alpha="0.40000000000000002" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="WWf-Qy-Fi4">
                    <rect key="frame" x="0.0" y="2" width="414" height="140"/>
                    <color key="backgroundColor" red="0.95686274510000002" green="0.95686274510000002" blue="0.95686274510000002" alpha="0.30356378420000002" colorSpace="calibratedRGB"/>
                </view>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="zaT-LZ-JP9">
                    <rect key="frame" x="8" y="7" width="86" height="132"/>
                    <constraints>
                        <constraint firstAttribute="width" secondItem="zaT-LZ-JP9" secondAttribute="height" multiplier="25:33" constant="-14" id="FOe-lk-JMj"/>
                    </constraints>
                </imageView>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="N2W-9s-b6R">
                    <rect key="frame" x="32.5" y="82" width="40" height="40"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="40" id="bDa-H9-rPe"/>
                        <constraint firstAttribute="width" constant="40" id="xSn-HP-iNv"/>
                    </constraints>
                </imageView>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ldD-mn-NeB">
                    <rect key="frame" x="32.5" y="82" width="40" height="40"/>
                    <connections>
                        <action selector="didTapButton:" destination="iN0-l3-epB" eventType="touchUpInside" id="xKu-Ym-GwJ"/>
                    </connections>
                </button>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="WUE-Gv-h4l">
                    <rect key="frame" x="102" y="2" width="304" height="140"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Ym1-hJ-pUe">
                            <rect key="frame" x="2" y="5" width="44.5" height="20.5"/>
                            <fontDescription key="fontDescription" type="boldSystem" pointSize="17"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="qMB-SJ-46y">
                            <rect key="frame" x="263" y="5" width="33" height="22"/>
                            <color key="backgroundColor" white="0.33333333333333331" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="33" id="6oJ-ca-fTf"/>
                                <constraint firstAttribute="height" constant="22" id="tW3-c6-nHM"/>
                            </constraints>
                            <state key="normal" image="dots18"/>
                            <connections>
                                <action selector="didTapButton:" destination="iN0-l3-epB" eventType="touchUpInside" id="Wek-wG-OQB"/>
                            </connections>
                        </button>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="info10" translatesAutoresizingMaskIntoConstraints="NO" id="3kD-PM-KPI">
                            <rect key="frame" x="3" y="55.5" width="10" height="10"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="10" id="iku-3y-N75"/>
                                <constraint firstAttribute="height" constant="10" id="vsY-y3-EDc"/>
                            </constraints>
                        </imageView>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="members" translatesAutoresizingMaskIntoConstraints="NO" id="oNz-JI-Z3f">
                            <rect key="frame" x="3" y="27.5" width="10" height="10"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="10" id="rcH-Qe-hUx"/>
                                <constraint firstAttribute="width" constant="10" id="wqJ-kc-LoW"/>
                            </constraints>
                        </imageView>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="-- contributors" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9fd-V0-lbt">
                            <rect key="frame" x="16" y="25.5" width="77.5" height="14"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="14" id="hqX-fW-z6X"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" weight="light" pointSize="11"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="-- flexes" lineBreakMode="tailTruncation" numberOfLines="3" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="EAb-xm-VEy">
                            <rect key="frame" x="16" y="117" width="44.5" height="14"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="14" id="epR-Lu-n6V"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="11"/>
                            <color key="textColor" white="0.66666666669999997" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="gf10Gray" translatesAutoresizingMaskIntoConstraints="NO" id="nIY-h8-Anc">
                            <rect key="frame" x="3" y="119" width="10" height="10"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="10" id="0Mp-eu-TZb"/>
                                <constraint firstAttribute="width" constant="10" id="w6d-bC-MZa"/>
                            </constraints>
                        </imageView>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Description goes here." lineBreakMode="tailTruncation" numberOfLines="3" baselineAdjustment="alignBaselines" minimumFontSize="6" translatesAutoresizingMaskIntoConstraints="NO" id="OqD-H1-gH6">
                            <rect key="frame" x="16" y="53.5" width="128" height="14.5"/>
                            <fontDescription key="fontDescription" type="system" pointSize="12"/>
                            <color key="textColor" white="0.66666666666666663" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="hv3-bj-sOj">
                            <rect key="frame" x="227" y="5" width="33" height="22"/>
                            <color key="backgroundColor" white="0.33333333333333331" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="22" id="ULi-rm-kAM"/>
                                <constraint firstAttribute="width" constant="33" id="YlZ-tH-zFX"/>
                            </constraints>
                            <state key="normal" image="favs11"/>
                            <connections>
                                <action selector="didTapButton:" destination="iN0-l3-epB" eventType="touchUpInside" id="u63-v5-mj2"/>
                            </connections>
                        </button>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="-- subscribers" lineBreakMode="tailTruncation" numberOfLines="3" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="cpm-cF-ZN9">
                            <rect key="frame" x="81" y="117" width="75.5" height="14"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="14" id="Ph0-j1-agf"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="11"/>
                            <color key="textColor" white="0.66666666669999997" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="fav10" translatesAutoresizingMaskIntoConstraints="NO" id="SI0-01-Y4N">
                            <rect key="frame" x="68" y="119" width="10" height="10"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="10" id="4Nh-yK-63N"/>
                                <constraint firstAttribute="width" constant="10" id="UWb-cq-loF"/>
                            </constraints>
                        </imageView>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="qMB-SJ-46y" firstAttribute="top" secondItem="Ym1-hJ-pUe" secondAttribute="top" id="2dy-R9-dVJ"/>
                        <constraint firstItem="cpm-cF-ZN9" firstAttribute="leading" secondItem="SI0-01-Y4N" secondAttribute="trailing" constant="3" id="48Y-OY-Q1M"/>
                        <constraint firstItem="Ym1-hJ-pUe" firstAttribute="leading" secondItem="WUE-Gv-h4l" secondAttribute="leading" constant="2" id="8xj-nv-wMD"/>
                        <constraint firstItem="hv3-bj-sOj" firstAttribute="top" secondItem="qMB-SJ-46y" secondAttribute="top" id="Fp8-OI-zWF"/>
                        <constraint firstItem="EAb-xm-VEy" firstAttribute="leading" secondItem="nIY-h8-Anc" secondAttribute="trailing" constant="3" id="IOO-jr-0Qe"/>
                        <constraint firstItem="qMB-SJ-46y" firstAttribute="leading" secondItem="hv3-bj-sOj" secondAttribute="trailing" constant="3" id="KKY-TJ-cXN"/>
                        <constraint firstItem="OqD-H1-gH6" firstAttribute="leading" secondItem="3kD-PM-KPI" secondAttribute="trailing" constant="3" id="NZE-nL-WhU"/>
                        <constraint firstItem="OqD-H1-gH6" firstAttribute="top" secondItem="3kD-PM-KPI" secondAttribute="top" constant="-2" id="Swk-xf-fTB"/>
                        <constraint firstItem="hv3-bj-sOj" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="Ym1-hJ-pUe" secondAttribute="trailing" constant="4" id="TvJ-ok-aPy"/>
                        <constraint firstAttribute="height" constant="140" id="Ujt-mS-Qo2"/>
                        <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="EAb-xm-VEy" secondAttribute="trailing" constant="4" id="Xah-y8-RZr"/>
                        <constraint firstAttribute="trailing" secondItem="qMB-SJ-46y" secondAttribute="trailing" constant="8" id="YXU-dc-M0i"/>
                        <constraint firstItem="Ym1-hJ-pUe" firstAttribute="top" secondItem="WUE-Gv-h4l" secondAttribute="top" constant="5" id="b90-8H-Cvt"/>
                        <constraint firstItem="EAb-xm-VEy" firstAttribute="centerY" secondItem="nIY-h8-Anc" secondAttribute="centerY" id="bme-R0-Bw3"/>
                        <constraint firstItem="9fd-V0-lbt" firstAttribute="centerY" secondItem="oNz-JI-Z3f" secondAttribute="centerY" id="cKZ-1n-re9"/>
                        <constraint firstAttribute="bottom" secondItem="nIY-h8-Anc" secondAttribute="bottom" constant="11" id="gAm-QE-6Vt"/>
                        <constraint firstItem="cpm-cF-ZN9" firstAttribute="centerY" secondItem="SI0-01-Y4N" secondAttribute="centerY" id="m3I-Sg-dXi"/>
                        <constraint firstItem="SI0-01-Y4N" firstAttribute="top" secondItem="nIY-h8-Anc" secondAttribute="top" id="nJf-g6-1BM"/>
                        <constraint firstItem="oNz-JI-Z3f" firstAttribute="leading" secondItem="WUE-Gv-h4l" secondAttribute="leading" constant="3" id="oZu-2S-P1h"/>
                        <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="OqD-H1-gH6" secondAttribute="trailing" constant="4" id="rVX-MZ-Oe7"/>
                        <constraint firstItem="SI0-01-Y4N" firstAttribute="leading" secondItem="EAb-xm-VEy" secondAttribute="trailing" constant="7.5" id="rca-vh-GY8"/>
                        <constraint firstItem="oNz-JI-Z3f" firstAttribute="top" secondItem="Ym1-hJ-pUe" secondAttribute="bottom" constant="2" id="t09-3h-R6Z"/>
                        <constraint firstItem="3kD-PM-KPI" firstAttribute="top" secondItem="oNz-JI-Z3f" secondAttribute="bottom" constant="18" id="ta4-WH-U4S"/>
                        <constraint firstItem="3kD-PM-KPI" firstAttribute="leading" secondItem="WUE-Gv-h4l" secondAttribute="leading" constant="3" id="wcG-aB-mUl"/>
                        <constraint firstItem="9fd-V0-lbt" firstAttribute="leading" secondItem="oNz-JI-Z3f" secondAttribute="trailing" constant="3" id="xyW-tf-4vj"/>
                        <constraint firstItem="nIY-h8-Anc" firstAttribute="leading" secondItem="WUE-Gv-h4l" secondAttribute="leading" constant="3" id="zsJ-SJ-ee0"/>
                    </constraints>
                </view>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="WWf-Qy-Fi4" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="2" id="2OY-p0-igF"/>
                <constraint firstItem="WWf-Qy-Fi4" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" id="3Aw-oH-llx"/>
                <constraint firstItem="ldD-mn-NeB" firstAttribute="leading" secondItem="N2W-9s-b6R" secondAttribute="leading" id="5cf-yC-3PX"/>
                <constraint firstItem="N2W-9s-b6R" firstAttribute="centerX" secondItem="zaT-LZ-JP9" secondAttribute="centerX" constant="1.5" id="CFn-ZY-Gwo"/>
                <constraint firstAttribute="trailing" secondItem="WUE-Gv-h4l" secondAttribute="trailing" constant="8" id="DAo-Ro-qwn"/>
                <constraint firstItem="zaT-LZ-JP9" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" priority="999" constant="7" id="G9V-OQ-ubw"/>
                <constraint firstItem="WUE-Gv-h4l" firstAttribute="leading" secondItem="zaT-LZ-JP9" secondAttribute="trailing" constant="8" id="KJU-l0-yJp"/>
                <constraint firstItem="zaT-LZ-JP9" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="8" id="XwB-kP-mY2"/>
                <constraint firstItem="ldD-mn-NeB" firstAttribute="top" secondItem="N2W-9s-b6R" secondAttribute="top" id="Yvm-Vu-4p0"/>
                <constraint firstAttribute="bottom" secondItem="zaT-LZ-JP9" secondAttribute="bottom" priority="999" constant="5" id="bOp-Xw-ahP"/>
                <constraint firstAttribute="trailing" secondItem="WWf-Qy-Fi4" secondAttribute="trailing" id="cpm-hd-JXj"/>
                <constraint firstItem="ldD-mn-NeB" firstAttribute="bottom" secondItem="N2W-9s-b6R" secondAttribute="bottom" id="f6n-M4-iei"/>
                <constraint firstItem="N2W-9s-b6R" firstAttribute="centerY" secondItem="zaT-LZ-JP9" secondAttribute="centerY" constant="29" id="lLy-Yy-1d6"/>
                <constraint firstAttribute="bottom" secondItem="WWf-Qy-Fi4" secondAttribute="bottom" constant="2" id="oEq-xX-Kyl"/>
                <constraint firstItem="ldD-mn-NeB" firstAttribute="trailing" secondItem="N2W-9s-b6R" secondAttribute="trailing" id="vap-em-KIY"/>
                <constraint firstItem="WUE-Gv-h4l" firstAttribute="centerY" secondItem="iN0-l3-epB" secondAttribute="centerY" id="yTZ-L4-TLI"/>
            </constraints>
            <nil key="simulatedTopBarMetrics"/>
            <nil key="simulatedBottomBarMetrics"/>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="backView" destination="WWf-Qy-Fi4" id="zMs-9j-GTx"/>
                <outlet property="favButton" destination="hv3-bj-sOj" id="FSj-rI-gZr"/>
                <outlet property="favButtonWidthConstraint" destination="YlZ-tH-zFX" id="9UJ-sB-PNq"/>
                <outlet property="flexImageView" destination="zaT-LZ-JP9" id="LEk-rr-4mr"/>
                <outlet property="memberCountLabel" destination="9fd-V0-lbt" id="lZd-ZG-rGq"/>
                <outlet property="moreButton" destination="qMB-SJ-46y" id="p2x-ee-vcH"/>
                <outlet property="moreButtonWidthConstraint" destination="6oJ-ca-fTf" id="bhl-LM-2c4"/>
                <outlet property="newButton" destination="ldD-mn-NeB" id="owg-bk-9KU"/>
                <outlet property="profileImageView" destination="N2W-9s-b6R" id="ARP-iE-huB"/>
                <outlet property="statsLabel" destination="EAb-xm-VEy" id="fTU-ov-luv"/>
                <outlet property="subTitleLabel" destination="OqD-H1-gH6" id="pD3-OM-Wnl"/>
                <outlet property="subsLabel" destination="cpm-cF-ZN9" id="8dG-RX-Utj"/>
                <outlet property="titleLabel" destination="Ym1-hJ-pUe" id="JMl-7c-vAL"/>
            </connections>
            <point key="canvasLocation" x="31.884057971014496" y="-56.25"/>
        </view>
    </objects>
    <resources>
        <image name="dots18" width="18" height="18"/>
        <image name="fav10" width="7" height="9"/>
        <image name="favs11" width="11" height="14"/>
        <image name="gf10Gray" width="10" height="10"/>
        <image name="info10" width="10" height="10"/>
        <image name="members" width="10" height="9"/>
    </resources>
</document>
