//
//  SearchResultTableViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 10/9/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

class SearchResultTableViewCell: UITableViewCell {
    
    @IBOutlet weak var profileImage: UIImageView!
    @IBOutlet weak var profileSuperView: UIView!
    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var subTitleLabel: UILabel!
    @IBOutlet weak var followButton: UIButton!
    @IBOutlet weak var collectionView: UICollectionView!
    @IBOutlet weak var titleLabelTrailingConstraint: NSLayoutConstraint!
    
    weak var delegate: HomeDelegate?

    var flexArray: [Flex] = []
    var channelId: String?
    var resultType: SearchResultType = .user

    static var cellIdentifier = String(describing: SearchResultTableViewCell.self)
    
    override func awakeFromNib() {
        super.awakeFromNib()
        profileImage.layer.cornerRadius = profileImage.frame.size.height/2
        subTitleLabel.text = "Flexter"
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(UINib(nibName: FlexCollectionViewCell.cellIdentifier, bundle: nil), forCellWithReuseIdentifier: FlexCollectionViewCell.cellIdentifier)
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        profileImage.image = nil
        titleLabel.text = ""
        collectionView.reloadData()
    }
    
    func configureCell(_ channelId: String, _ type: SearchResultType) {
        self.channelId = channelId
        self.resultType = type
        refreshChannel(top: true)
    }
    
    // MARK: - Action
    
    @IBAction func didTapButton(_ sender: UIButton) {
        delegate?.didTapToFollow(sender, self)
    }
    
    // MARK: - Network Services
    
    @objc func refreshChannel(top: Bool) {
        if let channelId = channelId, resultType == .channel {
            GFNetworkServices.getChannelFlexes(channelId: channelId, top: top, { (success, result, error) in
                DispatchQueue.main.async {
                    Utilities.hideSpinner()
                }
                if !result.isEmpty {
                    if top {
                        self.flexArray = []
                    }
                    self.flexArray.append(contentsOf: result)
                    self.flexArray = Array(Set(self.flexArray))
                    self.flexArray.sort(by: { $0.createAtDate?.timeIntervalSinceReferenceDate ?? 0.0 > $1.createAtDate?.timeIntervalSinceReferenceDate ?? 0.0 })
                    if let channelId = self.flexArray.last?.channel?.channelId, let _ = GFDefaults.channelTimeStamps, let time = self.flexArray.last?.createAt {
                        GFDefaults.channelTimeStamps?[channelId] = time
                    } else if let channelId = self.flexArray.last?.channel?.channelId, let time = self.flexArray.last?.createAt {
                        GFDefaults.channelTimeStamps = [channelId: time]
                    }
                    DispatchQueue.main.async {
                        self.collectionView.reloadData()
                    }
                }
            })
        } else if let channelId = channelId, resultType == .user {
            GFNetworkServices.getUserFlexes(channelId: channelId, top: top, { (success, result, error) in
                DispatchQueue.main.async {
                    Utilities.hideSpinner()
                }
                if !result.isEmpty {
                    if top {
                        self.flexArray = []
                    }
                    self.flexArray.append(contentsOf: result)
                    self.flexArray = Array(Set(self.flexArray))
                    self.flexArray.sort(by: { $0.createAtDate?.timeIntervalSinceReferenceDate ?? 0.0 > $1.createAtDate?.timeIntervalSinceReferenceDate ?? 0.0 })
                    if let channelId = self.flexArray.last?.channel?.channelId, let _ = GFDefaults.channelTimeStamps, let time = self.flexArray.last?.createAt {
                        GFDefaults.channelTimeStamps?[channelId] = time
                    } else if let channelId = self.flexArray.last?.channel?.channelId, let time = self.flexArray.last?.createAt {
                        GFDefaults.channelTimeStamps = [channelId: time]
                    }
                    DispatchQueue.main.async {
                        self.collectionView.reloadData()
                    }
                }
            })

        }
    }
}

extension SearchResultTableViewCell: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        if flexArray.isEmpty {
            return 1
        }
        return flexArray.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: FlexCollectionViewCell.cellIdentifier, for: indexPath) as? FlexCollectionViewCell else { return FlexCollectionViewCell() }
        if flexArray.isEmpty {
            cell.flexImageView.image = FlexManager.placeholders[Int.random(in: 0..<FlexManager.placeholders.count)]
            cell.flexImageView.alpha = 0.6
            let label = UILabel(frame: CGRect(x: 0, y: 0, width: 100, height: 130))
            label.text = ".. needs content.. "
            label.textColor = .gfYellow_F2DE76
            label.numberOfLines = 0
            label.tag = 972231
            label.layer.shadowColor = UIColor.black.cgColor
            label.layer.shadowOffset = CGSize(width: 1.5, height: 1.5)
            label.font = .systemFont(ofSize: 19, weight: .heavy)
            label.textAlignment = .center
            cell.addSubview(label)
            return cell
        }
        cell.subviews.filter({$0.tag == 972231 }).forEach({ $0.removeFromSuperview() })
        cell.configureCell(flexArray[indexPath.row])
        cell.flexImageView.alpha = 1.0
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: 100, height: 132)
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat {
        return 4.0
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, insetForSectionAt section: Int) -> UIEdgeInsets {
        return UIEdgeInsets(top: 0.0, left: 0.0, bottom: 0.0, right: 0.0)
    }
}
