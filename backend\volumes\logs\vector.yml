data_dir: /vector-data-dir

api:
  enabled: true
  address: 0.0.0.0:9001

sources:
  docker_host:
    type: docker_logs
    docker_host: unix:///var/run/docker.sock
    include_containers:
      - supabase-auth
      - supabase-rest
      - supabase-realtime
      - supabase-storage
      - supabase-kong
      - supabase-db
      - supabase-studio
      - supabase-imgproxy
      - supabase-meta
      - supabase-functions
      - supabase-analytics

transforms:
  docker_logs_json:
    type: remap
    inputs:
      - docker_host
    source: |
      if exists(.message) {
        .message = parse_json(.message) ?? .message
      }

sinks:
  # Send logs to Logflare
  logflare_logs:
    type: http
    inputs:
      - docker_logs_json
    uri: http://analytics:4000/api/logs
    method: post
    headers:
      Content-Type: application/json
      X-API-KEY: "${LOGFLARE_API_KEY:-}"
    encoding:
      codec: json
    healthcheck:
      enabled: true

  # Also output to console for debugging
  console:
    type: console
    inputs:
      - docker_logs_json
    encoding:
      codec: json
