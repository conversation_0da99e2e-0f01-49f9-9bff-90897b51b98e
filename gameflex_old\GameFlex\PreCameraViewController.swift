//
//  PreCameraViewController.swift
//  GameFlex
//
//  Created by <PERSON> on 7/6/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import FirebaseAuth
import FirebaseInstanceID
import FirebaseMessaging

class PreCameraViewController: GFViewController {
                
    override func viewDidLoad() {
        super.viewDidLoad()
        title = ""
//        resetButton.setTitleColor(.gfGreen, for: .normal)
//        bugButton.setTitleColor(.gfGreen, for: .normal)
//
//        versionLabel.text = "GameFlex v\(User.app ?? "NA")"
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        // Make the navigation bar background clear
        navigationController?.navigationBar.setBackgroundImage(UIImage(), for: .default)
        navigationController?.navigationBar.shadowImage = UIImage()
        navigationController?.navigationBar.isTranslucent = true
        navigationController?.navigationBar.tintColor = .white
        // Make the navigation bar background clear
        navigationController?.navigationBar.setBackgroundImage(UIImage(), for: .default)
        navigationController?.navigationBar.shadowImage = UIImage()
        navigationController?.navigationBar.isTranslucent = true
        guard CameraViewModel.state != .restart else {
            let cvc = CameraViewController.storyboardInstance()
            CameraViewModel.state = .buttons
            navigationController?.pushViewController(cvc, animated: true)
            return
        }
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        Utilities.hideSpinner()
        if GFDefaults.shared.hasAskedCameraPermission && GFDefaults.shared.hasAskedLibraryPermission /*&& GFDefaults.shared.hasAskedMicrophonePermission*/ && GFDefaults.shared.hasAskedPushPermission {
            let cvc = CameraViewController.storyboardInstance()
            CameraViewModel.state = .buttons
            navigationController?.pushViewController(cvc, animated: true)
            return
        } else {
            // TODO: check permissions in Settings
        }
    }
    
    @IBAction func didTapButton(_ sender: Any) {
//        switch sender as! UIButton {
//        case createContentButton:
            let ttfvc = TryingToFlexViewController.storyboardInstance()
            ttfvc.delegate = self
            present(ttfvc, animated: true)
//        case bugButton:
            let bvc = BugViewController.storyboardInstance()
            navigationController?.pushViewController(bvc, animated: true)
//        case otherButton:
            let vc = Temp2ViewController.storyboardInstance()
            navigationController?.pushViewController(vc, animated: true)
//        default:
            UserDefaults.standard.removeObject(forKey: GFDefaults.kFavStickerFrequencyDictionary)
            UserDefaults.standard.removeObject(forKey: GFDefaults.kFavRecentStickers)
//        }
    }
    
    private func getTheToken(_ closure: @escaping (_ result: String) -> Void) {
        InstanceID.instanceID().instanceID { (result, error) in
          if let error = error {
            print("Error fetching remote instance ID: \(error)")
          } else if let result = result {
            print("Remote InstanceID token: \(result.token)")
            closure(result.token)
          }
        }
    }
    
}

extension PreCameraViewController: PermissionsDelegate {
    
    func didFinishWithPermissions() {
    }
}
