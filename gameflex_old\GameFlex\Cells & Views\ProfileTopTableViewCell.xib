<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17703"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="iN0-l3-epB" customClass="ProfileTopTableViewCell" customModule="GameFlex" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="436" height="198"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1lu-Zl-9H8">
                    <rect key="frame" x="136" y="92" width="40" height="1"/>
                    <color key="backgroundColor" white="0.33333333333333331" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="1" id="K73-sM-KNL"/>
                        <constraint firstAttribute="width" constant="40" id="oxK-E3-dkX"/>
                    </constraints>
                </view>
                <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="CgM-3W-UBv">
                    <rect key="frame" x="16" y="30" width="100" height="100"/>
                    <subviews>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="Layer 6" translatesAutoresizingMaskIntoConstraints="NO" id="ZS4-v6-CFe">
                            <rect key="frame" x="0.0" y="0.0" width="100" height="100"/>
                        </imageView>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="100" id="HUo-86-Ciy"/>
                        <constraint firstAttribute="trailing" secondItem="ZS4-v6-CFe" secondAttribute="trailing" id="PLT-U7-noH"/>
                        <constraint firstAttribute="bottom" secondItem="ZS4-v6-CFe" secondAttribute="bottom" id="X6U-SJ-KpW"/>
                        <constraint firstItem="ZS4-v6-CFe" firstAttribute="top" secondItem="CgM-3W-UBv" secondAttribute="top" id="itr-21-UcB"/>
                        <constraint firstItem="ZS4-v6-CFe" firstAttribute="leading" secondItem="CgM-3W-UBv" secondAttribute="leading" id="tHJ-ga-ZHN"/>
                        <constraint firstAttribute="height" constant="100" id="wbh-wg-qw7"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="lBU-WE-hDo">
                    <rect key="frame" x="127" y="70" width="299" height="17"/>
                    <subviews>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="profileFlex" translatesAutoresizingMaskIntoConstraints="NO" id="PwP-I6-iEl">
                            <rect key="frame" x="8" y="3" width="11" height="11"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="11" id="eJr-Iz-tWM"/>
                            </constraints>
                        </imageView>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="4" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="lll-8t-Cy3">
                            <rect key="frame" x="22" y="0.0" width="9" height="17"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="17" id="SIF-01-P3t"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="profileUp" translatesAutoresizingMaskIntoConstraints="NO" id="4w5-N1-IFb">
                            <rect key="frame" x="47" y="3" width="11" height="11"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="11" id="JNr-Zt-ua4"/>
                            </constraints>
                        </imageView>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="3" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rWi-5V-xtL">
                            <rect key="frame" x="61" y="0.0" width="9" height="17"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="17" id="erf-sz-are"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="profileLike" translatesAutoresizingMaskIntoConstraints="NO" id="Ag0-qR-a7m">
                            <rect key="frame" x="86" y="3" width="11" height="11"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="11" id="Ksf-Ei-I0x"/>
                            </constraints>
                        </imageView>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="210" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4fJ-FS-lyN">
                            <rect key="frame" x="100" y="0.0" width="24" height="17"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="17" id="26t-Ye-A11"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="profileReflex" translatesAutoresizingMaskIntoConstraints="NO" id="IzV-KX-s41">
                            <rect key="frame" x="140" y="3" width="13" height="11"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="11" id="vqv-hc-Xyr"/>
                            </constraints>
                        </imageView>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="98" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="XyN-uL-egC">
                            <rect key="frame" x="156" y="0.0" width="18" height="17"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="17" id="yRn-3O-rNE"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="lll-8t-Cy3" firstAttribute="centerY" secondItem="lBU-WE-hDo" secondAttribute="centerY" id="00H-3Z-8yq"/>
                        <constraint firstItem="PwP-I6-iEl" firstAttribute="centerY" secondItem="lBU-WE-hDo" secondAttribute="centerY" id="0Xi-l4-DnE"/>
                        <constraint firstItem="XyN-uL-egC" firstAttribute="centerY" secondItem="lBU-WE-hDo" secondAttribute="centerY" id="Dzm-KE-Wuy"/>
                        <constraint firstItem="Ag0-qR-a7m" firstAttribute="leading" secondItem="rWi-5V-xtL" secondAttribute="trailing" constant="16" id="FNG-zL-NSM"/>
                        <constraint firstItem="rWi-5V-xtL" firstAttribute="centerY" secondItem="lBU-WE-hDo" secondAttribute="centerY" id="Kb2-VP-oas"/>
                        <constraint firstItem="4w5-N1-IFb" firstAttribute="centerY" secondItem="lBU-WE-hDo" secondAttribute="centerY" id="Nmz-TP-PvA"/>
                        <constraint firstItem="lll-8t-Cy3" firstAttribute="leading" secondItem="PwP-I6-iEl" secondAttribute="trailing" constant="3" id="Np1-9f-QZi"/>
                        <constraint firstAttribute="height" constant="17" id="ZLx-73-jqb"/>
                        <constraint firstItem="IzV-KX-s41" firstAttribute="centerY" secondItem="lBU-WE-hDo" secondAttribute="centerY" id="bDn-Ra-bgi"/>
                        <constraint firstItem="rWi-5V-xtL" firstAttribute="leading" secondItem="4w5-N1-IFb" secondAttribute="trailing" constant="3" id="hDs-9T-IBH"/>
                        <constraint firstItem="IzV-KX-s41" firstAttribute="leading" secondItem="4fJ-FS-lyN" secondAttribute="trailing" constant="16" id="iqH-AQ-4ba"/>
                        <constraint firstItem="4fJ-FS-lyN" firstAttribute="centerY" secondItem="lBU-WE-hDo" secondAttribute="centerY" id="jO2-DV-Wud"/>
                        <constraint firstItem="4w5-N1-IFb" firstAttribute="leading" secondItem="lll-8t-Cy3" secondAttribute="trailing" constant="16" id="jmC-Y6-3Bb"/>
                        <constraint firstItem="XyN-uL-egC" firstAttribute="leading" secondItem="IzV-KX-s41" secondAttribute="trailing" constant="3" id="kLN-So-o7o"/>
                        <constraint firstItem="4fJ-FS-lyN" firstAttribute="leading" secondItem="Ag0-qR-a7m" secondAttribute="trailing" constant="3" id="pRH-MQ-Pnd"/>
                        <constraint firstItem="Ag0-qR-a7m" firstAttribute="centerY" secondItem="lBU-WE-hDo" secondAttribute="centerY" id="vHb-ev-NoH"/>
                        <constraint firstItem="PwP-I6-iEl" firstAttribute="leading" secondItem="lBU-WE-hDo" secondAttribute="leading" constant="8" id="vgU-Dq-1aC"/>
                    </constraints>
                </view>
                <label opaque="NO" userInteractionEnabled="NO" alpha="0.0" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="#RecentTag1 #RecentTag2 #RecentTag3" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumScaleFactor="0.5" translatesAutoresizingMaskIntoConstraints="NO" id="8a0-4l-vy8">
                    <rect key="frame" x="116" y="144" width="304" height="21"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <nil key="highlightedColor"/>
                </label>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" insetsLayoutMarginsFromSafeArea="NO" text="Flexing since September 2020" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" id="Dvd-lH-bTp">
                    <rect key="frame" x="136" y="45" width="237" height="22"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                    <color key="textColor" red="0.95686274510000002" green="0.95686274510000002" blue="0.95686274510000002" alpha="0.30356378420000002" colorSpace="calibratedRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ewi-jo-12T">
                    <rect key="frame" x="25.5" y="167" width="81" height="17"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="FlexRank" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumScaleFactor="0.5" translatesAutoresizingMaskIntoConstraints="NO" id="Slw-ld-aiL">
                            <rect key="frame" x="19" y="0.0" width="59" height="17"/>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="17" id="tRO-rG-SJv"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <color key="textColor" red="0.95686274510000002" green="0.95686274510000002" blue="0.95686274510000002" alpha="0.69964683220000001" colorSpace="calibratedRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" adjustsImageWhenHighlighted="NO" adjustsImageWhenDisabled="NO" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="lWh-mq-Fv2">
                            <rect key="frame" x="0.0" y="1" width="16" height="15"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="15" id="4jM-nO-8NS"/>
                                <constraint firstAttribute="width" constant="16" id="P0Q-f6-WpP"/>
                            </constraints>
                            <state key="normal" image="starGray"/>
                        </button>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="Slw-ld-aiL" firstAttribute="centerY" secondItem="lWh-mq-Fv2" secondAttribute="centerY" id="0yK-bo-JbZ"/>
                        <constraint firstAttribute="trailing" secondItem="Slw-ld-aiL" secondAttribute="trailing" constant="3" id="43Y-AP-uc1"/>
                        <constraint firstItem="lWh-mq-Fv2" firstAttribute="centerY" secondItem="ewi-jo-12T" secondAttribute="centerY" id="5yh-FO-1zs"/>
                        <constraint firstItem="Slw-ld-aiL" firstAttribute="leading" secondItem="lWh-mq-Fv2" secondAttribute="trailing" constant="3" id="Iq6-Pb-Ns5"/>
                        <constraint firstItem="Slw-ld-aiL" firstAttribute="leading" secondItem="lWh-mq-Fv2" secondAttribute="trailing" constant="3" id="V2J-13-0A6"/>
                        <constraint firstItem="lWh-mq-Fv2" firstAttribute="leading" secondItem="ewi-jo-12T" secondAttribute="leading" id="xSl-dS-TnL"/>
                        <constraint firstAttribute="height" constant="17" id="zUZ-Lt-2lO"/>
                    </constraints>
                </view>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="oq0-Vb-F1g">
                    <rect key="frame" x="366" y="8" width="54" height="28"/>
                    <color key="backgroundColor" red="0.95686274510000002" green="0.95686274510000002" blue="0.95686274510000002" alpha="0.30356378420000002" colorSpace="calibratedRGB"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="54" id="rif-2G-L9Y"/>
                        <constraint firstAttribute="height" constant="28" id="zNF-iq-gt8"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="13"/>
                    <state key="normal" title="EDIT">
                        <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    </state>
                    <connections>
                        <action selector="didTapButton:" destination="iN0-l3-epB" eventType="touchUpInside" id="GDZ-Lb-pfW"/>
                    </connections>
                </button>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="GameFlexGuy234" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="VB3-85-RfC">
                    <rect key="frame" x="136" y="26" width="137" height="21"/>
                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" editable="NO" selectable="NO" translatesAutoresizingMaskIntoConstraints="NO" id="oad-G8-ges">
                    <rect key="frame" x="132" y="89" width="291" height="50"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="50" id="0vJ-yr-4bb"/>
                    </constraints>
                    <string key="text">Lorem ipsum dolor sit er elit lamet, consectetaur cillium adipisicing pecu, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. Nam liber te conscient to factor tum poen legum odioque civiuda.</string>
                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                    <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                </textView>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="First Last name" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumScaleFactor="0.40000000000000002" translatesAutoresizingMaskIntoConstraints="NO" id="N70-0L-STV">
                    <rect key="frame" x="25.5" y="146" width="81" height="17"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="17" id="I5a-Oa-bQb"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="11"/>
                    <color key="textColor" red="0.95686274510000002" green="0.95686274510000002" blue="0.95686274510000002" alpha="0.30356378420000002" colorSpace="calibratedRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="MIo-Qi-A4o">
                    <rect key="frame" x="379" y="21.5" width="41" height="30"/>
                    <state key="normal" title="follow"/>
                    <connections>
                        <action selector="didTapButton:" destination="iN0-l3-epB" eventType="touchUpInside" id="fmd-cn-hFU"/>
                    </connections>
                </button>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="8a0-4l-vy8" firstAttribute="top" secondItem="oad-G8-ges" secondAttribute="bottom" constant="5" id="0oJ-xc-s49"/>
                <constraint firstItem="lBU-WE-hDo" firstAttribute="top" secondItem="Dvd-lH-bTp" secondAttribute="bottom" constant="3" id="4oK-Rf-Gkn"/>
                <constraint firstItem="oad-G8-ges" firstAttribute="top" secondItem="lBU-WE-hDo" secondAttribute="bottom" constant="2" id="5Cf-RQ-wDd"/>
                <constraint firstItem="N70-0L-STV" firstAttribute="top" secondItem="CgM-3W-UBv" secondAttribute="bottom" constant="16" id="5Rp-C2-TnI"/>
                <constraint firstItem="Slw-ld-aiL" firstAttribute="centerY" secondItem="lWh-mq-Fv2" secondAttribute="centerY" id="6Ql-WW-9EC"/>
                <constraint firstItem="VB3-85-RfC" firstAttribute="leading" secondItem="CgM-3W-UBv" secondAttribute="trailing" constant="20" id="9xW-aa-GcL"/>
                <constraint firstItem="CgM-3W-UBv" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="30" id="AK4-33-loJ"/>
                <constraint firstItem="MIo-Qi-A4o" firstAttribute="centerY" secondItem="VB3-85-RfC" secondAttribute="centerY" id="AT3-sS-xkT"/>
                <constraint firstItem="ewi-jo-12T" firstAttribute="top" secondItem="N70-0L-STV" secondAttribute="bottom" constant="4" id="As6-w8-c1z"/>
                <constraint firstItem="ewi-jo-12T" firstAttribute="centerX" secondItem="CgM-3W-UBv" secondAttribute="centerX" id="BwY-RL-UL5"/>
                <constraint firstItem="oq0-Vb-F1g" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="8" id="Fa1-nH-ViJ"/>
                <constraint firstAttribute="trailing" secondItem="oq0-Vb-F1g" secondAttribute="trailing" constant="16" id="Jru-Qo-0oa"/>
                <constraint firstItem="1lu-Zl-9H8" firstAttribute="top" secondItem="lBU-WE-hDo" secondAttribute="bottom" constant="5" id="RfA-Fd-sW1"/>
                <constraint firstItem="Slw-ld-aiL" firstAttribute="leading" secondItem="lWh-mq-Fv2" secondAttribute="trailing" constant="3" id="Ta7-ZF-9Hn"/>
                <constraint firstItem="VB3-85-RfC" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="26" id="W44-Kj-c0b"/>
                <constraint firstItem="1lu-Zl-9H8" firstAttribute="leading" secondItem="CgM-3W-UBv" secondAttribute="trailing" constant="20" id="Y4T-iy-C9n"/>
                <constraint firstItem="lBU-WE-hDo" firstAttribute="leading" secondItem="CgM-3W-UBv" secondAttribute="trailing" constant="11" id="ZGJ-Sy-2fI"/>
                <constraint firstItem="ewi-jo-12T" firstAttribute="centerX" secondItem="CgM-3W-UBv" secondAttribute="centerX" id="bfi-xu-UnK"/>
                <constraint firstItem="oad-G8-ges" firstAttribute="leading" secondItem="CgM-3W-UBv" secondAttribute="trailing" constant="16" id="fYs-0g-LG1"/>
                <constraint firstAttribute="trailing" secondItem="oad-G8-ges" secondAttribute="trailing" constant="13" id="hZP-jL-C3T"/>
                <constraint firstItem="N70-0L-STV" firstAttribute="centerX" secondItem="CgM-3W-UBv" secondAttribute="centerX" id="i1y-41-hyt"/>
                <constraint firstItem="8a0-4l-vy8" firstAttribute="leading" secondItem="ewi-jo-12T" secondAttribute="trailing" constant="9.5" id="iOI-Cn-Hzg"/>
                <constraint firstAttribute="trailing" secondItem="MIo-Qi-A4o" secondAttribute="trailing" constant="16" id="nlm-8s-TCc"/>
                <constraint firstAttribute="trailing" secondItem="lBU-WE-hDo" secondAttribute="trailing" constant="10" id="okQ-4z-U6G"/>
                <constraint firstAttribute="trailing" secondItem="8a0-4l-vy8" secondAttribute="trailing" constant="16" id="tIN-EW-0Lf"/>
                <constraint firstItem="CgM-3W-UBv" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="16" id="uCa-SO-GcI"/>
                <constraint firstItem="Dvd-lH-bTp" firstAttribute="leading" secondItem="VB3-85-RfC" secondAttribute="leading" id="vUT-XY-iX2"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="channelsParticipatingInLabel" destination="lll-8t-Cy3" id="voN-VI-uwg"/>
                <outlet property="editButton" destination="oq0-Vb-F1g" id="ZSj-oz-EdL"/>
                <outlet property="flexingSinceLabel" destination="Dvd-lH-bTp" id="XdK-Cr-Jqr"/>
                <outlet property="flexterImageView" destination="ZS4-v6-CFe" id="nJa-6C-Abn"/>
                <outlet property="flexterNameLabel" destination="VB3-85-RfC" id="ZxJ-ab-Ssf"/>
                <outlet property="followButton" destination="MIo-Qi-A4o" id="lrN-Lm-hYW"/>
                <outlet property="imageSuperView" destination="CgM-3W-UBv" id="03s-pU-bHw"/>
                <outlet property="lifeTimeLikesLabel" destination="4fJ-FS-lyN" id="Vud-oW-kIX"/>
                <outlet property="lifeTimeReflexesLabel" destination="XyN-uL-egC" id="hKa-lv-ZzJ"/>
                <outlet property="nameLabel" destination="N70-0L-STV" id="LG8-ZE-H6g"/>
                <outlet property="personalSummaryTextView" destination="oad-G8-ges" id="hkz-Pd-3mz"/>
                <outlet property="rankLabel" destination="Slw-ld-aiL" id="kN0-i0-INr"/>
                <outlet property="recentFlexesCountLabel" destination="rWi-5V-xtL" id="S7p-fd-9pP"/>
                <outlet property="recentHashTagsLabel" destination="8a0-4l-vy8" id="0Hl-Dh-N1b"/>
                <outlet property="starButton" destination="lWh-mq-Fv2" id="Cin-L8-kAs"/>
            </connections>
            <point key="canvasLocation" x="639.13043478260875" y="10.044642857142856"/>
        </view>
    </objects>
    <resources>
        <image name="Layer 6" width="269" height="309"/>
        <image name="profileFlex" width="11" height="11"/>
        <image name="profileLike" width="11" height="11"/>
        <image name="profileReflex" width="13" height="11"/>
        <image name="profileUp" width="11" height="11"/>
        <image name="starGray" width="16" height="15"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
