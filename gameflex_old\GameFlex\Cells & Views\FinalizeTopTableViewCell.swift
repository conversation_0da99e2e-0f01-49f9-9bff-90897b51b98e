//
//  FinalizeTopTableViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 10/17/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

class FinalizeTopTableViewCell: UITableViewCell {
    
    @IBOutlet weak var flexImageView: UIImageView!
    @IBOutlet weak var textView: UITextView!
    @IBOutlet weak var characterCountLabel: UILabel!
    @IBOutlet weak var titleLabel: UILabel!
        
    static var cellIdentifier = String(describing: FinalizeTopTableViewCell.self)
    
    override func awakeFromNib() {
        super.awakeFromNib()
        textView.keyboardAppearance = .dark
        textView.keyboardType = .twitter
        textView.addDoneButtonToKeyboard()
        characterCountLabel.text = "0"
        textView.delegate = self
        titleLabel.text = "finalize.textView.titleLabel".localized
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
    }
}

extension FinalizeTopTableViewCell: UITextViewDelegate {
    
    func textViewDidBeginEditing(_ textView: UITextView) {
        if textView.text == "Finalize.textView.placeholder".localized {
            textView.text = ""
            textView.textColor = .white
        }
    }
    
    func textViewDidEndEditing(_ textView: UITextView) {
        if textView.text == "" {
            textView.text = "Finalize.textView.placeholder".localized
            textView.textColor = .darkGray
        }
    }
    
    func textView(_ textView: UITextView, shouldChangeTextIn range: NSRange, replacementText text: String) -> Bool {
        guard text != "\n" else {
            return false
        }
        let result = (textView.text as NSString?)?.replacingCharacters(in: range, with: text) ?? text
        characterCountLabel.text = "\(result.count)"
        FinalizeViewModel.captionText = result

        return true
    }
}
