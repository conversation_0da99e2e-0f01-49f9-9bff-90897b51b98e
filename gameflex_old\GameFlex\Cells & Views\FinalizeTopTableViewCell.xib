<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17156" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17125"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="iN0-l3-epB" customClass="FinalizeTopTableViewCell" customModule="GameFlex" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="414" height="104"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="svW-gs-BFh">
                    <rect key="frame" x="68" y="21" width="330" height="62"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="62" id="vcd-Yw-1bv"/>
                    </constraints>
                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                    <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                </textView>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gta-Ho-dSe">
                    <rect key="frame" x="365" y="2" width="33" height="16"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="16" id="T3m-69-lpr"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="13"/>
                    <color key="textColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <nil key="highlightedColor"/>
                </label>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gkk-KR-DBy">
                    <rect key="frame" x="68" y="2" width="45" height="21"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="21" id="neH-c8-eMe"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="17"/>
                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <nil key="highlightedColor"/>
                </label>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="0V6-mo-Dui">
                    <rect key="frame" x="16" y="13" width="44" height="80"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="80" id="6Ii-v7-b2b"/>
                        <constraint firstAttribute="width" constant="44" id="EgZ-qd-ZmY"/>
                    </constraints>
                </imageView>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="svW-gs-BFh" secondAttribute="trailing" constant="16" id="0QZ-I6-7Cd"/>
                <constraint firstAttribute="trailing" secondItem="gta-Ho-dSe" secondAttribute="trailing" constant="16" id="1rR-9V-izy"/>
                <constraint firstItem="svW-gs-BFh" firstAttribute="leading" secondItem="0V6-mo-Dui" secondAttribute="trailing" constant="8" id="WCs-ry-TXx"/>
                <constraint firstItem="gkk-KR-DBy" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="2" id="aTc-4W-XfH"/>
                <constraint firstItem="gta-Ho-dSe" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="2" id="fUf-OT-68l"/>
                <constraint firstItem="0V6-mo-Dui" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="13" id="ia3-hG-jHp"/>
                <constraint firstItem="svW-gs-BFh" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="21" id="nzM-Zi-BNn"/>
                <constraint firstItem="gkk-KR-DBy" firstAttribute="leading" secondItem="svW-gs-BFh" secondAttribute="leading" id="osj-87-VAK"/>
                <constraint firstItem="0V6-mo-Dui" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="16" id="psm-qL-ec0"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="characterCountLabel" destination="gta-Ho-dSe" id="VJ4-XD-fNP"/>
                <outlet property="flexImageView" destination="0V6-mo-Dui" id="atk-ze-Yuh"/>
                <outlet property="textView" destination="svW-gs-BFh" id="5uU-qe-orq"/>
                <outlet property="titleLabel" destination="gkk-KR-DBy" id="pMA-dc-IKX"/>
            </connections>
            <point key="canvasLocation" x="140.57971014492756" y="-155.35714285714286"/>
        </view>
    </objects>
</document>
