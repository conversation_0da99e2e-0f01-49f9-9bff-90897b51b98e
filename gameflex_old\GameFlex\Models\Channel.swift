//
//  Channel.swift
//  GameFlex
//
//  Created by <PERSON> on 10/1/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import Foundation
import SwiftyJSON

enum ChannelType: String {
    case channel, user
}

struct Channel {
    
    // mini details on User object
    var channelId: String?
    var channelName: String?
    var channelImage: String?
    var channelDescription: String?
    var flexes: [Flex]?
    var type: ChannelType?
    
    // used in parsing flexes
    static func parseTheChannel(_ json: JSON) -> Channel {
        var channel = Channel()
        channel.channelName = json["channelName"].string
        channel.channelId = json["channelId"].string
        channel.channelImage = json["channelImage"].string
        channel.channelDescription = json["channelDescription"].string
        channel.flexes = Flex.parse(json["flexes"].arrayValue)
        channel.type = .channel
        /*
        if let type = json["channelType"].string {
            channel.type = ChannelType(rawValue: type)
            switch channel.type {
            case .channel:
                channel.channelName = json["channelName"].string
                channel.channelImage = json["channelImage"].string
                channel.channelId = json["channelId"].string
            case .user:
                channel.channelName = json["flexterName"].string
                channel.channelImage = json["profileImage"].string
                channel.channelId = json["userId"].string
            case .none: break
            }
        }*/
        return channel
    }
    
    static func parseArray(_ arr: [JSON]) -> [Channel] {
        var array: [Channel] = []
        for dat in arr {
            array.append(parseTheSubChannel(dat))
        }
        return array
    }
    
    // used in parsing followings/owned/
    static func parseTheSubChannel(_ json: JSON) -> Channel {
        var channel = Channel()
        channel.channelName = json["name"].string
        channel.channelId = json["id"].string
        channel.channelImage = json["profileImage"].string
        channel.channelDescription = json["channelDescription"].string
        channel.flexes = Flex.parse(json["flexes"].arrayValue)
        channel.type = ChannelType(rawValue: json["type"].stringValue)
        
        return channel
    }
    
    static func parseFlexterAsChannel(_ json: JSON) -> Channel {
        var channel = Channel()
        channel.channelName = json["flexterName"].string
        channel.channelId = json["userId"].string
        channel.channelImage = json["profileImage"].string
        channel.type = .user
        
        return channel

    }
}

extension Channel: Comparable {
    static func < (lhs: Channel, rhs: Channel) -> Bool {
        return lhs.channelName ?? "a" < rhs.channelName ?? "b"
    }
}

extension Channel: Hashable {
    func hash(into hasher: inout Hasher) {
        hasher.combine(channelId)
        hasher.combine(channelName)
    }
}
