-- Logs setup for Supabase Analytics
-- This script sets up the logging functionality

-- <PERSON>reate analytics schema
CREATE SCHEMA IF NOT EXISTS _analytics;

-- <PERSON><PERSON> logs table
CREATE TABLE IF NOT EXISTS _analytics.logs (
  id bigserial PRIMARY KEY,
  timestamp timestamptz NOT NULL DEFAULT now(),
  level text NOT NULL,
  message text NOT NULL,
  metadata jsonb DEFAULT '{}'::jsonb,
  path text,
  method text,
  status_code integer,
  request_id text,
  user_id uuid,
  ip_address inet,
  user_agent text
);

-- <PERSON>reate indexes for better performance
CREATE INDEX IF NOT EXISTS logs_timestamp_idx ON _analytics.logs(timestamp);
CREATE INDEX IF NOT EXISTS logs_level_idx ON _analytics.logs(level);
CREATE INDEX IF NOT EXISTS logs_user_id_idx ON _analytics.logs(user_id);
CREATE INDEX IF NOT EXISTS logs_request_id_idx ON _analytics.logs(request_id);

-- Grant permissions
GRANT USAGE ON SCHEMA _analytics TO anon, authenticated, service_role;
GRANT ALL ON ALL TABLES IN SCHEMA _analytics TO anon, authenticated, service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA _analytics TO anon, authenticated, service_role;
