import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:gameflex_mobile/theme/app_theme.dart';
import 'package:gameflex_mobile/widgets/common/gf_button.dart';
import 'package:gameflex_mobile/screens/profile_screen.dart';
import 'package:gameflex_mobile/providers/posts_provider.dart';
import 'package:gameflex_mobile/widgets/post_card.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // Load posts when the home screen is first created
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final postsProvider = Provider.of<PostsProvider>(context, listen: false);
      if (postsProvider.status == PostsStatus.initial) {
        postsProvider.loadPosts();
      }
    });

    // Add scroll listener for pagination
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      final postsProvider = Provider.of<PostsProvider>(context, listen: false);
      postsProvider.loadMorePosts();
    }
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'GameFlex',
          style: TextStyle(
            color: AppColors.gfGreen,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search, color: AppColors.gfOffWhite),
            onPressed: () {},
          ),
          IconButton(
            icon: const Icon(
              Icons.notifications_none,
              color: AppColors.gfOffWhite,
            ),
            onPressed: () {},
          ),
        ],
      ),
      body: _buildBody(),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: _onItemTapped,
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
          BottomNavigationBarItem(
            icon: Icon(Icons.notifications),
            label: 'Alerts',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.add_circle_outline),
            label: 'Create',
          ),
          BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile'),
        ],
      ),
    );
  }

  Widget _buildBody() {
    switch (_selectedIndex) {
      case 0:
        return _buildHomeTab();
      case 1:
        return _buildPlaceholderTab('Alerts');
      case 2:
        return _buildPlaceholderTab('Create');
      case 3:
        return const ProfileScreen();
      default:
        return _buildHomeTab();
    }
  }

  Widget _buildHomeTab() {
    return Consumer<PostsProvider>(
      builder: (context, postsProvider, child) {
        if (postsProvider.status == PostsStatus.loading &&
            postsProvider.posts.isEmpty) {
          return const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
            ),
          );
        }

        if (postsProvider.status == PostsStatus.error &&
            postsProvider.posts.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 64,
                  color: AppColors.gfGrayText,
                ),
                const SizedBox(height: 16),
                Text(
                  'Failed to load posts',
                  style: const TextStyle(
                    color: AppColors.gfOffWhite,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  postsProvider.errorMessage ?? 'Unknown error',
                  style: const TextStyle(
                    color: AppColors.gfGrayText,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                GFButton(
                  text: 'Retry',
                  onPressed: () => postsProvider.loadPosts(),
                  type: GFButtonType.primary,
                ),
              ],
            ),
          );
        }

        if (postsProvider.posts.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.post_add, size: 64, color: AppColors.gfGrayText),
                SizedBox(height: 16),
                Text(
                  'No posts yet',
                  style: TextStyle(
                    color: AppColors.gfOffWhite,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Be the first to share something!',
                  style: TextStyle(color: AppColors.gfGrayText, fontSize: 14),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: postsProvider.refreshPosts,
          color: AppColors.gfGreen,
          backgroundColor: AppColors.gfDarkBackground,
          child: ListView.builder(
            controller: _scrollController,
            itemCount:
                postsProvider.posts.length + (postsProvider.hasMore ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == postsProvider.posts.length) {
                // Loading indicator for pagination
                return const Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppColors.gfGreen,
                      ),
                    ),
                  ),
                );
              }

              final post = postsProvider.posts[index];
              return PostCard(
                post: post,
                onTap: () {
                  // TODO: Navigate to post detail screen
                },
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildPlaceholderTab(String tabName) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            tabName,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.gfOffWhite,
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            'This is a placeholder for the tab content',
            style: TextStyle(color: AppColors.gfGrayText),
          ),
          const SizedBox(height: 24),
          GFButton(
            text: 'Go to Home',
            onPressed: () => _onItemTapped(0),
            type: GFButtonType.primary,
          ),
        ],
      ),
    );
  }
}
