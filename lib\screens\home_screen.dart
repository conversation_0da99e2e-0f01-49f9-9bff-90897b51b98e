import 'package:flutter/material.dart';
import 'package:gameflex_mobile/theme/app_theme.dart';
import 'package:gameflex_mobile/widgets/common/gf_button.dart';
import 'package:gameflex_mobile/screens/profile_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'GameFlex',
          style: TextStyle(
            color: AppColors.gfGreen,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search, color: AppColors.gfOffWhite),
            onPressed: () {},
          ),
          IconButton(
            icon: const Icon(
              Icons.notifications_none,
              color: AppColors.gfOffWhite,
            ),
            onPressed: () {},
          ),
        ],
      ),
      body: _buildBody(),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: _onItemTapped,
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
          BottomNavigationBarItem(
            icon: Icon(Icons.notifications),
            label: 'Alerts',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.add_circle_outline),
            label: 'Create',
          ),
          BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile'),
        ],
      ),
    );
  }

  Widget _buildBody() {
    switch (_selectedIndex) {
      case 0:
        return _buildHomeTab();
      case 1:
        return _buildPlaceholderTab('Alerts');
      case 2:
        return _buildPlaceholderTab('Create');
      case 3:
        return const ProfileScreen();
      default:
        return _buildHomeTab();
    }
  }

  Widget _buildHomeTab() {
    return ListView.builder(
      itemCount: 10,
      itemBuilder: (context, index) {
        return _buildFeedItem(index);
      },
    );
  }

  Widget _buildFeedItem(int index) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: AppColors.gfDarkBackground,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // User info header
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: Row(
              children: [
                CircleAvatar(
                  backgroundColor: Colors.grey[800],
                  radius: 20,
                  child: Text(
                    'U${index + 1}',
                    style: const TextStyle(
                      color: AppColors.gfGreen,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'User ${index + 1}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: AppColors.gfOffWhite,
                      ),
                    ),
                    const Text(
                      '@username',
                      style: TextStyle(
                        color: AppColors.gfGrayText,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(
                    Icons.more_vert,
                    color: AppColors.gfGrayText,
                  ),
                  onPressed: () {},
                ),
              ],
            ),
          ),
          // Post content
          Container(
            height: 250,
            width: double.infinity,
            color: Colors.grey[900],
            child: Center(
              child: Text(
                'Post Content ${index + 1}',
                style: const TextStyle(
                  color: AppColors.gfOffWhite,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          // Action buttons
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildActionButton(Icons.favorite_border, 'Like'),
                _buildActionButton(Icons.chat_bubble_outline, 'Comment'),
                _buildActionButton(Icons.share, 'Share'),
              ],
            ),
          ),
          // Caption
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 12.0,
              vertical: 4.0,
            ),
            child: Text(
              'This is a sample caption for post ${index + 1}. #gameflex #gaming',
              style: const TextStyle(color: AppColors.gfOffWhite),
            ),
          ),
          // Comments count
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 12.0,
              vertical: 8.0,
            ),
            child: Text(
              'View all ${(index + 1) * 5} comments',
              style: const TextStyle(color: AppColors.gfGrayText, fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(IconData icon, String label) {
    return InkWell(
      onTap: () {},
      child: Row(
        children: [
          Icon(icon, color: AppColors.gfGrayText, size: 20),
          const SizedBox(width: 4),
          Text(
            label,
            style: const TextStyle(color: AppColors.gfGrayText, fontSize: 12),
          ),
        ],
      ),
    );
  }

  Widget _buildPlaceholderTab(String tabName) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            tabName,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.gfOffWhite,
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            'This is a placeholder for the tab content',
            style: TextStyle(color: AppColors.gfGrayText),
          ),
          const SizedBox(height: 24),
          GFButton(
            text: 'Go to Home',
            onPressed: () => _onItemTapped(0),
            type: GFButtonType.primary,
          ),
        ],
      ),
    );
  }
}
