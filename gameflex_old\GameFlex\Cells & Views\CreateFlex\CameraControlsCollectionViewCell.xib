<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17506" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17505"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="iN0-l3-epB" customClass="CameraControlsCollectionViewCell" customModule="GameFlex" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="414" height="222"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="s9O-y4-h12">
                    <rect key="frame" x="358" y="174" width="40" height="40"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="40" id="dm3-pQ-Xir"/>
                        <constraint firstAttribute="width" constant="40" id="xgc-zQ-BVx"/>
                    </constraints>
                    <state key="normal" image="flashAuto"/>
                    <connections>
                        <action selector="didTapButtonWithSender:" destination="iN0-l3-epB" eventType="touchUpInside" id="EHl-ht-6b6"/>
                    </connections>
                </button>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" adjustsImageWhenHighlighted="NO" adjustsImageWhenDisabled="NO" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="MtD-Vv-qkJ">
                    <rect key="frame" x="16" y="174" width="40" height="40"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="40" id="AnI-r2-cgW"/>
                        <constraint firstAttribute="height" constant="40" id="rFg-mv-h6X"/>
                    </constraints>
                    <state key="normal" image="Btn_Fade"/>
                </button>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" reversesTitleShadowWhenHighlighted="YES" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="VyE-mj-Kmc">
                    <rect key="frame" x="16" y="174" width="40" height="40"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="40" id="3Y0-MI-eyY"/>
                        <constraint firstAttribute="width" constant="40" id="aCa-u9-YVQ"/>
                    </constraints>
                    <state key="normal" image="swapCamera"/>
                    <connections>
                        <action selector="didTapButtonWithSender:" destination="iN0-l3-epB" eventType="touchUpInside" id="NoK-4U-bou"/>
                    </connections>
                </button>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" adjustsImageWhenHighlighted="NO" adjustsImageWhenDisabled="NO" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="GW6-jf-PM6">
                    <rect key="frame" x="358" y="114" width="40" height="40"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="40" id="bfP-on-tND"/>
                        <constraint firstAttribute="height" constant="40" id="otP-Wp-YF5"/>
                    </constraints>
                    <state key="normal" image="Btn_Fade"/>
                </button>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" reversesTitleShadowWhenHighlighted="YES" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="y1G-kr-RJe">
                    <rect key="frame" x="364.5" y="120.5" width="27" height="27"/>
                    <state key="normal" image="timer"/>
                    <connections>
                        <action selector="didTapButtonWithSender:" destination="iN0-l3-epB" eventType="touchUpInside" id="qXc-ll-HZX"/>
                    </connections>
                </button>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" adjustsImageWhenHighlighted="NO" adjustsImageWhenDisabled="NO" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="yaw-3n-dn5">
                    <rect key="frame" x="170.5" y="122" width="73" height="73"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="73" id="b59-Sm-44A"/>
                        <constraint firstAttribute="height" constant="73" id="uj1-3q-UwZ"/>
                    </constraints>
                    <state key="normal" image="Btn_Fade"/>
                </button>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" reversesTitleShadowWhenHighlighted="YES" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="x72-kR-JPa">
                    <rect key="frame" x="182.5" y="134" width="49" height="49"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="49" id="0Oa-AJ-HOh"/>
                        <constraint firstAttribute="width" constant="49" id="m38-Jo-cXb"/>
                    </constraints>
                    <state key="normal" image="Btn_Middle"/>
                    <connections>
                        <action selector="didTapButtonWithSender:" destination="iN0-l3-epB" eventType="touchUpInside" id="5m0-xc-5su"/>
                    </connections>
                </button>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" reversesTitleShadowWhenHighlighted="YES" showsTouchWhenHighlighted="YES" adjustsImageWhenHighlighted="NO" adjustsImageWhenDisabled="NO" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="lg3-LG-vSo">
                    <rect key="frame" x="358" y="174" width="40" height="40"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="40" id="ckO-xs-pyP"/>
                        <constraint firstAttribute="height" constant="40" id="xNl-iQ-x99"/>
                    </constraints>
                    <state key="normal" image="Btn_Fade"/>
                </button>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="yaw-3n-dn5" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="0ot-Ez-Akw"/>
                <constraint firstAttribute="bottom" secondItem="lg3-LG-vSo" secondAttribute="bottom" constant="8" id="4J2-eD-t7o"/>
                <constraint firstItem="y1G-kr-RJe" firstAttribute="centerX" secondItem="GW6-jf-PM6" secondAttribute="centerX" id="4kW-og-zRH"/>
                <constraint firstAttribute="trailing" secondItem="s9O-y4-h12" secondAttribute="trailing" constant="16" id="6c9-JV-Jqy"/>
                <constraint firstItem="x72-kR-JPa" firstAttribute="centerY" secondItem="yaw-3n-dn5" secondAttribute="centerY" id="B7a-7J-YZm"/>
                <constraint firstItem="s9O-y4-h12" firstAttribute="centerY" secondItem="lg3-LG-vSo" secondAttribute="centerY" id="CYp-jH-los"/>
                <constraint firstAttribute="trailing" secondItem="lg3-LG-vSo" secondAttribute="trailing" constant="16" id="QgU-lk-Ha4"/>
                <constraint firstItem="y1G-kr-RJe" firstAttribute="centerY" secondItem="GW6-jf-PM6" secondAttribute="centerY" id="US5-vR-7KL"/>
                <constraint firstItem="x72-kR-JPa" firstAttribute="centerX" secondItem="yaw-3n-dn5" secondAttribute="centerX" id="WO2-W6-hka"/>
                <constraint firstAttribute="bottom" secondItem="MtD-Vv-qkJ" secondAttribute="bottom" constant="8" id="Wr3-sG-C1e"/>
                <constraint firstAttribute="bottom" secondItem="s9O-y4-h12" secondAttribute="bottom" constant="8" id="bou-l4-mWc"/>
                <constraint firstAttribute="trailing" secondItem="GW6-jf-PM6" secondAttribute="trailing" constant="16" id="cCb-D8-ZB8"/>
                <constraint firstItem="lg3-LG-vSo" firstAttribute="top" secondItem="GW6-jf-PM6" secondAttribute="bottom" constant="20" id="kwd-GE-tYi"/>
                <constraint firstItem="x72-kR-JPa" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="mBV-Qp-g5A"/>
                <constraint firstItem="MtD-Vv-qkJ" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="16" id="oQW-Nu-J3X"/>
                <constraint firstAttribute="bottom" secondItem="VyE-mj-Kmc" secondAttribute="bottom" constant="8" id="spq-aR-rmZ"/>
                <constraint firstAttribute="bottom" secondItem="yaw-3n-dn5" secondAttribute="bottom" constant="27" id="tJf-dL-HPG"/>
                <constraint firstItem="VyE-mj-Kmc" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="16" id="wlL-9O-mN8"/>
                <constraint firstItem="s9O-y4-h12" firstAttribute="centerX" secondItem="lg3-LG-vSo" secondAttribute="centerX" id="ysJ-B3-pKg"/>
            </constraints>
            <nil key="simulatedTopBarMetrics"/>
            <nil key="simulatedBottomBarMetrics"/>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="cameraButton" destination="x72-kR-JPa" id="CtG-IZ-q3t"/>
                <outlet property="flashButton" destination="s9O-y4-h12" id="qj7-tY-clb"/>
                <outlet property="swapCameraButton" destination="VyE-mj-Kmc" id="p6t-GC-Hxq"/>
                <outlet property="timerBackgroundButton" destination="GW6-jf-PM6" id="Ycw-3k-L0A"/>
                <outlet property="timerButton" destination="y1G-kr-RJe" id="Ax1-SD-l6b"/>
            </connections>
            <point key="canvasLocation" x="137.68115942028987" y="369.97767857142856"/>
        </view>
    </objects>
    <resources>
        <image name="Btn_Fade" width="73" height="73"/>
        <image name="Btn_Middle" width="49" height="49"/>
        <image name="flashAuto" width="22" height="28"/>
        <image name="swapCamera" width="28" height="24"/>
        <image name="timer" width="27" height="27"/>
    </resources>
</document>
