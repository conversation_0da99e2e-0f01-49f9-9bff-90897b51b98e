//
//  ImageEnhacementControlsView.swift
//  GameFlex
//
//  Created by <PERSON> on 7/14/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

class ImageEnhancementControlsView: UIStackView {
    
    @IBOutlet weak var filterButton: UIButton!
    @IBOutlet weak var filterLabel: UILabel!
    @IBOutlet weak var drawButton: UIButton!
    @IBOutlet weak var drawLabel: UILabel!
    @IBOutlet weak var flareButton: UIButton!
    @IBOutlet weak var flareLabel: UILabel!
    @IBOutlet weak var textButton: UIButton!
    @IBOutlet weak var textLabel: UILabel!
    
    weak var delegate: CameraDelegate?
        
    // MARK: - Lifecycle

    required init(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setupView()
    }

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }

    
    func setupView() {
        if filterLabel != nil {
            filterLabel.text = "filter".localized
            drawLabel.text = "draw".localized
            flareLabel.text = "flare".localized
            textLabel.text = "text".localized
        }
        if drawButton != nil, drawButton.tintColor != .gfGreen {
            if #available(iOS 13.0, *) {
                drawButton.setImage(UIImage(systemName: "hand.draw"), for: .normal)
                drawButton.tintColor = .white
                drawButton.transform = CGAffineTransform(scaleX: 1.7, y: 1.7)
            } else {
                // Fallback on earlier versions
                drawButton.setImage(UIImage(named: "draw"), for: .normal)
                drawButton.tintColor = .white
            }
        }
    }
    
    @IBAction func didTapButton(sender: UIButton) {
        let labelsArray = [filterLabel, drawLabel, flareLabel, textLabel]
        labelsArray.forEach({ $0?.textColor = .white})
        var action: CameraActionType = .filter
        switch sender {
        case filterButton:
            filterButton.setImage(#imageLiteral(resourceName: "filterSelected"), for: .normal)
            filterLabel.textColor = .gfGreen
            action = .filter
        case drawButton:
            if #available(iOS 13.0 , *) {
                drawButton.setImage(UIImage(systemName: "hand.draw.fill"), for: .normal)
                drawButton.tintColor = .gfGreen
            } else {
                drawButton.setImage(#imageLiteral(resourceName: "drawFilled"), for: .normal)
                drawButton.tintColor = .gfGreen
            }
            drawButton.transform = CGAffineTransform(scaleX: 1.7, y: 1.7)
            drawLabel.textColor = .gfGreen
            action = .draw
        case flareButton:
            flareButton.setImage(#imageLiteral(resourceName: "flareSelected"), for: .normal)
            flareLabel.textColor = .gfGreen
            action = .flare
        case textButton:
            textButton.setImage(#imageLiteral(resourceName: "textSelected"), for: .normal)
            textLabel.textColor = .gfGreen
            action = .text
        default: break
        }
        changeOtherButtons(tapped: sender)
        delegate?.didTapForCameraAction(action)
    }
    
    func changeOtherButtons(tapped: UIButton) {
        var buttonsArray: [UIButton] = [filterButton, drawButton, flareButton, textButton]
        buttonsArray.removeAll(where: { $0 == tapped })
        buttonsArray.forEach({
            if $0 == filterButton {
                filterButton.setImage(#imageLiteral(resourceName: "filter"), for: .normal)
                filterLabel.textColor = .white
            }
            if $0 == drawButton {
                if #available(iOS 13.0, *) {
                    drawButton.setImage(UIImage(systemName: "hand.draw"), for: .normal)
                    drawButton.tintColor = .white
                    drawButton.transform = CGAffineTransform(scaleX: 1.7, y: 1.7)
                } else {
                    // Fallback on earlier versions
                    drawButton.setImage(UIImage(named: "draw"), for: .normal)
                    drawButton.tintColor = .white
                }
            }
            if $0 == flareButton {
                flareButton.setImage(#imageLiteral(resourceName: "flare"), for: .normal)
            }
            if $0 == textButton {
                textButton.setImage(#imageLiteral(resourceName: "text"), for: .normal)
            }
        })
    }
}
