<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17703"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="iN0-l3-epB" customClass="CommentTableViewCell" customModule="GameFlex" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="414" height="108"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="GameFlexter" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Mzq-HE-kyv">
                    <rect key="frame" x="61" y="4" width="88" height="17"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="17" id="Ijl-1Y-DVH"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="14"/>
                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <nil key="highlightedColor"/>
                </label>
                <textView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" bounces="NO" scrollEnabled="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" bouncesZoom="NO" text="Lorem ipsum dolor sit er elit lamet, consectetaur cillium adipisicing pecu," translatesAutoresizingMaskIntoConstraints="NO" id="6Yo-0B-bS1">
                    <rect key="frame" x="56" y="22" width="342" height="59.5"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                    <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                </textView>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="43 views • Liked by KingFlex and 412 others" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumScaleFactor="0.5" translatesAutoresizingMaskIntoConstraints="NO" id="siW-YV-xnJ">
                    <rect key="frame" x="60" y="73.5" width="342" height="21"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="21" id="UDb-LJ-IJw"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ELk-0z-UaO">
                    <rect key="frame" x="367" y="2" width="31" height="21"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="21" id="YKv-XL-UIj"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" insetsLayoutMarginsFromSafeArea="NO" translatesAutoresizingMaskIntoConstraints="NO" id="GmR-sb-FjR">
                    <rect key="frame" x="16" y="5" width="40" height="40"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="40" id="Adi-gd-YaV"/>
                        <constraint firstAttribute="width" constant="40" id="G3R-bR-Nm0"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                            <integer key="value" value="20"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="clipsToBounds" value="YES"/>
                    </userDefinedRuntimeAttributes>
                </imageView>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="Mzq-HE-kyv" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="4" id="6S2-8w-PjD"/>
                <constraint firstAttribute="bottom" secondItem="siW-YV-xnJ" secondAttribute="bottom" constant="13.5" id="HjB-bY-vbq"/>
                <constraint firstItem="GmR-sb-FjR" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="16" id="NZ6-zr-JdG"/>
                <constraint firstAttribute="trailing" secondItem="6Yo-0B-bS1" secondAttribute="trailing" constant="16" id="OVT-cf-N6H"/>
                <constraint firstAttribute="trailing" secondItem="ELk-0z-UaO" secondAttribute="trailing" constant="16" id="Z37-XK-hYK"/>
                <constraint firstItem="Mzq-HE-kyv" firstAttribute="leading" secondItem="GmR-sb-FjR" secondAttribute="trailing" constant="5" id="ZPC-RN-JdK"/>
                <constraint firstItem="6Yo-0B-bS1" firstAttribute="top" secondItem="Mzq-HE-kyv" secondAttribute="bottom" constant="1" id="Zif-kv-feU"/>
                <constraint firstItem="6Yo-0B-bS1" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="56" id="hrg-7W-Gzt"/>
                <constraint firstItem="6Yo-0B-bS1" firstAttribute="bottom" secondItem="siW-YV-xnJ" secondAttribute="top" constant="8" id="ltb-wM-Yeg"/>
                <constraint firstItem="siW-YV-xnJ" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="60" id="q4H-1j-h4Q"/>
                <constraint firstItem="GmR-sb-FjR" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="5" id="uBM-DD-yH9"/>
                <constraint firstItem="ELk-0z-UaO" firstAttribute="centerY" secondItem="Mzq-HE-kyv" secondAttribute="centerY" id="vOM-YB-GyM"/>
                <constraint firstAttribute="trailing" secondItem="siW-YV-xnJ" secondAttribute="trailing" constant="12" id="zhw-Vz-h7z"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="commentTextView" destination="6Yo-0B-bS1" id="5Hl-Bd-fdd"/>
                <outlet property="dateTimeLabel" destination="ELk-0z-UaO" id="nyR-Px-RER"/>
                <outlet property="flexterName" destination="Mzq-HE-kyv" id="xlK-YM-6Ve"/>
                <outlet property="profileImageView" destination="GmR-sb-FjR" id="pjW-ZC-x5h"/>
                <outlet property="profileImageviewWidthConstraint" destination="G3R-bR-Nm0" id="bDH-gb-86B"/>
                <outlet property="statsLabel" destination="siW-YV-xnJ" id="XUq-4l-IH8"/>
            </connections>
            <point key="canvasLocation" x="227.536231884058" y="-135.26785714285714"/>
        </view>
    </objects>
</document>
